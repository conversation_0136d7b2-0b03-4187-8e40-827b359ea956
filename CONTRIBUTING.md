# Contributing to FHIR to OMOP CDM Transformation Pipeline

This document provides guidelines and instructions for contributing to the FHIR to OMOP CDM Transformation Pipeline, an internal project of AIO (Artificial Intelligence Orchestrator).

## Development Setup

1. Clone the repository
2. Create and activate the Conda environment:
   ```bash
   # Create and activate the Conda environment
   conda env create -f environment.yml
   conda activate fhir-omop
   ```
3. Copy `.env.example` to `.env` and configure as needed

## Coding Standards

For detailed coding and documentation standards, please refer to our comprehensive [standards guide](docs/guides/development/standards.md). This document includes detailed guidelines for:

- Documentation format and structure
- Python, SQL, and YAML coding styles
- File organization and naming conventions
- References and citations format
- Version control practices

### Python Coding Standards Summary

All Python code must adhere to the following standards:

1. **Style Guide**: Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) conventions.

2. **Type Hints**: Use type hints for all function parameters and return values.
   ```python
   def process_patient(patient_id: str, include_observations: bool = False) -> Dict[str, Any]:
       """Process a patient and return their data."""
       # Implementation
   ```

3. **Docstrings**: Use NumPy-style docstrings for all functions, classes, and modules.
   ```python
   def function_with_types(param1: int, param2: str) -> bool:
       """Example function with NumPy style docstrings.

       Parameters
       ----------
       param1 : int
           The first parameter.
       param2 : str
           The second parameter.

       Returns
       -------
       bool
           True if successful, False otherwise.
       """
       return True
   ```

4. **Imports**: Organize imports in the following order:
   - Standard library imports
   - Related third-party imports
   - Local application/library specific imports

## Commit Messages

Please use clear and descriptive commit messages. Follow these guidelines:

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests after the first line

## Testing

Run tests using pytest:

```bash
pytest
```

Ensure all tests pass before submitting a pull request.

## Documentation

Update documentation when making changes to the code. This includes:

- README.md
- Docstrings
- Comments in code
- Any relevant documentation files

## Proprietary Notice

By contributing to this project, you acknowledge that this is a proprietary project of AIO (Artificial Intelligence Orchestrator). All contributions become the property of AIO and are subject to internal confidentiality policies.
