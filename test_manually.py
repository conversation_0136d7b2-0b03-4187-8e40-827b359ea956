#!/usr/bin/env python
"""
Script para probar manualmente la transformación FHIR a OMOP.
Este script lee archivos FHIR locales y los transforma a formato OMOP.
"""
import os
import json
import logging
import pandas as pd
from pathlib import Path

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Rutas a los archivos de datos
SAMPLE_DATA_DIR = Path("data/sample_fhir")
INDIVIDUAL_DIR = SAMPLE_DATA_DIR / "individual"
BULK_EXPORT_DIR = SAMPLE_DATA_DIR / "bulk-export"

def load_fhir_resource_from_file(file_path):
    """Cargar un recurso FHIR desde un archivo JSON."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error al cargar el archivo {file_path}: {e}")
        return None

def load_fhir_resources_from_ndjson(file_path):
    """Cargar recursos FHIR desde un archivo NDJSON (bulk export)."""
    resources = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                if line.strip():
                    resources.append(json.loads(line))
        return resources
    except Exception as e:
        logger.error(f"Error al cargar el archivo NDJSON {file_path}: {e}")
        return []

def test_patient_mapper():
    """Probar el mapper de pacientes con datos de ejemplo."""
    try:
        # Importar el mapper (asegúrate de que la ruta de importación sea correcta)
        from src.fhir_omop.mappers.patient_mapper import map_patient_to_person, transform_patients_to_persons
        
        # Cargar un paciente de ejemplo
        patient_file = INDIVIDUAL_DIR / "patient-example_fhir.json"
        patient = load_fhir_resource_from_file(patient_file)
        
        if not patient:
            logger.error("No se pudo cargar el paciente de ejemplo")
            return
        
        # Mapear el paciente a persona
        logger.info("Mapeando paciente a persona...")
        person = map_patient_to_person(patient)
        
        if person:
            logger.info("Mapeo exitoso:")
            for key, value in person.items():
                logger.info(f"  {key}: {value}")
        else:
            logger.error("Error al mapear el paciente")
        
        # Probar con múltiples pacientes desde bulk export
        bulk_patient_file = BULK_EXPORT_DIR / "Patient.000.ndjson"
        patients = load_fhir_resources_from_ndjson(bulk_patient_file)
        
        if patients:
            logger.info(f"Cargados {len(patients)} pacientes del archivo bulk export")
            logger.info("Transformando pacientes a personas...")
            
            # Transformar los primeros 5 pacientes
            sample_patients = patients[:5]
            persons_df = transform_patients_to_persons(sample_patients)
            
            if not persons_df.empty:
                logger.info(f"Transformación exitosa. Columnas: {persons_df.columns.tolist()}")
                logger.info(f"Primeras 2 filas:\n{persons_df.head(2)}")
            else:
                logger.error("Error al transformar pacientes a personas")
        else:
            logger.error("No se pudieron cargar pacientes del archivo bulk export")
    
    except ImportError as e:
        logger.error(f"Error de importación: {e}")
        logger.error("Asegúrate de que las rutas de importación sean correctas")
    except Exception as e:
        logger.error(f"Error al probar el mapper de pacientes: {e}")

def test_encounter_mapper():
    """Probar el mapper de encuentros con datos de ejemplo."""
    try:
        # Importar el mapper
        from src.fhir_omop.mappers.encounter_mapper import transform_encounters_to_visits
        
        # Cargar encuentros desde bulk export
        bulk_encounter_file = BULK_EXPORT_DIR / "Encounter.000.ndjson"
        encounters = load_fhir_resources_from_ndjson(bulk_encounter_file)
        
        if encounters:
            logger.info(f"Cargados {len(encounters)} encuentros del archivo bulk export")
            
            # Crear un mapa de IDs de pacientes ficticio para pruebas
            # En un caso real, esto vendría de la transformación de pacientes
            person_id_map = {encounter.get("subject", {}).get("reference", "").replace("Patient/", ""): i 
                            for i, encounter in enumerate(encounters, start=1)}
            
            logger.info("Transformando encuentros a visitas...")
            
            # Transformar los primeros 5 encuentros
            sample_encounters = encounters[:5]
            visits_df = transform_encounters_to_visits(sample_encounters, person_id_map)
            
            if not visits_df.empty:
                logger.info(f"Transformación exitosa. Columnas: {visits_df.columns.tolist()}")
                logger.info(f"Primeras 2 filas:\n{visits_df.head(2)}")
            else:
                logger.error("Error al transformar encuentros a visitas")
        else:
            logger.error("No se pudieron cargar encuentros del archivo bulk export")
    
    except ImportError as e:
        logger.error(f"Error de importación: {e}")
    except Exception as e:
        logger.error(f"Error al probar el mapper de encuentros: {e}")

def main():
    """Función principal para ejecutar las pruebas manuales."""
    logger.info("Iniciando pruebas manuales de FHIR a OMOP")
    
    # Verificar que los directorios de datos existan
    if not INDIVIDUAL_DIR.exists() or not BULK_EXPORT_DIR.exists():
        logger.error(f"No se encontraron los directorios de datos: {INDIVIDUAL_DIR} o {BULK_EXPORT_DIR}")
        return
    
    # Probar los mappers
    logger.info("=== Probando Patient Mapper ===")
    test_patient_mapper()
    
    logger.info("\n=== Probando Encounter Mapper ===")
    test_encounter_mapper()
    
    logger.info("Pruebas manuales completadas")

if __name__ == "__main__":
    main()
