"""
Main ETL script for FHIR to OMOP transformation pipeline.
"""
import logging
import argparse
import pandas as pd
from utils.config import BATCH_SIZE
from utils.fhir_utils import get_all_resources, get_resources_by_patient
from mapping.patient_mapper import transform_patients_to_persons, load_persons_to_omop
from mapping.encounter_mapper import transform_encounters_to_visits, load_visits_to_omop
from mapping.condition_mapper import transform_conditions_to_condition_occurrences, load_condition_occurrences_to_omop
from mapping.observation_mapper import transform_observations, load_observations_to_omop

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("etl.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def create_id_maps(persons_df, visits_df):
    """
    Create mappings from source IDs to OMOP IDs.
    
    Args:
        persons_df (pandas.DataFrame): DataFrame of OMOP Person records
        visits_df (pandas.DataFrame): DataFrame of OMOP Visit_Occurrence records
        
    Returns:
        tuple: (person_id_map, visit_id_map)
    """
    person_id_map = {}
    visit_id_map = {}
    
    if not persons_df.empty and 'person_source_value' in persons_df.columns and 'person_id' in persons_df.columns:
        person_id_map = dict(zip(persons_df['person_source_value'], persons_df['person_id']))
    
    if not visits_df.empty and 'visit_source_value' in visits_df.columns and 'visit_occurrence_id' in visits_df.columns:
        visit_id_map = dict(zip(visits_df['visit_source_value'], visits_df['visit_occurrence_id']))
    
    return person_id_map, visit_id_map

def run_etl_pipeline(batch_size=BATCH_SIZE):
    """
    Run the complete ETL pipeline to transform FHIR data to OMOP CDM.
    
    Args:
        batch_size (int, optional): Number of resources to process per batch
        
    Returns:
        dict: Summary of records processed
    """
    logger.info("Starting FHIR to OMOP ETL pipeline")
    
    summary = {
        "patients_processed": 0,
        "persons_inserted": 0,
        "encounters_processed": 0,
        "visits_inserted": 0,
        "conditions_processed": 0,
        "condition_occurrences_inserted": 0,
        "observations_processed": 0,
        "measurements_inserted": 0,
        "observations_inserted": 0
    }
    
    try:
        # Step 1: Extract and transform Patient resources
        logger.info("Extracting Patient resources")
        patients = get_all_resources("Patient", batch_size)
        summary["patients_processed"] = len(patients)
        
        logger.info(f"Transforming {len(patients)} Patient resources to Person records")
        persons_df = transform_patients_to_persons(patients)
        
        if not persons_df.empty:
            logger.info("Loading Person records to OMOP CDM")
            summary["persons_inserted"] = load_persons_to_omop(persons_df)
        
        # Step 2: Extract and transform Encounter resources
        logger.info("Extracting Encounter resources")
        encounters = get_all_resources("Encounter", batch_size)
        summary["encounters_processed"] = len(encounters)
        
        # Create person ID map
        person_id_map, _ = create_id_maps(persons_df, pd.DataFrame())
        
        logger.info(f"Transforming {len(encounters)} Encounter resources to Visit_Occurrence records")
        visits_df = transform_encounters_to_visits(encounters, person_id_map)
        
        if not visits_df.empty:
            logger.info("Loading Visit_Occurrence records to OMOP CDM")
            summary["visits_inserted"] = load_visits_to_omop(visits_df)
        
        # Create visit ID map
        _, visit_id_map = create_id_maps(pd.DataFrame(), visits_df)
        
        # Step 3: Extract and transform Condition resources
        logger.info("Extracting Condition resources")
        conditions = get_all_resources("Condition", batch_size)
        summary["conditions_processed"] = len(conditions)
        
        logger.info(f"Transforming {len(conditions)} Condition resources to Condition_Occurrence records")
        condition_occurrences_df = transform_conditions_to_condition_occurrences(
            conditions, person_id_map, visit_id_map
        )
        
        if not condition_occurrences_df.empty:
            logger.info("Loading Condition_Occurrence records to OMOP CDM")
            summary["condition_occurrences_inserted"] = load_condition_occurrences_to_omop(condition_occurrences_df)
        
        # Step 4: Extract and transform Observation resources
        logger.info("Extracting Observation resources")
        observations = get_all_resources("Observation", batch_size)
        summary["observations_processed"] = len(observations)
        
        logger.info(f"Transforming {len(observations)} Observation resources to Measurement/Observation records")
        measurements_df, observations_df = transform_observations(
            observations, person_id_map, visit_id_map
        )
        
        if not measurements_df.empty or not observations_df.empty:
            logger.info("Loading Measurement/Observation records to OMOP CDM")
            measurements_inserted, observations_inserted = load_observations_to_omop(
                measurements_df, observations_df
            )
            summary["measurements_inserted"] = measurements_inserted
            summary["observations_inserted"] = observations_inserted
        
        logger.info("ETL pipeline completed successfully")
        logger.info(f"Summary: {summary}")
        
        return summary
    
    except Exception as e:
        logger.error(f"Error in ETL pipeline: {e}", exc_info=True)
        raise

def main():
    """
    Main entry point for the ETL script.
    """
    parser = argparse.ArgumentParser(description='FHIR to OMOP ETL Pipeline')
    parser.add_argument('--batch-size', type=int, default=BATCH_SIZE,
                        help=f'Number of resources to process per batch (default: {BATCH_SIZE})')
    args = parser.parse_args()
    
    run_etl_pipeline(args.batch_size)

if __name__ == "__main__":
    main()
