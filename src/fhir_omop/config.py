"""
Configuration module for FHIR to OMOP transformation pipeline.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file if present
load_dotenv()

# FHIR Server Configuration
FHIR_SERVER_BASE_URL = os.getenv("FHIR_SERVER_BASE_URL", "http://localhost:8080/fhir")
FHIR_SERVER_USERNAME = os.getenv("FHIR_SERVER_USERNAME", "")
FHIR_SERVER_PASSWORD = os.getenv("FHIR_SERVER_PASSWORD", "")

# OMOP Database Configuration
OMOP_DB_HOST = os.getenv("OMOP_DB_HOST", "localhost")
OMOP_DB_PORT = os.getenv("OMOP_DB_PORT", "5432")
OMOP_DB_NAME = os.getenv("OMOP_DB_NAME", "omop_cdm")
OMOP_DB_SCHEMA = os.getenv("OMOP_DB_SCHEMA", "public")
OMOP_DB_USERNAME = os.getenv("OMOP_DB_USERNAME", "postgres")
OMOP_DB_PASSWORD = os.getenv("OMOP_DB_PASSWORD", "postgres")

# Database connection string
OMOP_DB_CONNECTION_STRING = f"postgresql://{OMOP_DB_USERNAME}:{OMOP_DB_PASSWORD}@{OMOP_DB_HOST}:{OMOP_DB_PORT}/{OMOP_DB_NAME}"

# Vocabulary Configuration
VOCABULARY_PATH = os.getenv("VOCABULARY_PATH", "/home/<USER>/fhir_to_omop/sample_pipeline/data/vocabulary")

# Logging Configuration
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
LOG_FILE = os.getenv("LOG_FILE", "/home/<USER>/fhir_to_omop/sample_pipeline/etl.log")

# Batch Processing Configuration
BATCH_SIZE = int(os.getenv("BATCH_SIZE", "100"))
MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))

# Concept ID Mappings
# These would typically come from a terminology service or mapping table
# For this sample, we're hardcoding some common concept IDs
CONCEPT_IDS = {
    # Gender concept IDs
    "gender": {
        "male": 8507,        # SNOMED concept ID for Male
        "female": 8532,      # SNOMED concept ID for Female
        "unknown": 8551,     # SNOMED concept ID for Unknown
        "other": 8521        # SNOMED concept ID for Other
    },
    
    # Race concept IDs
    "race": {
        "white": 8527,       # SNOMED concept ID for White
        "black": 8516,       # SNOMED concept ID for Black
        "asian": 8515,       # SNOMED concept ID for Asian
        "native": 8657,      # SNOMED concept ID for American Indian or Alaska Native
        "other": 8522,       # SNOMED concept ID for Other Race
        "unknown": 9178      # SNOMED concept ID for Unknown racial group
    },
    
    # Ethnicity concept IDs
    "ethnicity": {
        "hispanic": 38003563,    # Hispanic
        "nonhispanic": 38003564, # Not Hispanic
        "unknown": 0             # No matching concept
    },
    
    # Visit type concept IDs
    "visit_type": {
        "inpatient": 9201,       # Inpatient Visit
        "outpatient": 9202,      # Outpatient Visit
        "emergency": 9203,       # Emergency Room Visit
        "other": 44814711        # Visit derived from EHR record
    },
    
    # Standard concept IDs for common domains
    "domain": {
        "person": 1,             # Person domain
        "visit": 8,              # Visit domain
        "condition": 19,         # Condition domain
        "observation": 27,       # Observation domain
        "measurement": 21,       # Measurement domain
        "procedure": 10,         # Procedure domain
        "drug": 13               # Drug domain
    },
    
    # Type concept IDs
    "type": {
        "primary_condition": 32020,      # EHR problem list entry
        "secondary_condition": 32022,    # EHR encounter diagnosis
        "ehr_observation": 32817,        # EHR observation
        "ehr_measurement": 32856,        # EHR measurement
        "ehr_medication": 32838,         # EHR medication list entry
        "ehr_procedure": 32866           # EHR order
    }
}

# Source vocabulary IDs
SOURCE_VOCABULARY_IDS = {
    "condition": {
        "icd10": 'ICD10',
        "icd10cm": 'ICD10CM',
        "snomed": 'SNOMED',
        "icd9": 'ICD9',
        "icd9cm": 'ICD9CM'
    },
    "observation": {
        "loinc": 'LOINC',
        "snomed": 'SNOMED'
    },
    "procedure": {
        "cpt4": 'CPT4',
        "hcpcs": 'HCPCS',
        "icd10pcs": 'ICD10PCS',
        "snomed": 'SNOMED'
    },
    "drug": {
        "rxnorm": 'RxNorm',
        "ndc": 'NDC',
        "atc": 'ATC'
    }
}
