"""
FHIR API utilities for FHIR to OMOP transformation pipeline.
"""
import logging
import requests
import json
from requests.auth import HTTP<PERSON>asic<PERSON><PERSON>
from utils.config import (
    FHIR_SERVER_BASE_URL,
    FHIR_SERVER_USERNAME,
    FHIR_SERVER_PASSWORD,
    BATCH_SIZE
)

# Set up logging
logger = logging.getLogger(__name__)

def get_auth():
    """
    Get authentication object for FHIR server if credentials are provided.
    
    Returns:
        HTTPBasicAuth or None: Authentication object or None if no credentials
    """
    if FHIR_SERVER_USERNAME and FHIR_SERVER_PASSWORD:
        return HTTPBasicAuth(FHIR_SERVER_USERNAME, FHIR_SERVER_PASSWORD)
    return None

def get_fhir_resource(resource_type, resource_id):
    """
    Retrieve a single FHIR resource by ID.
    
    Args:
        resource_type (str): FHIR resource type (e.g., 'Patient', 'Observation')
        resource_id (str): Resource ID
        
    Returns:
        dict: FHIR resource as JSON
    """
    url = f"{FHIR_SERVER_BASE_URL}/{resource_type}/{resource_id}"
    headers = {"Accept": "application/fhir+json"}
    
    try:
        response = requests.get(url, headers=headers, auth=get_auth())
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"Error retrieving {resource_type}/{resource_id}: {e}")
        raise

def search_fhir_resources(resource_type, params=None, page_size=BATCH_SIZE):
    """
    Search for FHIR resources with optional parameters.
    
    Args:
        resource_type (str): FHIR resource type (e.g., 'Patient', 'Observation')
        params (dict, optional): Search parameters
        page_size (int, optional): Number of resources per page
        
    Returns:
        list: List of FHIR resources
    """
    url = f"{FHIR_SERVER_BASE_URL}/{resource_type}"
    headers = {"Accept": "application/fhir+json"}
    
    if params is None:
        params = {}
    
    # Add pagination parameters
    params["_count"] = page_size
    
    all_resources = []
    
    try:
        # Handle pagination
        while url:
            response = requests.get(url, headers=headers, params=params, auth=get_auth())
            response.raise_for_status()
            
            bundle = response.json()
            
            # Extract resources from bundle
            if "entry" in bundle:
                resources = [entry["resource"] for entry in bundle["entry"]]
                all_resources.extend(resources)
                logger.info(f"Retrieved {len(resources)} {resource_type} resources")
            
            # Check for next page
            url = None
            if "link" in bundle:
                for link in bundle["link"]:
                    if link["relation"] == "next":
                        url = link["url"]
                        params = {}  # URL already contains parameters
                        break
            
        return all_resources
    except requests.exceptions.RequestException as e:
        logger.error(f"Error searching {resource_type}: {e}")
        raise

def get_all_resources(resource_type, page_size=BATCH_SIZE):
    """
    Retrieve all resources of a specific type.
    
    Args:
        resource_type (str): FHIR resource type (e.g., 'Patient', 'Observation')
        page_size (int, optional): Number of resources per page
        
    Returns:
        list: List of FHIR resources
    """
    return search_fhir_resources(resource_type, page_size=page_size)

def get_resources_by_patient(resource_type, patient_id, page_size=BATCH_SIZE):
    """
    Retrieve resources of a specific type for a patient.
    
    Args:
        resource_type (str): FHIR resource type (e.g., 'Observation', 'Condition')
        patient_id (str): Patient ID
        page_size (int, optional): Number of resources per page
        
    Returns:
        list: List of FHIR resources
    """
    params = {"patient": patient_id}
    return search_fhir_resources(resource_type, params, page_size)

def get_referenced_resource(reference):
    """
    Retrieve a resource by its reference.
    
    Args:
        reference (str): Resource reference (e.g., 'Patient/123')
        
    Returns:
        dict: FHIR resource as JSON
    """
    if not reference or '/' not in reference:
        logger.warning(f"Invalid reference: {reference}")
        return None
    
    # Handle absolute and relative references
    if reference.startswith('http'):
        url = reference
    else:
        # Extract resource type and ID
        parts = reference.split('/')
        if len(parts) != 2:
            logger.warning(f"Invalid reference format: {reference}")
            return None
        
        resource_type, resource_id = parts
        return get_fhir_resource(resource_type, resource_id)
    
    headers = {"Accept": "application/fhir+json"}
    
    try:
        response = requests.get(url, headers=headers, auth=get_auth())
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        logger.error(f"Error retrieving referenced resource {reference}: {e}")
        return None

def extract_extension_value(resource, url):
    """
    Extract a value from a FHIR extension by URL.
    
    Args:
        resource (dict): FHIR resource
        url (str): Extension URL
        
    Returns:
        any: Extension value or None if not found
    """
    if "extension" not in resource:
        return None
    
    for ext in resource["extension"]:
        if ext.get("url") == url:
            if "valueString" in ext:
                return ext["valueString"]
            elif "valueCode" in ext:
                return ext["valueCode"]
            elif "valueBoolean" in ext:
                return ext["valueBoolean"]
            elif "valueInteger" in ext:
                return ext["valueInteger"]
            elif "valueDecimal" in ext:
                return ext["valueDecimal"]
            elif "valueDateTime" in ext:
                return ext["valueDateTime"]
            elif "valueReference" in ext:
                return ext["valueReference"]
            # Handle nested extensions
            elif "extension" in ext:
                return ext["extension"]
    
    return None

def extract_coding(coding_list, system=None):
    """
    Extract a coding from a list of codings, optionally filtered by system.
    
    Args:
        coding_list (list): List of FHIR codings
        system (str, optional): System URL to filter by
        
    Returns:
        dict: Coding dictionary or None if not found
    """
    if not coding_list:
        return None
    
    if system:
        for coding in coding_list:
            if coding.get("system") == system:
                return coding
    
    # If no system specified or no matching system found, return first coding
    return coding_list[0]

def get_code_from_codeableconcept(codeable_concept, system=None):
    """
    Extract a code from a FHIR CodeableConcept, optionally filtered by system.
    
    Args:
        codeable_concept (dict): FHIR CodeableConcept
        system (str, optional): System URL to filter by
        
    Returns:
        str: Code value or None if not found
    """
    if not codeable_concept or "coding" not in codeable_concept:
        return None
    
    coding = extract_coding(codeable_concept["coding"], system)
    if coding and "code" in coding:
        return coding["code"]
    
    return None

def get_system_from_codeableconcept(codeable_concept):
    """
    Extract a system URL from a FHIR CodeableConcept.
    
    Args:
        codeable_concept (dict): FHIR CodeableConcept
        
    Returns:
        str: System URL or None if not found
    """
    if not codeable_concept or "coding" not in codeable_concept:
        return None
    
    coding = extract_coding(codeable_concept["coding"])
    if coding and "system" in coding:
        return coding["system"]
    
    return None

def get_display_from_codeableconcept(codeable_concept, system=None):
    """
    Extract a display text from a FHIR CodeableConcept, optionally filtered by system.
    
    Args:
        codeable_concept (dict): FHIR CodeableConcept
        system (str, optional): System URL to filter by
        
    Returns:
        str: Display text or None if not found
    """
    if not codeable_concept:
        return None
    
    # First try to get from coding
    if "coding" in codeable_concept:
        coding = extract_coding(codeable_concept["coding"], system)
        if coding and "display" in coding:
            return coding["display"]
    
    # Fall back to text
    if "text" in codeable_concept:
        return codeable_concept["text"]
    
    return None

def parse_fhir_datetime(datetime_str):
    """
    Parse a FHIR datetime string to a standardized format.
    
    Args:
        datetime_str (str): FHIR datetime string
        
    Returns:
        str: Standardized datetime string (YYYY-MM-DD HH:MM:SS)
    """
    if not datetime_str:
        return None
    
    # Handle date-only format
    if len(datetime_str) == 10 and datetime_str[4] == '-' and datetime_str[7] == '-':
        return f"{datetime_str}T00:00:00"
    
    # Handle various datetime formats
    if 'T' in datetime_str:
        # Already has time component
        date_part, time_part = datetime_str.split('T')
        
        # Handle timezone
        if 'Z' in time_part:
            time_part = time_part.replace('Z', '')
        elif '+' in time_part:
            time_part = time_part.split('+')[0]
        elif '-' in time_part and time_part.count(':') > 0:
            # Ensure we're not splitting the date
            time_parts = time_part.split('-')
            if len(time_parts) > 1 and ':' in time_parts[1]:
                time_part = time_parts[0]
        
        # Ensure time has seconds
        if time_part.count(':') == 1:
            time_part += ':00'
        
        return f"{date_part}T{time_part}"
    
    # Default case
    return f"{datetime_str}T00:00:00"

def get_resource_identifier(resource, system=None):
    """
    Extract an identifier from a FHIR resource, optionally filtered by system.
    
    Args:
        resource (dict): FHIR resource
        system (str, optional): Identifier system to filter by
        
    Returns:
        str: Identifier value or None if not found
    """
    if "identifier" not in resource:
        return None
    
    if system:
        for identifier in resource["identifier"]:
            if identifier.get("system") == system and "value" in identifier:
                return identifier["value"]
    
    # If no system specified or no matching system found, return first identifier
    if resource["identifier"] and "value" in resource["identifier"][0]:
        return resource["identifier"][0]["value"]
    
    return None
