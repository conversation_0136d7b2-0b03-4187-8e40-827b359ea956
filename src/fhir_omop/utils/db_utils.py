"""
Database utilities for FHIR to OMOP transformation pipeline.
"""
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import pandas as pd
from utils.config import OMOP_DB_CONNECTION_STRING

# Set up logging
logger = logging.getLogger(__name__)

def get_db_engine():
    """
    Create and return a SQLAlchemy database engine using the configured connection string.
    
    Returns:
        sqlalchemy.engine.Engine: Database engine object
    """
    try:
        engine = create_engine(OMOP_DB_CONNECTION_STRING)
        return engine
    except Exception as e:
        logger.error(f"Failed to create database engine: {e}")
        raise

def execute_sql(sql, params=None, return_results=False):
    """
    Execute SQL statement with optional parameters.
    
    Args:
        sql (str): SQL statement to execute
        params (dict, optional): Parameters for the SQL statement
        return_results (bool, optional): Whether to return query results
        
    Returns:
        pandas.DataFrame or None: Query results if return_results is True, otherwise None
    """
    engine = get_db_engine()
    try:
        with engine.connect() as connection:
            if return_results:
                if params:
                    result = pd.read_sql(sql, connection, params=params)
                else:
                    result = pd.read_sql(sql, connection)
                return result
            else:
                if params:
                    connection.execute(text(sql), params)
                else:
                    connection.execute(text(sql))
                connection.commit()
                return None
    except SQLAlchemyError as e:
        logger.error(f"Database error: {e}")
        raise
    except Exception as e:
        logger.error(f"Error executing SQL: {e}")
        raise

def get_next_sequence_id(sequence_name):
    """
    Get the next value from a database sequence.
    
    Args:
        sequence_name (str): Name of the sequence
        
    Returns:
        int: Next sequence value
    """
    sql = f"SELECT nextval('{sequence_name}')"
    result = execute_sql(sql, return_results=True)
    return result.iloc[0, 0]

def check_table_exists(table_name, schema='public'):
    """
    Check if a table exists in the database.
    
    Args:
        table_name (str): Name of the table to check
        schema (str, optional): Database schema name
        
    Returns:
        bool: True if table exists, False otherwise
    """
    sql = """
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = :schema
        AND table_name = :table_name
    )
    """
    params = {'schema': schema, 'table_name': table_name}
    result = execute_sql(sql, params, return_results=True)
    return result.iloc[0, 0]

def get_concept_id(concept_code, vocabulary_id, domain_id=None):
    """
    Look up a standard concept ID based on source code and vocabulary.
    
    Args:
        concept_code (str): Source concept code
        vocabulary_id (str): Vocabulary identifier (e.g., 'SNOMED', 'LOINC')
        domain_id (str, optional): Domain ID to filter by
        
    Returns:
        int: Standard concept ID if found, 0 otherwise
    """
    sql = """
    SELECT c.concept_id
    FROM concept c
    JOIN concept_relationship cr ON c.concept_id = cr.concept_id_2
    JOIN concept source ON source.concept_id = cr.concept_id_1
    WHERE source.concept_code = :concept_code
    AND source.vocabulary_id = :vocabulary_id
    AND cr.relationship_id = 'Maps to'
    AND c.standard_concept = 'S'
    """
    
    if domain_id:
        sql += " AND c.domain_id = :domain_id"
        params = {'concept_code': concept_code, 'vocabulary_id': vocabulary_id, 'domain_id': domain_id}
    else:
        params = {'concept_code': concept_code, 'vocabulary_id': vocabulary_id}
    
    result = execute_sql(sql, params, return_results=True)
    
    if result.empty:
        logger.warning(f"No standard concept found for {concept_code} in {vocabulary_id}")
        return 0
    
    return result.iloc[0, 0]

def get_source_concept_id(concept_code, vocabulary_id):
    """
    Look up a source concept ID based on source code and vocabulary.
    
    Args:
        concept_code (str): Source concept code
        vocabulary_id (str): Vocabulary identifier (e.g., 'SNOMED', 'LOINC')
        
    Returns:
        int: Source concept ID if found, 0 otherwise
    """
    sql = """
    SELECT concept_id
    FROM concept
    WHERE concept_code = :concept_code
    AND vocabulary_id = :vocabulary_id
    """
    
    params = {'concept_code': concept_code, 'vocabulary_id': vocabulary_id}
    result = execute_sql(sql, params, return_results=True)
    
    if result.empty:
        logger.warning(f"No source concept found for {concept_code} in {vocabulary_id}")
        return 0
    
    return result.iloc[0, 0]

def bulk_insert_dataframe(df, table_name, schema='public', chunk_size=1000):
    """
    Insert a pandas DataFrame into a database table in chunks.
    
    Args:
        df (pandas.DataFrame): DataFrame to insert
        table_name (str): Target table name
        schema (str, optional): Database schema name
        chunk_size (int, optional): Number of rows per chunk
        
    Returns:
        int: Number of rows inserted
    """
    engine = get_db_engine()
    try:
        total_rows = 0
        # Insert in chunks to avoid memory issues with large datasets
        for i in range(0, len(df), chunk_size):
            chunk = df.iloc[i:i+chunk_size]
            chunk.to_sql(
                table_name, 
                engine, 
                schema=schema, 
                if_exists='append', 
                index=False,
                method='multi'
            )
            total_rows += len(chunk)
            logger.info(f"Inserted chunk {i//chunk_size + 1} ({total_rows}/{len(df)} rows) into {schema}.{table_name}")
        
        return total_rows
    except SQLAlchemyError as e:
        logger.error(f"Database error during bulk insert: {e}")
        raise
    except Exception as e:
        logger.error(f"Error during bulk insert: {e}")
        raise
