"""
Utilidades para cargar recursos FHIR desde archivos locales.
Este módulo complementa fhir_utils.py para permitir pruebas sin un servidor FHIR.
"""
import os
import json
import logging
from pathlib import Path

# Set up logging
logger = logging.getLogger(__name__)

# Rutas a los archivos de datos
SAMPLE_DATA_DIR = Path("data/sample_fhir")
INDIVIDUAL_DIR = SAMPLE_DATA_DIR / "individual"
BULK_EXPORT_DIR = SAMPLE_DATA_DIR / "bulk-export"

def load_fhir_resource_from_file(file_path):
    """
    Cargar un recurso FHIR desde un archivo JSON.
    
    Args:
        file_path (str or Path): Ruta al archivo JSON
        
    Returns:
        dict: Recurso FHIR como diccionario
    """
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error al cargar el archivo {file_path}: {e}")
        return None

def load_fhir_resources_from_ndjson(file_path):
    """
    Cargar recursos FHIR desde un archivo NDJSON (bulk export).
    
    Args:
        file_path (str or Path): Ruta al archivo NDJSON
        
    Returns:
        list: Lista de recursos FHIR como diccionarios
    """
    resources = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                if line.strip():
                    resources.append(json.loads(line))
        return resources
    except Exception as e:
        logger.error(f"Error al cargar el archivo NDJSON {file_path}: {e}")
        return []

def get_all_resources_local(resource_type):
    """
    Obtener todos los recursos de un tipo específico desde archivos locales.
    
    Args:
        resource_type (str): Tipo de recurso FHIR (ej. 'Patient', 'Observation')
        
    Returns:
        list: Lista de recursos FHIR
    """
    # Buscar en el directorio de bulk export
    bulk_file = BULK_EXPORT_DIR / f"{resource_type}.000.ndjson"
    
    if bulk_file.exists():
        resources = load_fhir_resources_from_ndjson(bulk_file)
        logger.info(f"Cargados {len(resources)} recursos {resource_type} desde {bulk_file}")
        return resources
    
    # Si no hay archivo bulk, buscar ejemplos individuales
    individual_files = list(INDIVIDUAL_DIR.glob(f"*{resource_type.lower()}*.json"))
    
    if individual_files:
        resources = []
        for file_path in individual_files:
            resource = load_fhir_resource_from_file(file_path)
            if resource:
                resources.append(resource)
        
        logger.info(f"Cargados {len(resources)} recursos {resource_type} desde archivos individuales")
        return resources
    
    logger.warning(f"No se encontraron recursos {resource_type} en los archivos locales")
    return []

def get_resources_by_patient_local(resource_type, patient_id):
    """
    Obtener recursos de un tipo específico para un paciente desde archivos locales.
    
    Args:
        resource_type (str): Tipo de recurso FHIR (ej. 'Observation', 'Condition')
        patient_id (str): ID del paciente
        
    Returns:
        list: Lista de recursos FHIR
    """
    # Cargar todos los recursos del tipo
    all_resources = get_all_resources_local(resource_type)
    
    # Filtrar por paciente
    patient_resources = []
    for resource in all_resources:
        # Verificar si el recurso tiene referencia al paciente
        if "subject" in resource and "reference" in resource["subject"]:
            if resource["subject"]["reference"] == f"Patient/{patient_id}":
                patient_resources.append(resource)
        elif "patient" in resource and "reference" in resource["patient"]:
            if resource["patient"]["reference"] == f"Patient/{patient_id}":
                patient_resources.append(resource)
    
    logger.info(f"Encontrados {len(patient_resources)} recursos {resource_type} para el paciente {patient_id}")
    return patient_resources
