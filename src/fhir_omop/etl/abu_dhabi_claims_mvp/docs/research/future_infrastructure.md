# Future Infrastructure Planning

This document outlines planned infrastructure setup for scaling the Abu Dhabi Claims OMOP MVP to production environments.

## 🚧 Current Status: Planning Phase

The current MVP uses manual database setup. This document contains plans for automated infrastructure deployment in future phases.

## 📋 Planned Infrastructure Components

### Database Setup
- **PostgreSQL container** with OMOP CDM v5.4.2 schema
- **UAE-specific vocabulary** extensions (Shafafiya Dictionary)
- **Docker containerization** for consistent environments
- **Automated initialization** scripts

### OMOP Vocabulary Requirements
**Essential for 85%+ Abu Dhabi claims coverage:**
```yaml
# Required vocabularies for full integration
vocabularies:
  tier_1_critical:
    - SNOMED_CT    # Medical concepts
    - LOINC        # Laboratory codes  
    - CPT4         # Procedures (64.6% of data)
    - Gender       # Demographics
    - Race         # Demographics
    - Ethnicity    # Demographics
  
  tier_2_uae_specific:
    - RxNorm       # Drug mapping (23.1% of data)
    - ATC          # Drug classification
    - HCPCS        # Additional procedures
    - ICD10CM      # Future diagnosis support
    - NDC          # Complete drug coverage
    
  tier_3_local_extensions:
    - Shafafiya_Dictionary  # UAE service codes (35.4% unmapped)
    - DOH_Provider_Codes    # UAE licensed providers
    - UAE_Insurance_Codes   # Payer system integration
```

**Estimated Requirements:**
- **Storage**: 4-6 GB vocabulary data
- **Load Time**: 90-120 minutes initial setup
- **Coverage**: 85%+ of Abu Dhabi claims dataset

### Proposed Configuration
```yaml
# docker-compose.yml (planned)
services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: omop_cdm_abu_dhabi
      POSTGRES_USER: omop_user
      POSTGRES_PASSWORD: secure_password
    ports:
      - "5433:5432"  # Avoid conflict with FHIR server
    volumes:
      - ./ddl:/docker-entrypoint-initdb.d
```

### UAE-Specific Features (Planned)
- **Vocabulary Support**: Shafafiya Dictionary integration
- **Incomplete Data Handling**: Optimized for partial OMOP readiness
- **Local Code Support**: Tables for unmapped concepts (concept_id = 0)
- **Extensible Architecture**: Ready for incremental enhancement

## 🎯 Integration with Current MVP

### Current State
- Manual PostgreSQL setup
- Local database configuration via `config.yaml`
- Simple connection management

### Future Migration Path
1. **Phase 2**: Containerized database setup
2. **Phase 3**: UAE vocabulary integration
3. **Phase 4**: Production deployment automation

## 🔗 Related Documentation

- **Current setup**: See main `README.md` for manual database configuration
- **Research materials**: `docs/research/` contains analysis and mapping guides
- **OMOP learning**: `docs/research/learning_notes/` for educational materials

---

**Status**: Planning phase - not implemented
**Priority**: Future enhancement (post-MVP)
**Dependencies**: Successful MVP completion, UAE vocabulary access
