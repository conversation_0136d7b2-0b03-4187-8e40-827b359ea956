# Research Materials

This directory contains research materials, analysis, and documentation used during the development of the Abu Dhabi Claims to OMOP ETL pipeline.

## 📁 Directory Contents

### `learning_notes/`
Educational materials and experimental notebooks:
- `omop_learning_notebook.ipynb` - Original experimental notebook with validated transformation logic
- Source of truth for the production scripts implementation

### `eda/`
Exploratory Data Analysis materials:
- Data profiling and quality assessment
- Statistical analysis of Abu Dhabi claims data
- Variable identification and mapping analysis

### `mappings/`
UAE-specific mapping guides and documentation:
- OMOP concept mapping strategies
- UAE healthcare vocabulary analysis
- CPT code validation and filtering logic

### `official_uae_docs/`
Official UAE healthcare documentation:
- Shafafiya Dictionary references
- Healthcare coding standards
- Regulatory documentation



### `future_infrastructure.md`
Planning document for future infrastructure setup:
- Containerization plans
- UAE vocabulary integration
- Production deployment strategies

## 🎯 Purpose

These materials serve as:
- **Historical record** of development process
- **Educational resources** for understanding OMOP transformations
- **Reference materials** for future enhancements
- **Research foundation** for scaling to larger datasets

## 🔗 Relationship to Production Code

The production scripts in the parent directory were derived from the validated logic in these research materials, particularly:
- **Notebook cells 3-4** → `extract_claims.py`
- **Notebook cells 21-28** → `transform_omop.py`
- **Notebook cells 23-30** → `load_database.py`

## 📚 Usage

For developers working on:
- **Understanding the implementation**: Start with `learning_notes/omop_learning_notebook.ipynb`
- **Extending functionality**: Review `eda/` for data insights
- **UAE-specific features**: Consult `mappings/` and `official_uae_docs/`
- **Future planning**: See `future_infrastructure.md`

## 🧪 Quality Control & Testing

**Note**: Quality control functionality is integrated into the main module:
- **Unit tests**: `../../test_abu_dhabi_etl.py` (13 comprehensive tests)
- **Setup validation**: `../../verify_setup.py` (complete environment verification)
- **Pipeline validation**: Built-in checkpoints in `../../run_pipeline.py`
- **Data validation**: Expected metrics validated at each transformation step

---

**Note**: This is research/development material. For production usage, refer to the main module documentation and scripts in the parent directory.
