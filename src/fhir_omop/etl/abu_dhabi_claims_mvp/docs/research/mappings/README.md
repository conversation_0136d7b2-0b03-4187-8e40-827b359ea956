# UAE Vocabulary Mappings - Real Implementation

This directory contains the **real vocabulary mappings** for transforming **actual Abu Dhabi claims data** to OMOP standard concepts, with specific focus on **Shafafiya Dictionary integration** and **UAE healthcare coding systems**.

## 🇦🇪 **UAE Mapping Context**

Based on real analysis of 4,999 claims:
- **3,185 CPT procedures** (63.7%) - Standard international codes
- **1,154 UAE drug codes** (23.1%) - Local format requiring Shafafiya mapping
- **769 unique medical codes** total across all activity types
- **Expected 75% overall mapping success** with current resources

## 📁 **Files**

### **Completed Mappings**
- `source_to_concept_map.csv` - Master mapping file with real UAE codes
- `cpt_mappings.csv` - CPT mappings (targeting 80% success rate)
- `uae_drug_mappings.csv` - UAE drug codes → RxNorm via Shafafiya
- `unmapped_codes.csv` - Documented unmapped codes for client discussion

### **UAE-Specific Resources**
- **[UAE_VARIABLE_MAPPING_GUIDE.md](./UAE_VARIABLE_MAPPING_GUIDE.md)** - ✅ **Comprehensive UAE codes mapping guide**
- `shafafiya_integration.md` - Shafafiya Dictionary mapping methodology
- `uae_code_patterns.md` - Analysis of UAE drug code format ('B46-4387-00778-01')
- `mapping_methodology.md` - Real-world mapping approach and decisions
- `client_enhancement_requests.md` - Documented limitations for client

## 🗺️ **Real Mapping Strategy**

### **CPT Procedures (3,185 records - 63.7%)**
- **Target**: ≥80% mapping success to OMOP Procedure domain
- **Top codes identified**: 99203 (new patient visits), 97110 (therapeutic exercises)
- **Strategy**: Direct CPT4 vocabulary mapping
- **Challenge**: Some non-standard variations require investigation

### **UAE Drug Codes (1,154 records - 23.1%)**
- **Format**: 'B46-4387-00778-01' pattern (Letter-Numbers-Numbers-Numbers-Numbers)
- **Target**: ≥60% mapping success via Shafafiya Dictionary
- **Examples**: PULMICORT, ATROVENT, IMATOX respiratory medications
- **Strategy**: Shafafiya → RxNorm mapping with concept_id = 0 fallback
- **Resource**: [Shafafiya Dictionary](https://www.doh.gov.ae/en/Shafafiya/dictionary)

### **Provider and Facility Codes**
- **BDSC facility**: Burjeel Day Surgery Center mapping
- **Clinicians**: Basic provider mapping with available data
- **Challenge**: Missing specialty information for enhanced mapping

### **Missing Data Handling**
- **Demographics**: No patient age, gender, race available
- **Diagnoses**: No ICD-10 codes in current dataset
- **Strategy**: Document limitations for client enhancement requests

## 📚 **OMOP Vocabulary Requirements for Abu Dhabi Claims Integration**

### **Critical Vocabularies for Full OMOP CDM v5.4.2 Integration**

When integrating this Abu Dhabi Claims module with a complete OMOP CDM v5.4.2 database, the following vocabularies are **essential** for optimal mapping coverage:

#### **🔴 TIER 1: Currently Required (Implemented)**
```
✅ CPT4                - 64.6% of current dataset (3,185 procedures)
✅ SNOMED CT          - Medical concept standardization
✅ LOINC              - Laboratory results mapping
✅ Gender/Race/Ethnicity - Basic demographics support
```

#### **🟡 TIER 2: UAE-Specific Extensions (High Priority)**
```
⚠️  RxNorm            - For UAE drug code mapping (23.1% of dataset)
⚠️  ATC               - Therapeutic drug classification 
⚠️  HCPCS             - Healthcare Common Procedure Coding (activity_type = 4)
⚠️  ICD10CM           - Future diagnosis code support
⚠️  NDC               - National Drug Codes for complete drug mapping
```

#### **🟢 TIER 3: Complete UAE Healthcare System**
```
⏳ Shafafiya Dictionary - UAE-specific service codes (activity_type = 8)
⏳ DOH Provider Codes  - UAE Department of Health licensed providers
⏳ BDSC Facility Codes - Burjeel Day Surgery Center specific mappings
⏳ UAE Insurance Codes - Payer system integration (Daman, etc.)
```

### **Dataset Coverage Analysis**
- **Current Implementation**: 64.6% direct mapping (CPT procedures)
- **With Tier 2 Vocabularies**: ~85% expected coverage
- **With Tier 3 Extensions**: ~95% complete Abu Dhabi healthcare mapping

### **Vocabulary Size Requirements**
| Vocabulary | Estimated Size | Load Time | Priority |
|------------|---------------|-----------|----------|
| CPT4       | ~30 MB        | 2-3 min   | Critical |
| RxNorm     | ~300 MB       | 15-20 min | High     |
| SNOMED CT  | ~1.5 GB       | 45-60 min | Critical |
| LOINC      | ~200 MB       | 10-15 min | Critical |
| ATC        | ~20 MB        | 1-2 min   | High     |
| HCPCS      | ~40 MB        | 3-5 min   | Medium   |

### **Integration Strategy**
1. **Phase 1**: Load Tier 1 + Tier 2 vocabularies via [Athena](https://athena.ohdsi.org/)
2. **Phase 2**: Create local UAE vocabulary extensions for unmapped codes
3. **Phase 3**: Integrate Shafafiya Dictionary mappings
4. **Phase 4**: Validate mapping success rates against expected coverage

### **Vocabulary Bundle Recommendation**
For **complete Abu Dhabi Claims integration**, download from Athena:
```
Bundle Name: "Abu Dhabi Healthcare OMOP Bundle"
Vocabularies: SNOMED, LOINC, CPT4, RxNorm, ATC, HCPCS, ICD10CM, NDC, Gender, Race, Ethnicity
Expected Size: ~3-4 GB
Expected Load Time: 90-120 minutes
Coverage: 85%+ of Abu Dhabi claims data
```

## ✅ **Quality Criteria (Real-World)**

- **Completeness**: Target 75% overall mapping success across all domains
- **Accuracy**: Validate UAE drug mappings against Shafafiya Dictionary
- **Transparency**: Document all unmapped codes for client discussion
- **Extensibility**: Design for future Shafafiya Dictionary updates
- **Client Communication**: Prepare enhancement requests with business justification

## 🔗 **External Resources (UAE-Focused)**

### **Primary UAE Resources**
- **[Shafafiya Dictionary](https://www.doh.gov.ae/en/Shafafiya/dictionary)** - Critical UAE vocabulary source
- UAE Health Authority documentation
- BDSC facility-specific coding guidelines

### **International OMOP Resources**
- [OHDSI Athena](https://athena.ohdsi.org) - Standard vocabulary browser
- [CPT Code Lookup](https://www.aapc.com/codes/) - Procedure code validation
- [RxNorm Browser](https://mor.nlm.nih.gov/RxNav/) - Drug mapping targets

### **Research Tools**
- Medical coding references for UAE context
- Regional healthcare terminology resources
- Arabic medical terminology dictionaries

## 🚀 **Next Steps**

1. **Implement Mappings**: Use in ETL transformation logic
2. **Validate Results**: Test mapping success rates with real data
3. **Client Discussion**: Present unmapped codes and enhancement opportunities
4. **Iterate and Improve**: Refine mappings based on Shafafiya Dictionary updates

## 🔗 **Integration Points**

- **Feeds from**: `../eda/` analysis findings
- **Supports**: ETL implementation with real UAE codes
- **Prepares**: Client discussions with concrete enhancement requests
- **Enables**: Scalable vocabulary management for UAE healthcare
