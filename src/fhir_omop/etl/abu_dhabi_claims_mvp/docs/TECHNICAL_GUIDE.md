# Abu Dhabi Claims to OMOP ETL - Technical Guide

## Overview

This guide documents the technical implementation of the Abu Dhabi Claims to OMOP ETL pipeline, including design decisions, transformation logic, and the conversion process from experimental notebook to production scripts.

## Project Context

### Background
- **Source**: Abu Dhabi healthcare claims data (4,999 raw records → 3,185 CPT procedures)
- **Target**: OMOP CDM v5.4 (5 core tables)
- **Status**: Production-ready scripts based on validated notebook logic
- **Performance**: ~1.3 seconds processing time

### Validated Results
The implementation successfully transforms:
- **447 unique patients** → PERSON table
- **1,201 visits** → VISIT_OCCURRENCE table  
- **3,185 procedures** → PROCEDURE_OCCURRENCE table
- **3,185 cost records** → COST table
- **171 providers** → PROVIDER table

**Total processed value**: ~AED 520,000
**Data integrity**: 100% referential integrity maintained

## Implementation Architecture

### File Structure
```
abu_dhabi_claims_mvp/
├── extract_claims.py      # Data extraction & CPT filtering
├── transform_omop.py      # OMOP transformations (5 tables)
├── load_database.py       # PostgreSQL operations
├── run_pipeline.py        # Main orchestrator
├── config.yaml           # Configuration
├── test_abu_dhabi_etl.py  # Unit tests (13 tests)
├── quick_start.sh         # Convenience wrapper
├── verify_setup.py        # Setup validation
└── docs/
    ├── TECHNICAL_GUIDE.md # This document
    └── research/          # Research materials
```

### Data Flow
```
CSV (4,999 records)
  → Filter CPT (3,185 procedures)
  → Transform OMOP (8,189 records across 5 tables)
  → Load PostgreSQL (with validation)
```

## Core Transformation Logic

### 1. Data Extraction (`extract_claims.py`)
**Source**: Notebook cells 3-4

```python
def extract_claims_data(csv_path: str) -> pd.DataFrame:
    """Extract and filter claims data for CPT procedures only."""
    # Load raw data (4,999 records)
    # Filter for CPT procedures (3,185 records, 63.7%)
    # Validate against expected metrics
```

### 2. OMOP Transformations (`transform_omop.py`)
**Source**: Notebook cells 21-28

| Function | Logic | Input | Output |
|----------|-------|-------|--------|
| `transform_to_person()` | Deduplicate by `aio_patient_id` | Claims | 447 persons |
| `transform_to_visits()` | Group by `case` (encounter) | Claims | 1,201 visits |
| `transform_to_procedures()` | 1:1 mapping from claims | Claims | 3,185 procedures |
| `transform_to_costs()` | Link to procedures via `activity_id` | Claims | 3,185 costs |
| `transform_to_providers()` | Deduplicate by `clinician` | Claims | 171 providers |

### 3. Database Loading (`load_database.py`)
**Source**: Notebook cells 23-30

```python
def load_omop_tables(omop_data: dict, connection_string: str):
    """Load data maintaining referential integrity order."""
    # Order: person → provider → visit_occurrence → procedure_occurrence → cost
    # Includes validation and rollback on errors
```

## Key Design Decisions

### Configuration Management
- **Primary source**: `config.yaml` (database settings, file paths)
- **Override capability**: CLI arguments (`--db-name`, `--clear-existing`)
- **Conflict resolution**: Prioritizes config file over environment variables
- **Rationale**: Avoids conflicts with MCP server environment variables

### Data Handling
- **Existing data**: `--clear-existing` flag automatically clears tables
- **Error handling**: Graceful failure with detailed logging
- **Validation**: Multiple checkpoints ensure data integrity

### Testing Strategy
- **Unit tests**: 13 tests covering extraction, transformation, integration
- **Validation**: Expected metrics from notebook analysis
- **Setup verification**: Automated environment checking

## Conversion from Notebook

### Original Notebook Structure
The experimental notebook (`docs/research/learning_notes/omop_learning_notebook.ipynb`) provided the validated logic:

- **Cells 3-4**: Data loading and CPT filtering → `extract_claims.py`
- **Cells 21-28**: OMOP transformations → `transform_omop.py`
- **Cells 23-30**: Database operations → `load_database.py`
- **Complete workflow**: → `run_pipeline.py`

### Script Conversion Process
1. **Extract functions**: Convert notebook cells to reusable functions
2. **Add validation**: Include data quality checks from notebook
3. **Externalize config**: Move hardcoded values to `config.yaml`
4. **Add error handling**: Robust error management for production use
5. **Create tests**: Validate against notebook results

## Performance Characteristics

### Processing Metrics
- **Total runtime**: ~1.3 seconds
- **Memory usage**: Minimal (single DataFrame operations)
- **Database operations**: Batch inserts with transaction management
- **Scalability**: Tested up to 5K records, linear scaling expected

### Validation Checkpoints
1. **Input validation**: File existence, column presence
2. **Extraction validation**: Record counts, data types
3. **Transformation validation**: OMOP table volumes
4. **Loading validation**: Database integrity checks
5. **End-to-end validation**: Total record reconciliation

## Future Enhancements

### Immediate Opportunities
- **Larger datasets**: Scale testing beyond 5K records
- **Additional OMOP domains**: Drug exposure, conditions, observations
- **Vocabulary mapping**: Integration with OMOP standard vocabularies

### Infrastructure Evolution
- **Containerization**: Docker setup for consistent environments
- **UAE vocabulary**: Shafafiya Dictionary integration
- **Production deployment**: Automated infrastructure setup

## Troubleshooting Guide

### Common Issues
| Problem | Cause | Solution |
|---------|-------|----------|
| Duplicate key errors | Existing data | Use `--clear-existing` flag |
| Wrong record counts | Data file changes | Verify input file integrity |
| Database connection | Config mismatch | Run `python load_database.py --dry-run` |

### Debugging Commands
```bash
# Verify setup
./quick_start.sh verify

# Test individual components
python extract_claims.py --validate-only
python load_database.py --dry-run
python run_pipeline.py --validate-only

# Run with detailed logging
python run_pipeline.py --clear-existing
```

## Reference Documentation

### Primary Sources
- **Source notebook**: `docs/research/learning_notes/omop_learning_notebook.ipynb`
- **OMOP CDM v5.4.2**: https://github.com/OHDSI/CommonDataModel/tree/v5.4.2
- **Project standards**: `../../docs/guides/development/standards.md`

### Research Materials
- **EDA analysis**: `docs/research/eda/`
- **UAE mappings**: `docs/research/mappings/`
- **Official docs**: `docs/research/official_uae_docs/`

---

**Status**: ✅ Production-ready implementation
**Created**: December 2024
**Last updated**: December 2024
**Team**: FHIR-OMOP Development Team
