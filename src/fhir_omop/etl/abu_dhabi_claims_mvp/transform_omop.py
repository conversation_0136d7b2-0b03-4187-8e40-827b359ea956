"""
Abu Dhabi Claims to OMOP Transformation Module

This module transforms Abu Dhabi claims data into OMOP CDM format.
Based on validated logic from learning_notes/omop_learning_notebook.ipynb.

Author: FHIR-OMOP Development Team
Created: December 2024
License: MIT
"""

import logging
from typing import List, Dict, Any

import pandas as pd

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def transform_to_person(claims_df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Generate PERSON table records from unique patients.
    
    Based on notebook validation: 447 unique persons from claims data.
    Implements exact logic from notebook create_person_records function.
    
    Parameters
    ----------
    claims_df : pd.DataFrame
        Claims data with patient information
        
    Returns
    -------
    List[Dict[str, Any]]
        List of person records for OMOP PERSON table
    """
    logger.info("Transforming to PERSON table records")
    
    # Get unique patients - based on notebook logic
    unique_patients = claims_df['aio_patient_id'].unique()
    
    person_records = []
    for patient_id in unique_patients:
        person_record = {
            'person_id': patient_id,  # aio_patient_id
            'gender_concept_id': 0,  # Unknown (not available in dataset)
            'year_of_birth': None,   # Not available in dataset
            'birth_datetime': None,  # Not available in dataset
            'race_concept_id': 0,    # Unknown (not available in dataset)
            'ethnicity_concept_id': 0,  # Unknown (not available in dataset)
            'person_source_value': patient_id  # Original patient identifier
        }
        person_records.append(person_record)
    
    logger.info(f"Created {len(person_records):,} person records")
    return person_records


def transform_to_visits(claims_df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Generate VISIT_OCCURRENCE table records from unique encounters.
    
    Based on notebook validation: 1,201 visits by aggregating claims.
    Implements exact logic from notebook create_visit_records function.
    
    Parameters
    ----------
    claims_df : pd.DataFrame
        Claims data with visit information
        
    Returns
    -------
    List[Dict[str, Any]]
        List of visit records for OMOP VISIT_OCCURRENCE table
    """
    logger.info("Transforming to VISIT_OCCURRENCE table records")
    
    # Get unique visits (one record per case) with additional fields
    visit_data = claims_df.groupby('case').agg({
        'aio_patient_id': 'first',
        'encounter_start_date': 'first',
        'encounter_end_date': 'first',
        'case_type': 'first',
        'provider_id': 'first',  # MEDIUM priority
        'receiver_id': 'first',  # Care site (facility)
        'encounter_start_type_desc': 'first'  # Admitting source
    }).reset_index()
    
    visit_records = []
    for _, row in visit_data.iterrows():
        visit_record = {
            'visit_occurrence_id': int(row['case']),  # case - Convert to int
            'person_id': row['aio_patient_id'],  # aio_patient_id
            'visit_concept_id': 9202,  # 9202 = Outpatient Visit
            'visit_start_date': row['encounter_start_date'].date(),  # encounter_start_date
            'visit_end_date': row['encounter_end_date'].date(),  # encounter_end_date
            'visit_type_concept_id': 44818517,  # EHR encounter record
            'provider_id': row['provider_id'],  # provider_id (MEDIUM priority)
            'care_site_id': row['receiver_id'],  # receiver_id (facility)
            'visit_source_value': row['case_type'],  # case_type
            'visit_source_concept_id': 0,
            'admitting_source_value': row['encounter_start_type_desc']  # encounter_start_type_desc
        }
        visit_records.append(visit_record)
    
    logger.info(f"Created {len(visit_records):,} visit records")
    return visit_records


def transform_to_procedures(claims_df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Generate PROCEDURE_OCCURRENCE table records from CPT procedures.
    
    Based on notebook validation: 3,185 procedures (1:1 mapping).
    Implements exact logic from notebook create_procedure_records function.
    
    Parameters
    ----------
    claims_df : pd.DataFrame
        Claims data with procedure information
        
    Returns
    -------
    List[Dict[str, Any]]
        List of procedure records for OMOP PROCEDURE_OCCURRENCE table
    """
    logger.info("Transforming to PROCEDURE_OCCURRENCE table records")
    
    procedure_records = []
    
    for _, row in claims_df.iterrows():
        # Convert start_activity_date if available, otherwise use encounter_start_date
        procedure_date = row['start_activity_date'] if pd.notna(row['start_activity_date']) else row['encounter_start_date']
        if isinstance(procedure_date, str):
            procedure_date = pd.to_datetime(procedure_date, format='%d/%m/%Y')
        
        procedure_record = {
            'procedure_occurrence_id': int(row['activity_id']),  # activity_id - Convert to int
            'person_id': row['aio_patient_id'],  # aio_patient_id
            'visit_occurrence_id': int(row['case']),  # case - Convert to int
            'procedure_concept_id': 0,  # Will map CPT codes later
            'procedure_date': procedure_date.date(),  # start_activity_date or encounter_start_date
            'procedure_datetime': procedure_date,  # start_activity_date with time
            'procedure_type_concept_id': 38000275,  # EHR order list
            'modifier_concept_id': 0,  # reference_activity (could be mapped)
            'quantity': float(row['activity_quantity']) if pd.notna(row['activity_quantity']) else 1.0,  # activity_quantity
            'provider_id': row['clinician'],  # clinician
            'procedure_source_value': row['code_activity'],  # code_activity (CPT code)
            'procedure_source_concept_id': 0,
            'modifier_source_value': row['act_type_desc']  # act_type_desc
        }
        procedure_records.append(procedure_record)
    
    logger.info(f"Created {len(procedure_records):,} procedure records")
    return procedure_records


def transform_to_costs(claims_df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Generate COST table records from financial data.
    
    Based on notebook validation: 3,185 cost records.
    Implements exact logic from notebook create_cost_records function.
    
    Parameters
    ----------
    claims_df : pd.DataFrame
        Claims data with financial information
        
    Returns
    -------
    List[Dict[str, Any]]
        List of cost records for OMOP COST table
    """
    logger.info("Transforming to COST table records")
    
    cost_records = []
    
    for _, row in claims_df.iterrows():
        cost_record = {
            'cost_id': int(row['activity_id']),  # activity_id - Convert to int
            'cost_event_id': int(row['activity_id']),  # activity_id (same as cost_id) - Convert to int
            'cost_domain_id': 'Procedure',  # Domain of the cost event
            'cost_type_concept_id': 5032,  # 5032 = Claim
            'currency_concept_id': 44818568,  # AED currency
            'total_charge': float(row['gross']),  # gross (total charges)
            'total_cost': float(row['net']),  # net (net charges to payer)
            'total_paid': float(row['payment_amount']),  # payment_amount (actual payment)
            'paid_by_payer': float(row['net']) - float(row['patient_share']),  # net - patient_share
            'paid_by_patient': float(row['patient_share']),  # patient_share
            'paid_patient_copay': float(row['patient_share']),  # patient_share (copay)
            'paid_patient_coinsurance': 0.0,  # Not available in dataset
            'paid_patient_deductible': 0.0,  # Not available in dataset
            'paid_by_primary': float(row['payment_amount']),  # payment_amount
            'paid_ingredient_cost': 0.0,  # Not applicable for procedures
            'paid_dispensing_fee': 0.0,  # Not applicable for procedures
            'payer_plan_period_id': row['insurance_plan_id'],  # insurance_plan_id
            'amount_allowed': float(row['net']),  # net (allowed amount)
            'revenue_code_concept_id': 0,  # Could map from activity type
            'revenue_code_source_value': str(row['type_activity'])  # type_activity
        }
        cost_records.append(cost_record)
    
    logger.info(f"Created {len(cost_records):,} cost records")
    return cost_records


def transform_to_providers(claims_df: pd.DataFrame) -> List[Dict[str, Any]]:
    """Generate PROVIDER table records from unique clinicians.
    
    Based on notebook validation: 171 unique providers.
    Implements exact logic from notebook create_provider_records function.
    
    Parameters
    ----------
    claims_df : pd.DataFrame
        Claims data with provider information
        
    Returns
    -------
    List[Dict[str, Any]]
        List of provider records for OMOP PROVIDER table
    """
    logger.info("Transforming to PROVIDER table records")
    
    # Get unique providers (one record per clinician)
    provider_data = claims_df.groupby('clinician').agg({
        'clinician_name': 'first',
        'institution_name': 'first'
    }).reset_index()
    
    provider_records = []
    for _, row in provider_data.iterrows():
        provider_record = {
            'provider_id': row['clinician'],  # clinician
            'provider_name': row['clinician_name'],  # clinician_name
            'npi': None,  # Not available in dataset
            'dea': None,  # Not available in dataset
            'specialty_concept_id': 0,  # Not available in dataset
            'care_site_id': row['institution_name'],  # institution_name
            'year_of_birth': None,  # Not available in dataset
            'gender_concept_id': 0,  # Not available in dataset
            'provider_source_value': row['clinician'],  # clinician (original ID)
            'specialty_source_value': None,  # Not available in dataset
            'specialty_source_concept_id': 0,
            'gender_source_value': None,  # Not available in dataset
            'gender_source_concept_id': 0
        }
        provider_records.append(provider_record)
    
    logger.info(f"Created {len(provider_records):,} provider records")
    return provider_records


def transform_all_tables(claims_df: pd.DataFrame) -> Dict[str, List[Dict[str, Any]]]:
    """Transform claims data to all OMOP tables.
    
    Orchestrates transformation to all 5 OMOP tables.
    Based on notebook validation with exact record counts.
    
    Parameters
    ----------
    claims_df : pd.DataFrame
        Claims data to transform
        
    Returns
    -------
    Dict[str, List[Dict[str, Any]]]
        Dictionary containing all transformed OMOP table data
    """
    logger.info("Starting transformation to all OMOP tables")
    
    # Transform to all OMOP tables
    omop_data = {
        'person': transform_to_person(claims_df),
        'visit_occurrence': transform_to_visits(claims_df),
        'procedure_occurrence': transform_to_procedures(claims_df),
        'cost': transform_to_costs(claims_df),
        'provider': transform_to_providers(claims_df)
    }
    
    # Log summary
    total_records = sum(len(records) for records in omop_data.values())
    logger.info(f"Transformation complete - {total_records:,} total OMOP records created")
    
    for table_name, records in omop_data.items():
        logger.info(f"  {table_name}: {len(records):,} records")
    
    return omop_data
