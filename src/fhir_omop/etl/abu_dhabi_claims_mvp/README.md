# Abu Dhabi Claims to OMOP ETL - MVP

Complete ETL pipeline transforming Abu Dhabi healthcare claims into OMOP Common Data Model format. Production-ready scripts based on validated notebook analysis.

**Status**: ✅ Production-ready | **Processing**: 8,189 records in ~1.3s | **Tests**: 13/13 passing

## 🚀 Quick Start

```bash
# 1. Verify setup
./quick_start.sh verify

# 2. Run complete pipeline
./quick_start.sh clean-run

# 3. Expected: 8,189 OMOP records loaded successfully
```

**Results**: 447 patients → 1,201 visits → 3,185 procedures → 171 providers → ~AED 520K processed

## Setup

### Prerequisites
- **Conda environment**: `fhir-omop` activated
- **PostgreSQL**: Running locally with OMOP database
- **Location**: `/fhir-omop/src/fhir_omop/etl/abu_dhabi_claims_mvp/`

### Installation
```bash
# Navigate to module directory
cd src/fhir_omop/etl/abu_dhabi_claims_mvp/

# Make scripts executable
chmod +x quick_start.sh verify_setup.py

# Verify everything works
./quick_start.sh verify
```

## Usage

### Quick Commands
```bash
./quick_start.sh verify      # Verify setup
./quick_start.sh test        # Run unit tests
./quick_start.sh validate    # Test data extraction
./quick_start.sh transform   # Test transformation
./quick_start.sh clean-run   # Full pipeline (recommended)
./quick_start.sh help        # Show all options
```

### Step-by-Step Execution
```bash
# 1. Validate data extraction
python run_pipeline.py --validate-only
# → 3,185 CPT procedures extracted

# 2. Test transformation only
python run_pipeline.py --skip-load
# → 8,189 OMOP records created

# 3. Complete pipeline
python run_pipeline.py --clear-existing
# → Data cleared + 8,189 records loaded
```

### Advanced Options
```bash
# Custom input file
python run_pipeline.py --input-file /path/to/data.csv

# Different environment
python run_pipeline.py --environment testing

# Database connection test
python load_database.py --dry-run
```

## Architecture

### File Structure
```
abu_dhabi_claims_mvp/
├── extract_claims.py      # Data extraction & filtering
├── transform_omop.py      # OMOP transformations (5 tables)
├── load_database.py       # PostgreSQL operations
├── run_pipeline.py        # Main orchestrator
├── config.yaml           # Configuration
├── quick_start.sh         # Convenience wrapper
├── verify_setup.py        # Setup validation
├── test_abu_dhabi_etl.py  # Unit tests (13 tests)
└── docs/                  # Documentation & research
    ├── TECHNICAL_GUIDE.md # Implementation details
    └── research/          # Research materials & notebooks
```

### Data Flow
```
CSV (4,999 records)
  → Filter CPT (3,185 procedures)
  → Transform OMOP (8,189 records across 5 tables)
  → Load PostgreSQL (with validation)
```

### Configuration
- **Primary**: `config.yaml` (database settings, file paths)
- **Override**: CLI arguments (`--db-name`, `--clear-existing`)
- **Priority**: CLI > config.yaml > defaults

## Technical Details

### OMOP Transformation Logic
| Source | Target | Logic | Count |
|--------|--------|-------|-------|
| `aio_patient_id` | PERSON | Deduplicate patients | 447 |
| `case` | VISIT_OCCURRENCE | Group by encounter | 1,201 |
| Claims | PROCEDURE_OCCURRENCE | 1:1 mapping | 3,185 |
| `activity_id` | COST | Link to procedures | 3,185 |
| `clinician` | PROVIDER | Deduplicate providers | 171 |

### Technology Stack
- **Python 3.11** + conda environment
- **Dependencies**: pandas, SQLAlchemy, psycopg2-binary, PyYAML
- **Database**: PostgreSQL with OMOP CDM v5.4 schema
- **Processing**: ~1.3 seconds for complete pipeline
- **Validation**: 100% referential integrity maintained
## Testing

### Quick Test Commands
```bash
./quick_start.sh test        # Run all unit tests
./quick_start.sh verify      # Full setup verification
python -m pytest test_abu_dhabi_etl.py -v  # Detailed test output
```

### Test Coverage (13 tests)
- **Data extraction**: Raw loading, CPT filtering, validation
- **OMOP transformation**: All 5 table transformations
- **Data quality**: Required fields, referential integrity
- **Integration**: Complete pipeline validation

**Expected Results**: All tests pass with validated metrics (447 patients, 1,201 visits, 3,185 procedures)

## Troubleshooting

### Common Issues

| Problem | Solution | Command |
|---------|----------|---------|
| Database connection fails | Test connection | `python load_database.py --dry-run` |
| Duplicate key errors | Clear existing data | `./quick_start.sh clean-run` |
| Wrong data counts | Verify input file | `python extract_claims.py --validate-only` |
| Setup issues | Run verification | `./quick_start.sh verify` |

### Configuration Conflicts
**Issue**: MCP server environment variables may conflict with ETL settings

**Solution**: This module prioritizes `config.yaml` over environment variables
```bash
# Edit config.yaml for permanent changes
# Use CLI args for temporary overrides: --db-name custom_db
```

### OMOP Vocabulary Requirements

**For complete Abu Dhabi Claims integration with OMOP CDM v5.4:**

**Currently Supported (64.6% coverage):**
- ✅ CPT4 procedures directly mapped
- ✅ Basic OMOP structure implemented

**Required for Full Integration (85%+ coverage):**
```bash
# Essential vocabularies needed in target OMOP database:
- SNOMED CT      # Medical concept standardization
- LOINC          # Laboratory results  
- RxNorm         # UAE drug mapping (23.1% of data)
- ATC            # Drug classification
- HCPCS          # Additional procedures
- ICD10CM        # Future diagnosis support
```

**See detailed vocabulary requirements:** `docs/research/mappings/README.md#omop-vocabulary-requirements`

### Expected Metrics
- **Input**: 4,999 raw records → 3,185 CPT procedures (63.7%)
- **Output**: 8,189 OMOP records across 5 tables
- **Financial**: ~AED 520,000 total value processed

## Reference

### Documentation
- **Technical guide**: `docs/TECHNICAL_GUIDE.md` (implementation details)
- **Source notebook**: `docs/research/learning_notes/omop_learning_notebook.ipynb` (validated logic)
- **OMOP CDM v5.4.2**: https://github.com/OHDSI/CommonDataModel/tree/v5.4.2
- **Project standards**: `../../docs/guides/development/standards.md`

### Implementation Notes
Scripts converted from validated notebook cells:
- `extract_claims.py` ← Cells 3-4 (data loading, filtering)
- `transform_omop.py` ← Cells 21-28 (OMOP transformations)
- `load_database.py` ← Cells 23-30 (database operations)
- `run_pipeline.py` ← Complete workflow orchestration

**Status**: ✅ Production-ready | **Created**: December 2024 | **Team**: FHIR-OMOP Development



