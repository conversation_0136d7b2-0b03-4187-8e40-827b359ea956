"""
Abu Dhabi Claims OMOP Database Loading Module

This module loads transformed OMOP data into PostgreSQL database.
Based on validated logic from learning_notes/omop_learning_notebook.ipynb.

Author: FHIR-OMOP Development Team
Created: December 2024
License: MIT
"""

import argparse
import logging
from typing import Dict, List, Any, Optional
from contextlib import contextmanager

import pandas as pd
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError

# Import YAML for simple config loading
import yaml

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config_defaults(config_file: str = "config.yaml") -> dict:
    """Load default values from config.yaml file.

    Simple function to read config.yaml and extract database settings.
    This prioritizes config file over environment variables.

    Parameters
    ----------
    config_file : str
        Path to configuration file

    Returns
    -------
    dict
        Database configuration from config file
    """
    try:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        return config.get('database', {})
    except FileNotFoundError:
        logger.warning(f"Config file {config_file} not found, using defaults")
        return {}
    except Exception as e:
        logger.warning(f"Error reading config file: {e}")
        return {}


def parse_args():
    """Parse command line arguments.

    Simple approach: CLI args > config.yaml > environment variables > defaults

    Returns
    -------
    argparse.Namespace
        Parsed command line arguments
    """
    # Load config file defaults first
    config_defaults = load_config_defaults()

    parser = argparse.ArgumentParser(
        description="Load OMOP data into PostgreSQL database"
    )

    # Database connection arguments with config.yaml as defaults
    parser.add_argument(
        "--db-host",
        default=config_defaults.get('host', 'localhost'),
        help="Database host"
    )
    parser.add_argument(
        "--db-port",
        default=config_defaults.get('port', 5432),
        help="Database port"
    )
    parser.add_argument(
        "--db-name",
        default=config_defaults.get('database', 'omop_abu_dhabi'),
        help="Database name"
    )
    parser.add_argument(
        "--db-user",
        default=config_defaults.get('user', 'jaimepm'),
        help="Database user"
    )
    parser.add_argument(
        "--db-password",
        default=config_defaults.get('password', ''),
        help="Database password"
    )
    parser.add_argument(
        "--schema",
        default=config_defaults.get('schema', 'public'),
        help="Database schema"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Validate connection without loading data"
    )

    return parser.parse_args()


def create_connection_string(host: str, port: str, database: str, 
                           user: str, password: str) -> str:
    """Create PostgreSQL connection string.
    
    Parameters
    ----------
    host : str
        Database host
    port : str
        Database port
    database : str
        Database name
    user : str
        Database user
    password : str
        Database password
        
    Returns
    -------
    str
        PostgreSQL connection string
    """
    if password:
        return f"postgresql://{user}:{password}@{host}:{port}/{database}"
    else:
        return f"postgresql://{user}@{host}:{port}/{database}"


@contextmanager
def get_database_session(connection_string: str):
    """Get database session with proper error handling.
    
    Parameters
    ----------
    connection_string : str
        Database connection string
        
    Yields
    ------
    sqlalchemy.orm.Session
        Database session
    """
    engine = None
    session = None
    
    try:
        # Create engine
        engine = create_engine(connection_string)
        
        # Test connection
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        
        # Create session
        Session = sessionmaker(bind=engine)
        session = Session()
        
        logger.info("Database connection established")
        yield session
        
    except SQLAlchemyError as e:
        logger.error(f"Database connection error: {e}")
        raise
    finally:
        if session:
            session.close()
        if engine:
            engine.dispose()


def validate_database_schema(session, schema: str = "public") -> bool:
    """Validate that required OMOP tables exist.
    
    Parameters
    ----------
    session : sqlalchemy.orm.Session
        Database session
    schema : str, optional
        Database schema name, by default "public"
        
    Returns
    -------
    bool
        True if all required tables exist
    """
    logger.info("Validating database schema")
    
    required_tables = [
        'person',
        'visit_occurrence', 
        'procedure_occurrence',
        'cost',
        'provider'
    ]
    
    missing_tables = []
    
    for table in required_tables:
        try:
            result = session.execute(text(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = '{schema}' 
                    AND table_name = '{table}'
                );
            """))
            
            table_exists = result.scalar()
            
            if not table_exists:
                missing_tables.append(table)
            else:
                logger.info(f"✅ Table '{table}' exists")
                
        except SQLAlchemyError as e:
            logger.error(f"Error checking table '{table}': {e}")
            missing_tables.append(table)
    
    if missing_tables:
        logger.error(f"Missing required tables: {missing_tables}")
        return False
    
    logger.info("All required OMOP tables exist")
    return True


def load_table_data(session, table_name: str, records: List[Dict[str, Any]], 
                   schema: str = "public") -> int:
    """Load records into a specific OMOP table.
    
    Parameters
    ----------
    session : sqlalchemy.orm.Session
        Database session
    table_name : str
        Name of the table to load data into
    records : List[Dict[str, Any]]
        List of records to insert
    schema : str, optional
        Database schema name, by default "public"
        
    Returns
    -------
    int
        Number of records loaded
    """
    if not records:
        logger.warning(f"No records to load for table '{table_name}'")
        return 0
    
    logger.info(f"Loading {len(records):,} records into '{table_name}' table")
    
    try:
        # Convert records to DataFrame for bulk insert
        df = pd.DataFrame(records)
        
        # Load data using pandas to_sql for efficiency
        df.to_sql(
            name=table_name,
            con=session.bind,
            schema=schema,
            if_exists='append',
            index=False,
            method='multi'
        )
        
        session.commit()
        logger.info(f"✅ Successfully loaded {len(records):,} records into '{table_name}'")
        return len(records)
        
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"Error loading data into '{table_name}': {e}")
        raise
    except Exception as e:
        session.rollback()
        logger.error(f"Unexpected error loading '{table_name}': {e}")
        raise


def load_omop_tables(omop_data: Dict[str, List[Dict[str, Any]]], 
                    connection_string: str, schema: str = "public") -> Dict[str, int]:
    """Load transformed data to OMOP database.
    
    Maintains referential integrity from notebook validation.
    Implements exact logic from notebook database loading cells.
    
    Parameters
    ----------
    omop_data : Dict[str, List[Dict[str, Any]]]
        Dictionary containing all OMOP table data
    connection_string : str
        Database connection string
    schema : str, optional
        Database schema name, by default "public"
        
    Returns
    -------
    Dict[str, int]
        Dictionary with table names and number of records loaded
    """
    logger.info("Starting OMOP data loading to database")
    
    # Define loading order to maintain referential integrity
    # Based on notebook loading sequence
    loading_order = [
        'person',           # No dependencies
        'provider',         # No dependencies  
        'visit_occurrence', # Depends on person
        'procedure_occurrence', # Depends on person, visit_occurrence, provider
        'cost'             # Depends on procedure_occurrence
    ]
    
    load_results = {}
    
    with get_database_session(connection_string) as session:
        # Validate schema exists
        if not validate_database_schema(session, schema):
            raise ValueError("Required OMOP tables not found in database")
        
        # Load tables in order
        for table_name in loading_order:
            if table_name in omop_data:
                records_loaded = load_table_data(
                    session, table_name, omop_data[table_name], schema
                )
                load_results[table_name] = records_loaded
            else:
                logger.warning(f"No data provided for table '{table_name}'")
                load_results[table_name] = 0
    
    # Log summary
    total_loaded = sum(load_results.values())
    logger.info(f"Data loading complete - {total_loaded:,} total records loaded")
    
    for table_name, count in load_results.items():
        logger.info(f"  {table_name}: {count:,} records")
    
    return load_results


def validate_loaded_data(connection_string: str, schema: str = "public") -> Dict[str, int]:
    """Validate data was loaded correctly by counting records.
    
    Parameters
    ----------
    connection_string : str
        Database connection string
    schema : str, optional
        Database schema name, by default "public"
        
    Returns
    -------
    Dict[str, int]
        Dictionary with table names and record counts
    """
    logger.info("Validating loaded data")
    
    table_counts = {}
    
    with get_database_session(connection_string) as session:
        tables = ['person', 'visit_occurrence', 'procedure_occurrence', 'cost', 'provider']
        
        for table in tables:
            try:
                result = session.execute(text(f"SELECT COUNT(*) FROM {schema}.{table}"))
                count = result.scalar()
                table_counts[table] = count
                logger.info(f"  {table}: {count:,} records")
            except SQLAlchemyError as e:
                logger.error(f"Error counting records in '{table}': {e}")
                table_counts[table] = -1
    
    return table_counts


def main():
    """Main execution function.

    Simple approach: prioritize config.yaml over environment variables.
    """
    args = parse_args()

    try:
        # Create connection string using parsed args (which prioritize config.yaml)
        connection_string = create_connection_string(
            args.db_host, str(args.db_port), args.db_name,
            args.db_user, args.db_password
        )

        # Log current configuration for transparency
        logger.info("🔧 Using database configuration:")
        logger.info(f"  host: {args.db_host}")
        logger.info(f"  port: {args.db_port}")
        logger.info(f"  database: {args.db_name}")
        logger.info(f"  user: {args.db_user}")
        logger.info(f"  schema: {args.schema}")

        if args.dry_run:
            # Test connection only
            logger.info("🧪 Performing dry run - testing database connection...")
            with get_database_session(connection_string) as session:
                if validate_database_schema(session, args.schema):
                    logger.info("✅ Database connection and schema validation successful")
                    logger.info("💡 Ready to load OMOP data with these settings")
                else:
                    logger.error("❌ Database schema validation failed")
            return

        # Note: In actual usage, this would receive omop_data from transform_omop.py
        logger.info("📦 Database loading module ready")
        logger.info("💡 Use load_omop_tables() function to load transformed data")
        logger.info("💡 Or run the complete pipeline with: python run_pipeline.py")

    except Exception as e:
        logger.error(f"❌ Error during database operations: {e}")
        raise


if __name__ == "__main__":
    main()
