"""
Unit Tests for Abu Dhabi Claims to OMOP ETL Pipeline

Tests based on validated metrics from notebook and EDA documentation.
Ensures exact reproduction of expected results.

Author: FHIR-OMOP Development Team
Created: December 2024
License: MIT
"""

import unittest
import os
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock

import pandas as pd
import numpy as np

# Add current directory to path for local imports
sys.path.append(str(Path(__file__).parent))

from extract_claims import extract_claims_data, filter_cpt_procedures, validate_extracted_data
from transform_omop import (
    transform_to_person, transform_to_visits, transform_to_procedures,
    transform_to_costs, transform_to_providers, transform_all_tables
)


class TestAbuDhabiETLValidation(unittest.TestCase):
    """Test suite for Abu Dhabi Claims ETL validation against known metrics."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test data path and expected metrics."""
        cls.test_data_path = "../../../../data/real_test_datasets/claim_anonymized.csv"
        
        # Expected metrics from CONSOLIDATED_FINDINGS.md and notebook validation
        cls.expected_metrics = {
            'total_raw_records': 4999,
            'unique_patients_raw': 596,
            'cpt_records': 3185,
            'unique_patients_cpt': 447,
            'unique_visits': 1201,
            'unique_providers': 171,
            'cpt_percentage': 63.7  # Actual: 3185/4999 * 100 = 63.7%
        }
        
        # Load test data once for all tests
        if os.path.exists(cls.test_data_path):
            cls.raw_data = extract_claims_data(cls.test_data_path)
            cls.cpt_data = filter_cpt_procedures(cls.raw_data)
        else:
            cls.raw_data = None
            cls.cpt_data = None
    
    def setUp(self):
        """Set up for each test."""
        if self.raw_data is None:
            self.skipTest(f"Test data not found at {self.test_data_path}")


class TestDataExtraction(TestAbuDhabiETLValidation):
    """Test data extraction and filtering logic."""
    
    def test_raw_data_extraction(self):
        """Test that raw data extraction matches expected metrics."""
        # Test total record count
        self.assertEqual(
            len(self.raw_data), 
            self.expected_metrics['total_raw_records'],
            f"Expected {self.expected_metrics['total_raw_records']} raw records, got {len(self.raw_data)}"
        )
        
        # Test unique patients in raw data
        unique_patients = self.raw_data['aio_patient_id'].nunique()
        self.assertEqual(
            unique_patients,
            self.expected_metrics['unique_patients_raw'],
            f"Expected {self.expected_metrics['unique_patients_raw']} unique patients, got {unique_patients}"
        )
    
    def test_cpt_filtering(self):
        """Test CPT filtering produces expected results."""
        # Test CPT record count
        self.assertEqual(
            len(self.cpt_data),
            self.expected_metrics['cpt_records'],
            f"Expected {self.expected_metrics['cpt_records']} CPT records, got {len(self.cpt_data)}"
        )
        
        # Test CPT percentage
        cpt_percentage = (len(self.cpt_data) / len(self.raw_data)) * 100
        self.assertAlmostEqual(
            cpt_percentage,
            self.expected_metrics['cpt_percentage'],
            places=1,
            msg=f"Expected {self.expected_metrics['cpt_percentage']}% CPT records, got {cpt_percentage:.1f}%"
        )
        
        # Test that all filtered records have act_type_desc == 'CPT'
        non_cpt_records = self.cpt_data[self.cpt_data['act_type_desc'] != 'CPT']
        self.assertEqual(
            len(non_cpt_records),
            0,
            f"Found {len(non_cpt_records)} non-CPT records in filtered data"
        )
    
    def test_data_validation_metrics(self):
        """Test data validation function returns correct metrics."""
        validation_results = validate_extracted_data(self.cpt_data)
        
        # Test all expected metrics are present
        expected_keys = [
            'total_records', 'unique_patients', 'unique_visits', 'unique_providers',
            'expected_records', 'expected_patients', 'expected_visits', 'expected_providers',
            'records_match', 'patients_match', 'visits_match', 'providers_match'
        ]
        
        for key in expected_keys:
            self.assertIn(key, validation_results, f"Missing validation metric: {key}")
        
        # Test specific metric values
        self.assertEqual(validation_results['total_records'], self.expected_metrics['cpt_records'])
        self.assertEqual(validation_results['unique_patients'], self.expected_metrics['unique_patients_cpt'])
        self.assertEqual(validation_results['unique_visits'], self.expected_metrics['unique_visits'])
        self.assertEqual(validation_results['unique_providers'], self.expected_metrics['unique_providers'])


class TestOMOPTransformation(TestAbuDhabiETLValidation):
    """Test OMOP transformation functions."""
    
    def test_person_transformation(self):
        """Test person table transformation."""
        person_records = transform_to_person(self.cpt_data)
        
        # Test record count
        self.assertEqual(
            len(person_records),
            self.expected_metrics['unique_patients_cpt'],
            f"Expected {self.expected_metrics['unique_patients_cpt']} person records, got {len(person_records)}"
        )
        
        # Test all records have required fields
        required_fields = ['person_id', 'person_source_value']
        for record in person_records:
            for field in required_fields:
                self.assertIn(field, record, f"Missing required field: {field}")
        
        # Test unique person IDs
        person_ids = [record['person_id'] for record in person_records]
        self.assertEqual(
            len(set(person_ids)),
            len(person_ids),
            "Duplicate person IDs found"
        )
    
    def test_visit_transformation(self):
        """Test visit occurrence table transformation."""
        visit_records = transform_to_visits(self.cpt_data)
        
        # Test record count
        self.assertEqual(
            len(visit_records),
            self.expected_metrics['unique_visits'],
            f"Expected {self.expected_metrics['unique_visits']} visit records, got {len(visit_records)}"
        )
        
        # Test all records have required fields
        required_fields = ['visit_occurrence_id', 'person_id', 'visit_start_date', 'visit_end_date']
        for record in visit_records:
            for field in required_fields:
                self.assertIn(field, record, f"Missing required field: {field}")
        
        # Test date types
        for record in visit_records:
            self.assertIsNotNone(record['visit_start_date'], "visit_start_date should not be None")
            self.assertIsNotNone(record['visit_end_date'], "visit_end_date should not be None")
    
    def test_procedure_transformation(self):
        """Test procedure occurrence table transformation."""
        procedure_records = transform_to_procedures(self.cpt_data)
        
        # Test record count (1:1 mapping with CPT data)
        self.assertEqual(
            len(procedure_records),
            self.expected_metrics['cpt_records'],
            f"Expected {self.expected_metrics['cpt_records']} procedure records, got {len(procedure_records)}"
        )
        
        # Test all records have required fields
        required_fields = ['procedure_occurrence_id', 'person_id', 'visit_occurrence_id', 'procedure_source_value']
        for record in procedure_records:
            for field in required_fields:
                self.assertIn(field, record, f"Missing required field: {field}")
        
        # Test unique procedure IDs
        procedure_ids = [record['procedure_occurrence_id'] for record in procedure_records]
        self.assertEqual(
            len(set(procedure_ids)),
            len(procedure_ids),
            "Duplicate procedure occurrence IDs found"
        )
    
    def test_cost_transformation(self):
        """Test cost table transformation."""
        cost_records = transform_to_costs(self.cpt_data)
        
        # Test record count (1:1 mapping with CPT data)
        self.assertEqual(
            len(cost_records),
            self.expected_metrics['cpt_records'],
            f"Expected {self.expected_metrics['cpt_records']} cost records, got {len(cost_records)}"
        )
        
        # Test all records have required financial fields
        required_fields = ['cost_id', 'total_charge', 'total_cost', 'total_paid', 'paid_by_patient']
        for record in cost_records:
            for field in required_fields:
                self.assertIn(field, record, f"Missing required field: {field}")
                # Test that financial fields are numeric
                self.assertIsInstance(record[field], (int, float), f"{field} should be numeric")
    
    def test_provider_transformation(self):
        """Test provider table transformation."""
        provider_records = transform_to_providers(self.cpt_data)
        
        # Test record count
        self.assertEqual(
            len(provider_records),
            self.expected_metrics['unique_providers'],
            f"Expected {self.expected_metrics['unique_providers']} provider records, got {len(provider_records)}"
        )
        
        # Test all records have required fields
        required_fields = ['provider_id', 'provider_source_value']
        for record in provider_records:
            for field in required_fields:
                self.assertIn(field, record, f"Missing required field: {field}")
        
        # Test unique provider IDs
        provider_ids = [record['provider_id'] for record in provider_records]
        self.assertEqual(
            len(set(provider_ids)),
            len(provider_ids),
            "Duplicate provider IDs found"
        )


class TestOMOPIntegration(TestAbuDhabiETLValidation):
    """Test complete OMOP transformation integration."""
    
    def test_complete_transformation(self):
        """Test complete transformation produces all expected tables."""
        omop_data = transform_all_tables(self.cpt_data)
        
        # Test all expected tables are present
        expected_tables = ['person', 'visit_occurrence', 'procedure_occurrence', 'cost', 'provider']
        for table in expected_tables:
            self.assertIn(table, omop_data, f"Missing OMOP table: {table}")
        
        # Test total record count
        total_records = sum(len(records) for records in omop_data.values())
        expected_total = (
            self.expected_metrics['unique_patients_cpt'] +  # person
            self.expected_metrics['unique_visits'] +        # visit_occurrence
            self.expected_metrics['cpt_records'] +          # procedure_occurrence
            self.expected_metrics['cpt_records'] +          # cost
            self.expected_metrics['unique_providers']       # provider
        )
        
        self.assertEqual(
            total_records,
            expected_total,
            f"Expected {expected_total} total OMOP records, got {total_records}"
        )
    
    def test_referential_integrity(self):
        """Test referential integrity between OMOP tables."""
        omop_data = transform_all_tables(self.cpt_data)
        
        # Extract IDs for referential integrity testing
        person_ids = set(record['person_id'] for record in omop_data['person'])
        visit_ids = set(record['visit_occurrence_id'] for record in omop_data['visit_occurrence'])
        provider_ids = set(record['provider_id'] for record in omop_data['provider'])
        
        # Test visit_occurrence references valid person_ids
        for visit in omop_data['visit_occurrence']:
            self.assertIn(
                visit['person_id'], 
                person_ids,
                f"Visit {visit['visit_occurrence_id']} references invalid person_id {visit['person_id']}"
            )
        
        # Test procedure_occurrence references valid person_ids and visit_ids
        for procedure in omop_data['procedure_occurrence']:
            self.assertIn(
                procedure['person_id'],
                person_ids,
                f"Procedure {procedure['procedure_occurrence_id']} references invalid person_id {procedure['person_id']}"
            )
            self.assertIn(
                procedure['visit_occurrence_id'],
                visit_ids,
                f"Procedure {procedure['procedure_occurrence_id']} references invalid visit_id {procedure['visit_occurrence_id']}"
            )
            self.assertIn(
                procedure['provider_id'],
                provider_ids,
                f"Procedure {procedure['procedure_occurrence_id']} references invalid provider_id {procedure['provider_id']}"
            )


class TestDataQuality(TestAbuDhabiETLValidation):
    """Test data quality and consistency."""
    
    def test_required_columns_present(self):
        """Test that all required columns are present in the dataset."""
        required_columns = [
            'aio_patient_id', 'case', 'activity_id', 'code_activity', 'act_type_desc',
            'clinician', 'encounter_start_date', 'encounter_end_date',
            'gross', 'net', 'patient_share', 'payment_amount'
        ]
        
        for column in required_columns:
            self.assertIn(
                column,
                self.cpt_data.columns,
                f"Required column missing: {column}"
            )
    
    def test_no_null_critical_fields(self):
        """Test that critical fields have no null values."""
        critical_fields = ['aio_patient_id', 'case', 'activity_id', 'code_activity']
        
        for field in critical_fields:
            null_count = self.cpt_data[field].isnull().sum()
            self.assertEqual(
                null_count,
                0,
                f"Critical field {field} has {null_count} null values"
            )
    
    def test_financial_data_consistency(self):
        """Test financial data consistency and completeness."""
        # Test that financial fields are numeric
        financial_fields = ['gross', 'net', 'patient_share', 'payment_amount']
        
        for field in financial_fields:
            # Test no null values
            null_count = self.cpt_data[field].isnull().sum()
            self.assertEqual(
                null_count,
                0,
                f"Financial field {field} has {null_count} null values"
            )
            
            # Test all values are numeric
            non_numeric = pd.to_numeric(self.cpt_data[field], errors='coerce').isnull().sum()
            self.assertEqual(
                non_numeric,
                0,
                f"Financial field {field} has {non_numeric} non-numeric values"
            )


if __name__ == '__main__':
    # Run tests with verbose output
    unittest.main(verbosity=2)
