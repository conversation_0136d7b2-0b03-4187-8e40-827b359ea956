#!/bin/bash
# Abu Dhabi Claims ETL - Quick Start Script
# Simple wrapper for common development tasks

set -e  # Exit on any error

echo "🚀 Abu Dhabi Claims ETL - Quick Start"
echo "======================================"

# Function to show usage
show_usage() {
    echo ""
    echo "Usage: ./quick_start.sh [command]"
    echo ""
    echo "🔧 SETUP & VALIDATION:"
    echo "  verify     - Environment verification"
    echo "  test       - Unit tests"
    echo ""
    echo "📊 DATA PROCESSING:"
    echo "  validate   - Test data extraction"
    echo "  transform  - Test transformations"
    echo ""
    echo "🚀 FULL PIPELINE:"
    echo "  run        - Complete pipeline (interactive)"
    echo "  clean-run  - Pipeline with data clearing (recommended)"
    echo ""
    echo "❓ HELP:"
    echo "  help       - Show this help message"
    echo "  guide      - Output interpretation guide"
    echo ""
    echo "💡 RECOMMENDED WORKFLOW:"
    echo "  ./quick_start.sh verify      # 1. Verify setup"
    echo "  ./quick_start.sh clean-run   # 2. Run pipeline"
    echo ""
}

# Function to show output interpretation guide
show_guide() {
    echo ""
    echo "📖 OUTPUT INTERPRETATION GUIDE"
    echo "=============================="
    echo ""
    echo "🔍 SUCCESS INDICATORS:"
    echo "  verify     → '🎉 ALL CHECKS PASSED!'"
    echo "  test       → '13 passed' in pytest output"
    echo "  validate   → '3,185 CPT procedure records'"
    echo "  transform  → '8,189 total OMOP records created'"
    echo "  run        → 'ETL Pipeline completed successfully'"
    echo "  clean-run  → 'Clearing X records' + 'Pipeline completed'"
    echo ""
    echo "⚠️  COMMON WARNINGS:"
    echo "  run        → 'Found X existing records' (use clean-run instead)"
    echo ""
    echo "❌ TROUBLESHOOTING:"
    echo "  Database   → python load_database.py --dry-run"
    echo "  Environment → ./quick_start.sh verify"
    echo "  Data file  → Check ../../../../data/real_test_datasets/claim_anonymized.csv"
    echo ""
    echo "⏱️  PERFORMANCE: Total pipeline ~1.3 seconds"
    echo ""
}

# Check if we're in the right directory
if [[ ! -f "run_pipeline.py" ]]; then
    echo "❌ Error: Please run this script from the abu_dhabi_claims_mvp directory"
    echo "   Expected location: src/fhir_omop/etl/abu_dhabi_claims_mvp/"
    exit 1
fi

# Parse command
case "${1:-help}" in
    "verify")
        echo "🔍 SETUP VERIFICATION"
        echo "Running complete environment verification (verify_setup.py)..."
        echo "→ Checking: files, Python packages, database connection, data extraction, unit tests"
        echo ""
        python verify_setup.py
        ;;
    "test")
        echo "🧪 UNIT TESTS"
        echo "Running comprehensive test suite (pytest test_abu_dhabi_etl.py)..."
        echo "→ Testing: data extraction, OMOP transformations, database operations"
        echo ""
        python -m pytest test_abu_dhabi_etl.py -v
        ;;
    "validate")
        echo "📊 DATA EXTRACTION VALIDATION"
        echo "Testing CSV data loading and CPT filtering (run_pipeline.py --validate-only)..."
        echo "→ Expected: 4,999 raw → 3,185 CPT procedures (447 patients, 1,201 visits)"
        echo ""
        python run_pipeline.py --validate-only
        ;;
    "transform")
        echo "🔄 TRANSFORMATION TEST"
        echo "Testing OMOP transformations without database (run_pipeline.py --skip-load)..."
        echo "→ Expected: 8,189 OMOP records (person, visits, procedures, costs, providers)"
        echo ""
        python run_pipeline.py --skip-load
        ;;
    "run")
        echo "🚀 COMPLETE PIPELINE"
        echo "Running full ETL pipeline (run_pipeline.py)..."
        echo "→ Checking for existing data first..."
        echo ""

        # Check if data exists in database
        echo "🔍 Checking for existing data in database..."
        existing_data=$(python load_database.py --dry-run 2>/dev/null | grep -o "person.*records" | grep -o "[0-9]*" | head -1)

        # If detection failed, try direct database query
        if [ -z "$existing_data" ]; then
            existing_data=$(python -c "
from load_database import get_database_session, create_connection_string
from sqlalchemy import text
conn_str = create_connection_string('localhost', '5432', 'omop_abu_dhabi', 'jaimepm', '')
with get_database_session(conn_str) as session:
    result = session.execute(text('SELECT COUNT(*) FROM person'))
    print(result.scalar())
" 2>/dev/null || echo "0")
        fi

        echo "📊 Found $existing_data existing person records"

        if [ "$existing_data" -gt 0 ] 2>/dev/null; then
            echo "⚠️  WARNING: Found $existing_data existing records in database"
            echo "   The pipeline will fail with duplicate key errors."
            echo ""
            echo "💡 RECOMMENDED SOLUTIONS:"
            echo "   1. Use: ./quick_start.sh clean-run  (clears data automatically)"
            echo "   2. Clear manually: python -c \"from load_database import *; conn=create_connection_string('localhost','5432','omop_abu_dhabi','jaimepm',''); session=get_database_session(conn); session.execute(text('TRUNCATE TABLE cost, procedure_occurrence, visit_occurrence, provider, person CASCADE')); session.commit()\""
            echo ""
            echo "❓ Continue anyway? (will likely fail) [y/N]:"
            read -r response
            if [[ ! "$response" =~ ^[Yy]$ ]]; then
                echo "❌ Pipeline cancelled. Use './quick_start.sh clean-run' instead."
                exit 1
            fi
        fi

        python run_pipeline.py
        ;;
    "clean-run")
        echo "🧹 SAFE PIPELINE EXECUTION"
        echo "Running pipeline with automatic data clearing (run_pipeline.py --clear-existing)..."
        echo "→ Will clear existing data → extract → transform → load"
        echo "→ Recommended for repeated runs"
        echo ""
        python run_pipeline.py --clear-existing
        ;;
    "help"|"--help"|"-h")
        show_usage
        ;;
    "guide")
        show_guide
        ;;
    *)
        echo "❌ Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac

# Function to show command completion with context
show_completion() {
    local command=$1
    echo ""
    echo "✅ Command '$command' completed successfully!"

    case "$command" in
        "verify")
            echo "💡 Next step: Run './quick_start.sh clean-run' to execute the pipeline"
            ;;
        "test")
            echo "💡 All tests passed! Your implementation is validated."
            ;;
        "validate")
            echo "💡 Data extraction validated! Next: './quick_start.sh transform' to test transformations"
            ;;
        "transform")
            echo "💡 Transformations validated! Next: './quick_start.sh clean-run' to load to database"
            ;;
        "run"|"clean-run")
            echo "💡 Pipeline complete! Check database for 8,189 OMOP records across 5 tables"
            ;;
    esac
    echo ""
}

show_completion "${1:-help}"
