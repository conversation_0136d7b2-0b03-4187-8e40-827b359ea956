#!/usr/bin/env python3
"""
Abu Dhabi Claims ETL Setup Verification Script

This script helps developers verify that the Abu Dhabi Claims to OMOP ETL
module is properly configured and ready to run.

Usage:
    python verify_setup.py
    ./verify_setup.py          # If made executable

Make executable with: chmod +x verify_setup.py

Author: FHIR-OMOP Development Team
Created: December 2024
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print('='*60)

def print_step(step_num, description):
    """Print a formatted step."""
    print(f"\n{step_num}. {description}")
    print("-" * 50)

def run_command(command, description):
    """Run a command and return success status."""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print(f"✅ {description}")
            return True
        else:
            print(f"❌ {description}")
            print(f"   Error: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - Timeout")
        return False
    except Exception as e:
        print(f"❌ {description} - Exception: {e}")
        return False

def check_file_exists(file_path, description):
    """Check if a file exists."""
    if Path(file_path).exists():
        print(f"✅ {description}")
        return True
    else:
        print(f"❌ {description} - File not found: {file_path}")
        return False

def main():
    """Main verification function."""
    print_header("Abu Dhabi Claims ETL Setup Verification")
    
    # Track overall success
    all_checks_passed = True
    
    # Step 1: Check required files
    print_step(1, "Checking Required Files")
    
    required_files = [
        ("config.yaml", "Configuration file"),
        ("extract_claims.py", "Data extraction script"),
        ("transform_omop.py", "OMOP transformation script"),
        ("load_database.py", "Database loading script"),
        ("run_pipeline.py", "Main pipeline script"),
        ("test_abu_dhabi_etl.py", "Unit tests"),
        ("../../../../data/real_test_datasets/claim_anonymized.csv", "Input data file")
    ]
    
    for file_path, description in required_files:
        if not check_file_exists(file_path, description):
            all_checks_passed = False
    
    # Step 2: Check Python environment
    print_step(2, "Checking Python Environment")
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 11):
        print(f"✅ Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    else:
        print(f"❌ Python version: {python_version.major}.{python_version.minor}.{python_version.micro} (requires 3.11+)")
        all_checks_passed = False
    
    # Check required packages
    required_packages = ["pandas", "sqlalchemy", "psycopg2", "yaml", "pytest"]
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ Package available: {package}")
        except ImportError:
            print(f"❌ Package missing: {package}")
            all_checks_passed = False
    
    # Step 3: Test configuration
    print_step(3, "Testing Configuration")
    
    if not run_command("python load_database.py --dry-run", "Database connection test"):
        all_checks_passed = False
    
    # Step 4: Test data extraction
    print_step(4, "Testing Data Extraction")
    
    if not run_command("python run_pipeline.py --validate-only", "Data extraction validation"):
        all_checks_passed = False
    
    # Step 5: Run unit tests
    print_step(5, "Running Unit Tests")
    
    if not run_command("python -m pytest test_abu_dhabi_etl.py -v", "Unit test execution"):
        all_checks_passed = False
    
    # Step 6: Test transformation (without database loading)
    print_step(6, "Testing Transformation")
    
    if not run_command("python run_pipeline.py --skip-load", "OMOP transformation test"):
        all_checks_passed = False
    
    # Final summary
    print_header("Verification Summary")
    
    if all_checks_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("\n✅ Your Abu Dhabi Claims ETL module is ready to use.")
        print("\n📋 Next steps:")
        print("   1. Run complete pipeline: python run_pipeline.py")
        print("   2. Check the README.md for detailed usage instructions")
        print("   3. Review the learning notebook for implementation details")
        return 0
    else:
        print("❌ SOME CHECKS FAILED!")
        print("\n🔧 Please fix the issues above before proceeding.")
        print("\n💡 Common solutions:")
        print("   - Ensure you're in the correct directory")
        print("   - Activate the fhir-omop conda environment")
        print("   - Check database connection settings in config.yaml")
        print("   - Verify input data file exists")
        return 1

if __name__ == "__main__":
    sys.exit(main())
