"""
Encounter to Visit mapping module for FHIR to OMOP transformation.

This module provides functions to transform FHIR Encounter resources to OMOP Visit_Occurrence records.

Original Sources:
- OHDSI ETL-German-FHIR-Core: https://github.com/OHDSI/ETL-German-FHIR-Core/blob/main/src/main/java/org/miracum/etl/fhirtoomop/mapper/EncounterMapper.java
- NACHC-CAD/fhir-to-omop: https://github.com/NACHC-CAD/fhir-to-omop/tree/main/src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/encounter
- Vulcan FHIR-to-OMOP Implementation Guide: https://build.fhir.org/ig/HL7/fhir-omop-ig/

References:
- FHIR Encounter Resource: https://www.hl7.org/fhir/encounter.html
- OMOP CDM Visit_Occurrence Table: https://ohdsi.github.io/CommonDataModel/cdm54.html#VISIT_OCCURRENCE
"""
import logging
import pandas as pd
from datetime import datetime
from utils.db_utils import get_concept_id, bulk_insert_dataframe
from utils.fhir_utils import (
    parse_fhir_datetime,
    get_code_from_codeableconcept,
    get_system_from_codeableconcept,
    get_display_from_codeableconcept,
    get_referenced_resource
)
from utils.config import CONCEPT_IDS

# Set up logging
logger = logging.getLogger(__name__)

def map_encounter_class_to_visit_concept_id(encounter_class):
    """
    Map FHIR Encounter.class to OMOP visit_concept_id.

    Args:
        encounter_class (dict): FHIR Encounter.class coding

    Returns:
        int: OMOP visit_concept_id
    """
    if not encounter_class or "code" not in encounter_class:
        return CONCEPT_IDS["visit_type"]["other"]

    code = encounter_class["code"].lower()

    # Map common encounter classes to visit types
    if code == "inpatient" or code == "inp":
        return CONCEPT_IDS["visit_type"]["inpatient"]
    elif code == "outpatient" or code == "amb" or code == "ambulatory":
        return CONCEPT_IDS["visit_type"]["outpatient"]
    elif code == "emergency" or code == "emer" or code == "er":
        return CONCEPT_IDS["visit_type"]["emergency"]
    else:
        logger.warning(f"Unknown encounter class: {code}, using 'other' concept ID")
        return CONCEPT_IDS["visit_type"]["other"]

def map_encounter_type_to_visit_type_concept_id(encounter_type):
    """
    Map FHIR Encounter.type to OMOP visit_type_concept_id.

    Args:
        encounter_type (list): FHIR Encounter.type CodeableConcept list

    Returns:
        int: OMOP visit_type_concept_id
    """
    # For simplicity, we'll use a standard EHR visit concept ID
    # In a real implementation, this would map to more specific concepts
    return 44818518  # Visit derived from EHR record

def extract_visit_dates(encounter):
    """
    Extract start and end dates from FHIR Encounter resource.

    Args:
        encounter (dict): FHIR Encounter resource

    Returns:
        tuple: (start_datetime, end_datetime)
    """
    start_datetime = None
    end_datetime = None

    # Extract period if available
    if "period" in encounter:
        period = encounter["period"]
        if "start" in period:
            start_datetime = parse_fhir_datetime(period["start"])
        if "end" in period:
            end_datetime = parse_fhir_datetime(period["end"])

    # If no period, try to use other date fields
    if not start_datetime and "statusHistory" in encounter and encounter["statusHistory"]:
        # Use the earliest status date as start date
        earliest_date = None
        for status in encounter["statusHistory"]:
            if "period" in status and "start" in status["period"]:
                status_date = parse_fhir_datetime(status["period"]["start"])
                if not earliest_date or status_date < earliest_date:
                    earliest_date = status_date

        if earliest_date:
            start_datetime = earliest_date

    return start_datetime, end_datetime

def map_encounter_to_visit(encounter, person_id=None):
    """
    Map FHIR Encounter resource to OMOP Visit_Occurrence record.

    Args:
        encounter (dict): FHIR Encounter resource
        person_id (int, optional): OMOP person_id if already known

    Returns:
        dict: OMOP Visit_Occurrence record
    """
    if not encounter or encounter.get("resourceType") != "Encounter":
        logger.error("Invalid Encounter resource")
        return None

    try:
        # Extract encounter ID
        encounter_id = encounter.get("id")
        if not encounter_id:
            logger.error("Encounter resource missing ID")
            return None

        # Get person_id from subject reference if not provided
        if not person_id and "subject" in encounter and "reference" in encounter["subject"]:
            subject_ref = encounter["subject"]["reference"]
            if subject_ref.startswith("Patient/"):
                patient_id = subject_ref.split("/")[1]
                person_id = int(patient_id) if patient_id.isdigit() else None

        if not person_id:
            logger.error(f"Could not determine person_id for Encounter {encounter_id}")
            return None

        # Extract visit type from class
        visit_concept_id = CONCEPT_IDS["visit_type"]["other"]
        if "class" in encounter:
            visit_concept_id = map_encounter_class_to_visit_concept_id(encounter["class"])

        # Extract visit type concept
        visit_type_concept_id = map_encounter_type_to_visit_type_concept_id(
            encounter.get("type", [])
        )

        # Extract visit dates
        start_datetime, end_datetime = extract_visit_dates(encounter)
        if not start_datetime:
            logger.warning(f"Encounter {encounter_id} missing start date, using current date")
            start_datetime = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")

        # Extract care site information
        care_site_id = None
        if "serviceProvider" in encounter and "reference" in encounter["serviceProvider"]:
            # In a real implementation, this would map to a care_site_id
            # For this sample, we'll leave it as None
            pass

        # Extract provider information
        provider_id = None
        if "participant" in encounter and encounter["participant"]:
            for participant in encounter["participant"]:
                if "individual" in participant and "reference" in participant["individual"]:
                    # In a real implementation, this would map to a provider_id
                    # For this sample, we'll leave it as None
                    pass

        # Create Visit_Occurrence record
        visit = {
            "visit_occurrence_id": int(encounter_id) if encounter_id.isdigit() else None,  # Will be auto-assigned if None
            "person_id": person_id,
            "visit_concept_id": visit_concept_id,
            "visit_start_date": start_datetime[:10] if start_datetime else None,
            "visit_start_datetime": start_datetime,
            "visit_end_date": end_datetime[:10] if end_datetime else None,
            "visit_end_datetime": end_datetime,
            "visit_type_concept_id": visit_type_concept_id,
            "provider_id": provider_id,
            "care_site_id": care_site_id,
            "visit_source_value": encounter_id,
            "visit_source_concept_id": 0,  # Would require vocabulary mapping
            "admitted_from_concept_id": 0,  # Would require mapping from hospitalization.admitSource
            "admitted_from_source_value": None,
            "discharge_to_concept_id": 0,  # Would require mapping from hospitalization.dischargeDisposition
            "discharge_to_source_value": None,
            "preceding_visit_occurrence_id": None  # Would require additional logic to link visits
        }

        return visit

    except Exception as e:
        logger.error(f"Error mapping Encounter to Visit_Occurrence: {e}")
        return None

def transform_encounters_to_visits(encounters, person_id_map=None):
    """
    Transform a list of FHIR Encounter resources to OMOP Visit_Occurrence records.

    Args:
        encounters (list): List of FHIR Encounter resources
        person_id_map (dict, optional): Mapping of patient IDs to person IDs

    Returns:
        pandas.DataFrame: DataFrame of OMOP Visit_Occurrence records
    """
    visits = []

    for encounter in encounters:
        # Get person_id from map if available
        person_id = None
        if person_id_map and "subject" in encounter and "reference" in encounter["subject"]:
            subject_ref = encounter["subject"]["reference"]
            if subject_ref.startswith("Patient/"):
                patient_id = subject_ref.split("/")[1]
                person_id = person_id_map.get(patient_id)

        visit = map_encounter_to_visit(encounter, person_id)
        if visit:
            visits.append(visit)

    if not visits:
        logger.warning("No encounters were successfully mapped to visits")
        return pd.DataFrame()

    return pd.DataFrame(visits)

def load_visits_to_omop(visits_df):
    """
    Load Visit_Occurrence records into OMOP CDM.

    Args:
        visits_df (pandas.DataFrame): DataFrame of OMOP Visit_Occurrence records

    Returns:
        int: Number of records inserted
    """
    if visits_df.empty:
        logger.warning("No Visit_Occurrence records to load")
        return 0

    try:
        # Insert Visit_Occurrence records
        rows_inserted = bulk_insert_dataframe(visits_df, "visit_occurrence")
        logger.info(f"Inserted {rows_inserted} Visit_Occurrence records")
        return rows_inserted

    except Exception as e:
        logger.error(f"Error loading Visit_Occurrence records: {e}")
        raise
