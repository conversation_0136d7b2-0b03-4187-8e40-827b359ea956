"""
Patient to Person mapping module for FHIR to OMOP transformation.

This module provides functions to transform FHIR Patient resources to OMOP Person records.

Original Sources:
- OHDSI ETL-German-FHIR-Core: https://github.com/OHDSI/ETL-German-FHIR-Core/blob/main/src/main/java/org/miracum/etl/fhirtoomop/mapper/PatientMapper.java
- NACHC-CAD/fhir-to-omop: https://github.com/NACHC-CAD/fhir-to-omop/tree/main/src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/patient
- Vulcan FHIR-to-OMOP Implementation Guide: https://build.fhir.org/ig/HL7/fhir-omop-ig/

References:
- FHIR Patient Resource: https://www.hl7.org/fhir/patient.html
- OMOP CDM Person Table: https://ohdsi.github.io/CommonDataModel/cdm54.html#PERSON
"""
import logging
import pandas as pd
from datetime import datetime
from utils.db_utils import get_concept_id, bulk_insert_dataframe
from utils.fhir_utils import (
    parse_fhir_datetime,
    extract_extension_value,
    get_resource_identifier
)
from utils.config import CONCEPT_IDS

# Set up logging
logger = logging.getLogger(__name__)

def map_gender_to_concept_id(gender):
    """
    Map FHIR gender to OMOP gender concept ID.

    Args:
        gender (str): FHIR gender value

    Returns:
        int: OMOP gender concept ID
    """
    if not gender:
        return CONCEPT_IDS["gender"]["unknown"]

    gender = gender.lower()
    if gender in CONCEPT_IDS["gender"]:
        return CONCEPT_IDS["gender"][gender]

    logger.warning(f"Unknown gender: {gender}, using 'unknown' concept ID")
    return CONCEPT_IDS["gender"]["unknown"]

def map_race_to_concept_id(race_extension):
    """
    Map FHIR US Core race extension to OMOP race concept ID.

    Args:
        race_extension (dict): FHIR US Core race extension

    Returns:
        int: OMOP race concept ID
    """
    if not race_extension:
        return CONCEPT_IDS["race"]["unknown"]

    # US Core race extension has a specific structure
    # Look for the ombCategory extension which contains the primary race
    if isinstance(race_extension, list):
        for ext in race_extension:
            if ext.get("url") == "ombCategory":
                if "valueCoding" in ext:
                    coding = ext["valueCoding"]
                    code = coding.get("code", "").lower()

                    if code == "2106-3":
                        return CONCEPT_IDS["race"]["white"]
                    elif code == "2054-5":
                        return CONCEPT_IDS["race"]["black"]
                    elif code == "2028-9":
                        return CONCEPT_IDS["race"]["asian"]
                    elif code == "1002-5":
                        return CONCEPT_IDS["race"]["native"]
                    elif code == "2131-1":
                        return CONCEPT_IDS["race"]["other"]

    logger.warning(f"Could not determine race from extension, using 'unknown' concept ID")
    return CONCEPT_IDS["race"]["unknown"]

def map_ethnicity_to_concept_id(ethnicity_extension):
    """
    Map FHIR US Core ethnicity extension to OMOP ethnicity concept ID.

    Args:
        ethnicity_extension (dict): FHIR US Core ethnicity extension

    Returns:
        int: OMOP ethnicity concept ID
    """
    if not ethnicity_extension:
        return CONCEPT_IDS["ethnicity"]["unknown"]

    # US Core ethnicity extension has a specific structure
    # Look for the ombCategory extension which contains the ethnicity
    if isinstance(ethnicity_extension, list):
        for ext in ethnicity_extension:
            if ext.get("url") == "ombCategory":
                if "valueCoding" in ext:
                    coding = ext["valueCoding"]
                    code = coding.get("code", "")

                    if code == "2135-2":
                        return CONCEPT_IDS["ethnicity"]["hispanic"]
                    elif code == "2186-5":
                        return CONCEPT_IDS["ethnicity"]["nonhispanic"]

    logger.warning(f"Could not determine ethnicity from extension, using 'unknown' concept ID")
    return CONCEPT_IDS["ethnicity"]["unknown"]

def extract_birth_datetime(patient):
    """
    Extract birth datetime from FHIR Patient resource.

    Args:
        patient (dict): FHIR Patient resource

    Returns:
        str: Birth datetime in YYYY-MM-DD format
    """
    if "birthDate" in patient:
        return patient["birthDate"]

    # Try extensions if birthDate not available
    birth_datetime = extract_extension_value(
        patient,
        "http://hl7.org/fhir/StructureDefinition/patient-birthTime"
    )
    if birth_datetime:
        return birth_datetime

    return None

def map_patient_to_person(patient):
    """
    Map FHIR Patient resource to OMOP Person record.

    Args:
        patient (dict): FHIR Patient resource

    Returns:
        dict: OMOP Person record
    """
    if not patient or patient.get("resourceType") != "Patient":
        logger.error("Invalid Patient resource")
        return None

    try:
        # Extract patient ID
        patient_id = patient.get("id")
        if not patient_id:
            logger.error("Patient resource missing ID")
            return None

        # Extract gender
        gender = patient.get("gender")
        gender_concept_id = map_gender_to_concept_id(gender)

        # Extract birth information
        birth_datetime = extract_birth_datetime(patient)
        if birth_datetime:
            birth_datetime = parse_fhir_datetime(birth_datetime)
            year_of_birth = int(birth_datetime[:4]) if birth_datetime else None
            month_of_birth = int(birth_datetime[5:7]) if birth_datetime and len(birth_datetime) >= 7 else None
            day_of_birth = int(birth_datetime[8:10]) if birth_datetime and len(birth_datetime) >= 10 else None
        else:
            year_of_birth = None
            month_of_birth = None
            day_of_birth = None

        # Extract race and ethnicity from US Core extensions
        race_extension = extract_extension_value(
            patient,
            "http://hl7.org/fhir/us/core/StructureDefinition/us-core-race"
        )
        ethnicity_extension = extract_extension_value(
            patient,
            "http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity"
        )

        race_concept_id = map_race_to_concept_id(race_extension)
        ethnicity_concept_id = map_ethnicity_to_concept_id(ethnicity_extension)

        # Extract name information
        if "name" in patient and patient["name"]:
            name = patient["name"][0]  # Use first name entry
            given_name = name.get("given", [""])[0] if "given" in name and name["given"] else ""
            family_name = name.get("family", "")
        else:
            given_name = ""
            family_name = ""

        # Create Person record
        person = {
            "person_id": int(patient_id) if patient_id.isdigit() else None,  # Will be auto-assigned if None
            "gender_concept_id": gender_concept_id,
            "year_of_birth": year_of_birth,
            "month_of_birth": month_of_birth,
            "day_of_birth": day_of_birth,
            "birth_datetime": birth_datetime,
            "race_concept_id": race_concept_id,
            "ethnicity_concept_id": ethnicity_concept_id,
            "location_id": None,  # Would require mapping from Patient.address
            "provider_id": None,  # Would require mapping from Patient.generalPractitioner
            "care_site_id": None,  # Would require additional mapping
            "person_source_value": patient_id,
            "gender_source_value": gender,
            "gender_source_concept_id": 0,  # Would require vocabulary mapping
            "race_source_value": str(race_extension) if race_extension else None,
            "race_source_concept_id": 0,  # Would require vocabulary mapping
            "ethnicity_source_value": str(ethnicity_extension) if ethnicity_extension else None,
            "ethnicity_source_concept_id": 0,  # Would require vocabulary mapping
        }

        return person

    except Exception as e:
        logger.error(f"Error mapping Patient to Person: {e}")
        return None

def transform_patients_to_persons(patients):
    """
    Transform a list of FHIR Patient resources to OMOP Person records.

    Args:
        patients (list): List of FHIR Patient resources

    Returns:
        pandas.DataFrame: DataFrame of OMOP Person records
    """
    persons = []

    for patient in patients:
        person = map_patient_to_person(patient)
        if person:
            persons.append(person)

    if not persons:
        logger.warning("No patients were successfully mapped to persons")
        return pd.DataFrame()

    return pd.DataFrame(persons)

def load_persons_to_omop(persons_df):
    """
    Load Person records into OMOP CDM.

    Args:
        persons_df (pandas.DataFrame): DataFrame of OMOP Person records

    Returns:
        int: Number of records inserted
    """
    if persons_df.empty:
        logger.warning("No Person records to load")
        return 0

    try:
        # Insert Person records
        rows_inserted = bulk_insert_dataframe(persons_df, "person")
        logger.info(f"Inserted {rows_inserted} Person records")
        return rows_inserted

    except Exception as e:
        logger.error(f"Error loading Person records: {e}")
        raise
