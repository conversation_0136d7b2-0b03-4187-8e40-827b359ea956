"""
Observation to Measurement/Observation mapping module for FHIR to OMOP transformation.

This module provides functions to transform FHIR Observation resources to OMOP Measurement and Observation records.

Original Sources:
- OHDSI ETL-German-FHIR-Core: https://github.com/OHDSI/ETL-German-FHIR-Core/blob/main/src/main/java/org/miracum/etl/fhirtoomop/mapper/ObservationMapper.java
- NACHC-CAD/fhir-to-omop: https://github.com/NACHC-CAD/fhir-to-omop/tree/main/src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/observation
- Vulcan FHIR-to-OMOP Implementation Guide: https://build.fhir.org/ig/HL7/fhir-omop-ig/

References:
- FHIR Observation Resource: https://www.hl7.org/fhir/observation.html
- OMOP CDM Measurement Table: https://ohdsi.github.io/CommonDataModel/cdm54.html#MEASUREMENT
- OMOP CDM Observation Table: https://ohdsi.github.io/CommonDataModel/cdm54.html#OBSERVATION
"""
import logging
import pandas as pd
from datetime import datetime
from utils.db_utils import get_concept_id, get_source_concept_id, bulk_insert_dataframe
from utils.fhir_utils import (
    parse_fhir_datetime,
    get_code_from_codeableconcept,
    get_system_from_codeableconcept,
    get_display_from_codeableconcept,
    get_referenced_resource,
    extract_coding
)
from utils.config import CONCEPT_IDS, SOURCE_VOCABULARY_IDS

# Set up logging
logger = logging.getLogger(__name__)

def map_observation_code_to_concept_id(observation_code, system):
    """
    Map FHIR Observation.code to OMOP concept_id.

    Args:
        observation_code (str): FHIR Observation.code value
        system (str): FHIR Observation.code.system value

    Returns:
        tuple: (concept_id, source_concept_id)
    """
    if not observation_code or not system:
        return 0, 0

    # Determine vocabulary ID from system
    vocabulary_id = None
    if "loinc" in system.lower():
        vocabulary_id = SOURCE_VOCABULARY_IDS["observation"]["loinc"]
    elif "snomed" in system.lower():
        vocabulary_id = SOURCE_VOCABULARY_IDS["observation"]["snomed"]

    if not vocabulary_id:
        logger.warning(f"Unknown coding system: {system}, cannot map to vocabulary")
        return 0, 0

    # Get source concept ID
    source_concept_id = get_source_concept_id(observation_code, vocabulary_id)

    # Get standard concept ID
    concept_id = get_concept_id(observation_code, vocabulary_id)

    return concept_id, source_concept_id

def determine_observation_domain(concept_id, value_type):
    """
    Determine whether an observation should be mapped to OMOP Measurement or Observation.

    Args:
        concept_id (int): OMOP concept_id for the observation
        value_type (str): Type of observation value

    Returns:
        str: Domain ('Measurement' or 'Observation')
    """
    # In a real implementation, this would check the domain of the concept
    # For this sample, we'll use a simple heuristic based on value type
    if value_type in ['Quantity', 'valueQuantity', 'SampledData', 'numeric']:
        return 'Measurement'
    else:
        return 'Observation'

def extract_observation_value(observation):
    """
    Extract value and value type from FHIR Observation resource.

    Args:
        observation (dict): FHIR Observation resource

    Returns:
        tuple: (value, value_type, value_as_concept_id, unit_concept_id, unit_source_value)
    """
    value = None
    value_type = None
    value_as_concept_id = 0
    unit_concept_id = 0
    unit_source_value = None

    # Check for valueQuantity
    if "valueQuantity" in observation:
        value_type = "Quantity"
        value = observation["valueQuantity"].get("value")
        unit_source_value = observation["valueQuantity"].get("unit")

        # In a real implementation, map unit to concept_id
        # unit_concept_id = get_concept_id(unit_source_value, 'UCUM')

    # Check for valueCodeableConcept
    elif "valueCodeableConcept" in observation:
        value_type = "CodeableConcept"

        coding = extract_coding(observation["valueCodeableConcept"].get("coding", []))
        if coding:
            value = coding.get("display") or coding.get("code")

            # In a real implementation, map code to concept_id
            # system = coding.get("system")
            # code = coding.get("code")
            # if system and code:
            #     value_as_concept_id = get_concept_id(code, system)

    # Check for valueString
    elif "valueString" in observation:
        value_type = "String"
        value = observation["valueString"]

    # Check for valueBoolean
    elif "valueBoolean" in observation:
        value_type = "Boolean"
        value = observation["valueBoolean"]

    # Check for valueInteger
    elif "valueInteger" in observation:
        value_type = "Integer"
        value = observation["valueInteger"]

    # Check for valueRange
    elif "valueRange" in observation:
        value_type = "Range"
        low = observation["valueRange"].get("low", {}).get("value")
        high = observation["valueRange"].get("high", {}).get("value")
        if low is not None and high is not None:
            value = f"{low}-{high}"
        elif low is not None:
            value = f">={low}"
        elif high is not None:
            value = f"<={high}"

    # Check for valueRatio
    elif "valueRatio" in observation:
        value_type = "Ratio"
        numerator = observation["valueRatio"].get("numerator", {}).get("value")
        denominator = observation["valueRatio"].get("denominator", {}).get("value")
        if numerator is not None and denominator is not None:
            value = f"{numerator}/{denominator}"

    # Check for component values
    elif "component" in observation and observation["component"]:
        value_type = "Component"
        components = []
        for component in observation["component"]:
            if "code" in component and "coding" in component["code"]:
                code_display = get_display_from_codeableconcept(component["code"])

                # Extract component value
                component_value = None
                if "valueQuantity" in component:
                    component_value = component["valueQuantity"].get("value")
                    if "unit" in component["valueQuantity"]:
                        component_value = f"{component_value} {component['valueQuantity']['unit']}"
                elif "valueString" in component:
                    component_value = component["valueString"]
                elif "valueCodeableConcept" in component:
                    component_value = get_display_from_codeableconcept(component["valueCodeableConcept"])

                if code_display and component_value:
                    components.append(f"{code_display}: {component_value}")

        if components:
            value = "; ".join(components)

    # Check for dataAbsentReason
    elif "dataAbsentReason" in observation:
        value_type = "Absent"
        value = get_display_from_codeableconcept(observation["dataAbsentReason"])

    return value, value_type, value_as_concept_id, unit_concept_id, unit_source_value

def map_observation_to_measurement(observation, person_id=None, visit_id=None):
    """
    Map FHIR Observation resource to OMOP Measurement record.

    Args:
        observation (dict): FHIR Observation resource
        person_id (int, optional): OMOP person_id if already known
        visit_id (int, optional): OMOP visit_occurrence_id if already known

    Returns:
        dict: OMOP Measurement record
    """
    if not observation or observation.get("resourceType") != "Observation":
        logger.error("Invalid Observation resource")
        return None

    try:
        # Extract observation ID
        observation_id = observation.get("id")
        if not observation_id:
            logger.error("Observation resource missing ID")
            return None

        # Get person_id from subject reference if not provided
        if not person_id and "subject" in observation and "reference" in observation["subject"]:
            subject_ref = observation["subject"]["reference"]
            if subject_ref.startswith("Patient/"):
                patient_id = subject_ref.split("/")[1]
                person_id = int(patient_id) if patient_id.isdigit() else None

        if not person_id:
            logger.error(f"Could not determine person_id for Observation {observation_id}")
            return None

        # Get visit_id from encounter reference if not provided
        if not visit_id and "encounter" in observation and "reference" in observation["encounter"]:
            encounter_ref = observation["encounter"]["reference"]
            if encounter_ref.startswith("Encounter/"):
                encounter_id = encounter_ref.split("/")[1]
                visit_id = int(encounter_id) if encounter_id.isdigit() else None

        # Extract observation code and system
        measurement_concept_id = 0
        measurement_source_concept_id = 0
        measurement_source_value = None

        if "code" in observation:
            code = get_code_from_codeableconcept(observation["code"])
            system = get_system_from_codeableconcept(observation["code"])
            display = get_display_from_codeableconcept(observation["code"])

            if code and system:
                measurement_concept_id, measurement_source_concept_id = map_observation_code_to_concept_id(code, system)
                measurement_source_value = f"{code}|{display}" if display else code

        # Extract observation type
        measurement_type_concept_id = CONCEPT_IDS["type"]["ehr_measurement"]

        # Extract observation date
        measurement_datetime = None
        if "effectiveDateTime" in observation:
            measurement_datetime = parse_fhir_datetime(observation["effectiveDateTime"])
        elif "effectivePeriod" in observation and "start" in observation["effectivePeriod"]:
            measurement_datetime = parse_fhir_datetime(observation["effectivePeriod"]["start"])
        elif "issued" in observation:
            measurement_datetime = parse_fhir_datetime(observation["issued"])

        if not measurement_datetime:
            logger.warning(f"Observation {observation_id} missing effective date, using current date")
            measurement_datetime = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")

        # Extract observation value
        value, value_type, value_as_concept_id, unit_concept_id, unit_source_value = extract_observation_value(observation)

        # Extract provider information
        provider_id = None
        if "performer" in observation and observation["performer"]:
            # In a real implementation, this would map to a provider_id
            # For this sample, we'll leave it as None
            pass

        # Create Measurement record
        measurement = {
            "measurement_id": int(observation_id) if observation_id.isdigit() else None,  # Will be auto-assigned if None
            "person_id": person_id,
            "measurement_concept_id": measurement_concept_id,
            "measurement_date": measurement_datetime[:10] if measurement_datetime else None,
            "measurement_datetime": measurement_datetime,
            "measurement_type_concept_id": measurement_type_concept_id,
            "operator_concept_id": 0,  # Would require additional mapping
            "value_as_number": float(value) if value_type in ["Quantity", "Integer"] and value is not None else None,
            "value_as_concept_id": value_as_concept_id,
            "unit_concept_id": unit_concept_id,
            "range_low": None,  # Would require mapping from referenceRange
            "range_high": None,  # Would require mapping from referenceRange
            "provider_id": provider_id,
            "visit_occurrence_id": visit_id,
            "visit_detail_id": None,
            "measurement_source_value": measurement_source_value,
            "measurement_source_concept_id": measurement_source_concept_id,
            "unit_source_value": unit_source_value,
            "value_source_value": str(value) if value is not None else None
        }

        return measurement

    except Exception as e:
        logger.error(f"Error mapping Observation to Measurement: {e}")
        return None

def map_observation_to_observation(observation, person_id=None, visit_id=None):
    """
    Map FHIR Observation resource to OMOP Observation record.

    Args:
        observation (dict): FHIR Observation resource
        person_id (int, optional): OMOP person_id if already known
        visit_id (int, optional): OMOP visit_occurrence_id if already known

    Returns:
        dict: OMOP Observation record
    """
    if not observation or observation.get("resourceType") != "Observation":
        logger.error("Invalid Observation resource")
        return None

    try:
        # Extract observation ID
        observation_id = observation.get("id")
        if not observation_id:
            logger.error("Observation resource missing ID")
            return None

        # Get person_id from subject reference if not provided
        if not person_id and "subject" in observation and "reference" in observation["subject"]:
            subject_ref = observation["subject"]["reference"]
            if subject_ref.startswith("Patient/"):
                patient_id = subject_ref.split("/")[1]
                person_id = int(patient_id) if patient_id.isdigit() else None

        if not person_id:
            logger.error(f"Could not determine person_id for Observation {observation_id}")
            return None

        # Get visit_id from encounter reference if not provided
        if not visit_id and "encounter" in observation and "reference" in observation["encounter"]:
            encounter_ref = observation["encounter"]["reference"]
            if encounter_ref.startswith("Encounter/"):
                encounter_id = encounter_ref.split("/")[1]
                visit_id = int(encounter_id) if encounter_id.isdigit() else None

        # Extract observation code and system
        observation_concept_id = 0
        observation_source_concept_id = 0
        observation_source_value = None

        if "code" in observation:
            code = get_code_from_codeableconcept(observation["code"])
            system = get_system_from_codeableconcept(observation["code"])
            display = get_display_from_codeableconcept(observation["code"])

            if code and system:
                observation_concept_id, observation_source_concept_id = map_observation_code_to_concept_id(code, system)
                observation_source_value = f"{code}|{display}" if display else code

        # Extract observation type
        observation_type_concept_id = CONCEPT_IDS["type"]["ehr_observation"]

        # Extract observation date
        observation_datetime = None
        if "effectiveDateTime" in observation:
            observation_datetime = parse_fhir_datetime(observation["effectiveDateTime"])
        elif "effectivePeriod" in observation and "start" in observation["effectivePeriod"]:
            observation_datetime = parse_fhir_datetime(observation["effectivePeriod"]["start"])
        elif "issued" in observation:
            observation_datetime = parse_fhir_datetime(observation["issued"])

        if not observation_datetime:
            logger.warning(f"Observation {observation_id} missing effective date, using current date")
            observation_datetime = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")

        # Extract observation value
        value, value_type, value_as_concept_id, _, _ = extract_observation_value(observation)

        # Extract provider information
        provider_id = None
        if "performer" in observation and observation["performer"]:
            # In a real implementation, this would map to a provider_id
            # For this sample, we'll leave it as None
            pass

        # Create Observation record
        omop_observation = {
            "observation_id": int(observation_id) if observation_id.isdigit() else None,  # Will be auto-assigned if None
            "person_id": person_id,
            "observation_concept_id": observation_concept_id,
            "observation_date": observation_datetime[:10] if observation_datetime else None,
            "observation_datetime": ob
(Content truncated due to size limit. Use line ranges to read in chunks)