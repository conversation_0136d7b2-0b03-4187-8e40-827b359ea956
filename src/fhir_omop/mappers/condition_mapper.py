"""
Condition to Condition_Occurrence mapping module for FHIR to OMOP transformation.

This module provides functions to transform FHIR Condition resources to OMOP Condition_Occurrence records.

Original Sources:
- OHDSI ETL-German-FHIR-Core: https://github.com/OHDSI/ETL-German-FHIR-Core/blob/main/src/main/java/org/miracum/etl/fhirtoomop/mapper/ConditionMapper.java
- NACHC-CAD/fhir-to-omop: https://github.com/NACHC-CAD/fhir-to-omop/tree/main/src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/condition
- Vulcan FHIR-to-OMOP Implementation Guide: https://build.fhir.org/ig/HL7/fhir-omop-ig/

References:
- FHIR Condition Resource: https://www.hl7.org/fhir/condition.html
- OMOP CDM Condition_Occurrence Table: https://ohdsi.github.io/CommonDataModel/cdm54.html#CONDITION_OCCURRENCE
"""
import logging
import pandas as pd
from datetime import datetime
from utils.db_utils import get_concept_id, get_source_concept_id, bulk_insert_dataframe
from utils.fhir_utils import (
    parse_fhir_datetime,
    get_code_from_codeableconcept,
    get_system_from_codeableconcept,
    get_display_from_codeableconcept,
    get_referenced_resource
)
from utils.config import CONCEPT_IDS, SOURCE_VOCABULARY_IDS

# Set up logging
logger = logging.getLogger(__name__)

def map_condition_code_to_concept_id(condition_code, system):
    """
    Map FHIR Condition.code to OMOP condition_concept_id.

    Args:
        condition_code (str): FHIR Condition.code value
        system (str): FHIR Condition.code.system value

    Returns:
        tuple: (condition_concept_id, condition_source_concept_id)
    """
    if not condition_code or not system:
        return 0, 0

    # Determine vocabulary ID from system
    vocabulary_id = None
    if "snomed" in system.lower():
        vocabulary_id = SOURCE_VOCABULARY_IDS["condition"]["snomed"]
    elif "icd-10" in system.lower() or "icd10" in system.lower():
        if "cm" in system.lower():
            vocabulary_id = SOURCE_VOCABULARY_IDS["condition"]["icd10cm"]
        else:
            vocabulary_id = SOURCE_VOCABULARY_IDS["condition"]["icd10"]
    elif "icd-9" in system.lower() or "icd9" in system.lower():
        if "cm" in system.lower():
            vocabulary_id = SOURCE_VOCABULARY_IDS["condition"]["icd9cm"]
        else:
            vocabulary_id = SOURCE_VOCABULARY_IDS["condition"]["icd9"]

    if not vocabulary_id:
        logger.warning(f"Unknown coding system: {system}, cannot map to vocabulary")
        return 0, 0

    # Get source concept ID
    source_concept_id = get_source_concept_id(condition_code, vocabulary_id)

    # Get standard concept ID
    concept_id = get_concept_id(condition_code, vocabulary_id, "Condition")

    return concept_id, source_concept_id

def map_condition_category_to_type_concept_id(category):
    """
    Map FHIR Condition.category to OMOP condition_type_concept_id.

    Args:
        category (list): FHIR Condition.category CodeableConcept list

    Returns:
        int: OMOP condition_type_concept_id
    """
    if not category:
        return CONCEPT_IDS["type"]["primary_condition"]

    # Check each category for known values
    for cat in category:
        if "coding" in cat:
            for coding in cat["coding"]:
                code = coding.get("code", "").lower()
                system = coding.get("system", "").lower()

                # Check for problem-list-item
                if code == "problem-list-item" or "problem" in code:
                    return CONCEPT_IDS["type"]["primary_condition"]

                # Check for encounter-diagnosis
                if code == "encounter-diagnosis" or "encounter" in code or "diagnosis" in code:
                    return CONCEPT_IDS["type"]["secondary_condition"]

    # Default to primary condition
    return CONCEPT_IDS["type"]["primary_condition"]

def extract_condition_dates(condition):
    """
    Extract onset and end dates from FHIR Condition resource.

    Args:
        condition (dict): FHIR Condition resource

    Returns:
        tuple: (start_datetime, end_datetime)
    """
    start_datetime = None
    end_datetime = None

    # Extract onset date
    if "onsetDateTime" in condition:
        start_datetime = parse_fhir_datetime(condition["onsetDateTime"])
    elif "onsetPeriod" in condition and "start" in condition["onsetPeriod"]:
        start_datetime = parse_fhir_datetime(condition["onsetPeriod"]["start"])

    # Extract end date (abatement)
    if "abatementDateTime" in condition:
        end_datetime = parse_fhir_datetime(condition["abatementDateTime"])
    elif "abatementPeriod" in condition and "end" in condition["abatementPeriod"]:
        end_datetime = parse_fhir_datetime(condition["abatementPeriod"]["end"])

    # If no explicit onset date, use recorded date
    if not start_datetime and "recordedDate" in condition:
        start_datetime = parse_fhir_datetime(condition["recordedDate"])

    return start_datetime, end_datetime

def map_condition_to_condition_occurrence(condition, person_id=None, visit_id=None):
    """
    Map FHIR Condition resource to OMOP Condition_Occurrence record.

    Args:
        condition (dict): FHIR Condition resource
        person_id (int, optional): OMOP person_id if already known
        visit_id (int, optional): OMOP visit_occurrence_id if already known

    Returns:
        dict: OMOP Condition_Occurrence record
    """
    if not condition or condition.get("resourceType") != "Condition":
        logger.error("Invalid Condition resource")
        return None

    try:
        # Extract condition ID
        condition_id = condition.get("id")
        if not condition_id:
            logger.error("Condition resource missing ID")
            return None

        # Get person_id from subject reference if not provided
        if not person_id and "subject" in condition and "reference" in condition["subject"]:
            subject_ref = condition["subject"]["reference"]
            if subject_ref.startswith("Patient/"):
                patient_id = subject_ref.split("/")[1]
                person_id = int(patient_id) if patient_id.isdigit() else None

        if not person_id:
            logger.error(f"Could not determine person_id for Condition {condition_id}")
            return None

        # Get visit_id from encounter reference if not provided
        if not visit_id and "encounter" in condition and "reference" in condition["encounter"]:
            encounter_ref = condition["encounter"]["reference"]
            if encounter_ref.startswith("Encounter/"):
                encounter_id = encounter_ref.split("/")[1]
                visit_id = int(encounter_id) if encounter_id.isdigit() else None

        # Extract condition code and system
        condition_concept_id = 0
        condition_source_concept_id = 0
        condition_source_value = None

        if "code" in condition:
            code = get_code_from_codeableconcept(condition["code"])
            system = get_system_from_codeableconcept(condition["code"])
            display = get_display_from_codeableconcept(condition["code"])

            if code and system:
                condition_concept_id, condition_source_concept_id = map_condition_code_to_concept_id(code, system)
                condition_source_value = f"{code}|{display}" if display else code

        # Extract condition type
        condition_type_concept_id = map_condition_category_to_type_concept_id(
            condition.get("category", [])
        )

        # Extract condition dates
        start_datetime, end_datetime = extract_condition_dates(condition)
        if not start_datetime:
            logger.warning(f"Condition {condition_id} missing onset date, using current date")
            start_datetime = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")

        # Extract provider information
        provider_id = None
        if "asserter" in condition and "reference" in condition["asserter"]:
            # In a real implementation, this would map to a provider_id
            # For this sample, we'll leave it as None
            pass

        # Create Condition_Occurrence record
        condition_occurrence = {
            "condition_occurrence_id": int(condition_id) if condition_id.isdigit() else None,  # Will be auto-assigned if None
            "person_id": person_id,
            "condition_concept_id": condition_concept_id,
            "condition_start_date": start_datetime[:10] if start_datetime else None,
            "condition_start_datetime": start_datetime,
            "condition_end_date": end_datetime[:10] if end_datetime else None,
            "condition_end_datetime": end_datetime,
            "condition_type_concept_id": condition_type_concept_id,
            "condition_status_concept_id": 0,  # Would require mapping from condition.clinicalStatus
            "stop_reason": None,  # Would require mapping from condition.abatementString
            "provider_id": provider_id,
            "visit_occurrence_id": visit_id,
            "visit_detail_id": None,
            "condition_source_value": condition_source_value,
            "condition_source_concept_id": condition_source_concept_id,
            "condition_status_source_value": condition.get("clinicalStatus", {}).get("coding", [{}])[0].get("code") if "clinicalStatus" in condition else None
        }

        return condition_occurrence

    except Exception as e:
        logger.error(f"Error mapping Condition to Condition_Occurrence: {e}")
        return None

def transform_conditions_to_condition_occurrences(conditions, person_id_map=None, visit_id_map=None):
    """
    Transform a list of FHIR Condition resources to OMOP Condition_Occurrence records.

    Args:
        conditions (list): List of FHIR Condition resources
        person_id_map (dict, optional): Mapping of patient IDs to person IDs
        visit_id_map (dict, optional): Mapping of encounter IDs to visit_occurrence IDs

    Returns:
        pandas.DataFrame: DataFrame of OMOP Condition_Occurrence records
    """
    condition_occurrences = []

    for condition in conditions:
        # Get person_id from map if available
        person_id = None
        if person_id_map and "subject" in condition and "reference" in condition["subject"]:
            subject_ref = condition["subject"]["reference"]
            if subject_ref.startswith("Patient/"):
                patient_id = subject_ref.split("/")[1]
                person_id = person_id_map.get(patient_id)

        # Get visit_id from map if available
        visit_id = None
        if visit_id_map and "encounter" in condition and "reference" in condition["encounter"]:
            encounter_ref = condition["encounter"]["reference"]
            if encounter_ref.startswith("Encounter/"):
                encounter_id = encounter_ref.split("/")[1]
                visit_id = visit_id_map.get(encounter_id)

        condition_occurrence = map_condition_to_condition_occurrence(condition, person_id, visit_id)
        if condition_occurrence:
            condition_occurrences.append(condition_occurrence)

    if not condition_occurrences:
        logger.warning("No conditions were successfully mapped to condition_occurrences")
        return pd.DataFrame()

    return pd.DataFrame(condition_occurrences)

def load_condition_occurrences_to_omop(condition_occurrences_df):
    """
    Load Condition_Occurrence records into OMOP CDM.

    Args:
        condition_occurrences_df (pandas.DataFrame): DataFrame of OMOP Condition_Occurrence records

    Returns:
        int: Number of records inserted
    """
    if condition_occurrences_df.empty:
        logger.warning("No Condition_Occurrence records to load")
        return 0

    try:
        # Insert Condition_Occurrence records
        rows_inserted = bulk_insert_dataframe(condition_occurrences_df, "condition_occurrence")
        logger.info(f"Inserted {rows_inserted} Condition_Occurrence records")
        return rows_inserted

    except Exception as e:
        logger.error(f"Error loading Condition_Occurrence records: {e}")
        raise
