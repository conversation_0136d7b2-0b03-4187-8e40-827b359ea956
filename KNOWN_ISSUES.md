# Known Issues and Pending Tasks

This document tracks known issues, limitations, and pending tasks in the FHIR-OMOP project.

## Database Configuration Issues

### [ISSUE-DB-001] HAPI FHIR PostgreSQL Dialect Configuration

**Status**: Open
**Priority**: Medium
**Component**: FHIR Server Database
**Created**: 2025-05-02

#### Description

Despite configuring the HAPI FHIR server to use the recommended PostgreSQL dialect (`ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect`), the server logs show the following warning:

```
WARN 1 --- [main] u.f.j.u.PartitionedIdModeVerificationSvc : Dialect is not a HAPI FHIR dialect: org.hibernate.dialect.PostgreSQLDialect, version: 14.17
```

This indicates that the server is using the standard Hibernate PostgreSQL dialect instead of the HAPI FHIR-specific dialect. While the server functions correctly for basic operations, this may affect performance with large datasets or complex queries.

#### Impact

- No immediate functional impact observed
- Potential performance degradation with large datasets
- Possible limitations in FHIR-specific query optimizations

#### Investigation Steps

1. Review HAPI FHIR documentation for alternative configuration methods
2. Check HAPI FHIR community forums and issue trackers for similar problems
3. Investigate if this is specific to the Docker image or version being used
4. Test with different configuration approaches:
   - Environment variables
   - Application properties file
   - Custom Docker image

#### Possible Solutions

1. Create a custom `application.yaml` file and mount it to the container
2. Build a custom Docker image with the correct dialect configuration
3. Investigate if upgrading/downgrading HAPI FHIR version resolves the issue
4. Contact HAPI FHIR community for guidance

#### References

- [HAPI FHIR Database Support Documentation](https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html)
- [HAPI FHIR Google Group](https://groups.google.com/g/hapi-fhir/) - Community forum for HAPI FHIR issues
- [Configuration in docker-compose-postgres.yml](servers/fhir-server/docker-compose-postgres.yml)
