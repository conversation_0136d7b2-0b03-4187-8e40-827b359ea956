"""
Visualization Dashboard UI Components for the FHIR Server Demo Streamlit application.

This module provides comprehensive visualization dashboards for analyzing FHIR data
with a focus on clinical and epidemiological insights. It includes:

1. Database Overview: Resource counts, patient demographics, and data timeline
2. Clinical Insights: Diagnoses prevalence, lab measurements, and vital signs trends
3. Epidemiological Analysis: Condition prevalence by demographics and temporal patterns

The visualizations are designed to support clinical data scientists and researchers
in understanding data patterns and generating insights from healthcare data.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import logging
import sys
from pathlib import Path
import time

# Add the parent directory to the Python path to import utils
sys.path.append(str(Path(__file__).parent.parent))

# Import utility functions
from utils import execute_fhir_query

# Create a logger for this module
logger = logging.getLogger(__name__)

# Cache configuration
CACHE_TTL = 300  # Cache time-to-live in seconds (5 minutes)

# Cache decorator with TTL
def ttl_cache(ttl=CACHE_TTL):
    """
    Cache decorator with time-to-live (TTL) functionality.

    Args:
        ttl (int): Time to live in seconds

    Returns:
        Decorated function with caching
    """
    def wrapper(func):
        # Create cache for results
        cache = {}
        # Create cache for timestamps
        timestamps = {}

        def wrapped(*args, **kwargs):
            # Create a key from the function arguments
            key = str(args) + str(kwargs)

            # Check if key is in cache and not expired
            current_time = time.time()
            if key in cache and current_time - timestamps[key] < ttl:
                return cache[key]

            # Call the function and cache the result
            result = func(*args, **kwargs)
            cache[key] = result
            timestamps[key] = current_time

            return result

        return wrapped

    return wrapper

def render_visualization_tab():
    """
    Render the Dashboard tab in the Streamlit application.

    This function creates a comprehensive dashboard with multiple visualizations
    to analyze FHIR data from clinical and epidemiological perspectives.
    """
    st.markdown("Explore comprehensive visualizations of the FHIR database.")

    # Create tabs for different visualization categories
    visualization_tabs = st.tabs([
        "Database Overview",
        "Clinical Insights",
        "Epidemiological Analysis"
    ])

    # Tab 1: Database Overview
    with visualization_tabs[0]:
        render_database_overview()

    # Tab 2: Clinical Insights
    with visualization_tabs[1]:
        render_clinical_insights()

    # Tab 3: Epidemiological Analysis
    with visualization_tabs[2]:
        render_epidemiological_analysis()

def render_database_overview():
    """
    Render the Database Overview dashboard.

    This dashboard provides a high-level overview of the FHIR database, including:
    - Resource counts by type
    - Patient demographics
    - Data timeline
    """

    # Create columns for metrics
    col1, col2, col3 = st.columns(3)

    with st.spinner("Loading database statistics..."):
        try:
            # Get total patient count
            success, patient_result = execute_fhir_query(
                st.session_state.server_url,
                "Patient?_summary=count"
            )

            if success and "total" in patient_result:
                with col1:
                    st.metric("Total Patients", patient_result["total"])

            # Get total observation count
            success, observation_result = execute_fhir_query(
                st.session_state.server_url,
                "Observation?_summary=count"
            )

            if success and "total" in observation_result:
                with col2:
                    st.metric("Total Observations", observation_result["total"])

            # Get total condition count
            success, condition_result = execute_fhir_query(
                st.session_state.server_url,
                "Condition?_summary=count"
            )

            if success and "total" in condition_result:
                with col3:
                    st.metric("Total Conditions", condition_result["total"])

            # 1. Resource counts by type (bar chart)
            st.subheader("Resource Distribution")

            # Get resource counts
            resource_counts_df = get_resource_counts()

            if not resource_counts_df.empty:
                # Create and display the chart with multiple visualization options
                st.markdown("#### Resource Counts Visualization")
                create_resource_count_chart(resource_counts_df)
                # No need to display the chart here as it's now handled inside the function

            # 2. Patient demographics
            st.subheader("Patient Demographics")

            # Create two columns for gender and age distribution
            demo_col1, demo_col2 = st.columns(2)

            with demo_col1:
                st.markdown("#### Gender Distribution")

                # Get gender distribution
                success, gender_result = execute_fhir_query(
                    st.session_state.server_url,
                    "Patient?_summary=count&gender=male"
                )

                male_count = gender_result.get("total", 0) if success and "total" in gender_result else 0

                success, gender_result = execute_fhir_query(
                    st.session_state.server_url,
                    "Patient?_summary=count&gender=female"
                )

                female_count = gender_result.get("total", 0) if success and "total" in gender_result else 0

                # Create gender distribution pie chart
                gender_df = pd.DataFrame({
                    "Gender": ["Male", "Female", "Other/Unknown"],
                    "Count": [male_count, female_count,
                             patient_result.get("total", 0) - male_count - female_count
                             if "total" in patient_result else 0]
                })

                # Only show the chart if we have data
                if gender_df["Count"].sum() > 0:
                    fig = px.pie(
                        gender_df,
                        values="Count",
                        names="Gender",
                        title="Patient Gender Distribution",
                        color_discrete_sequence=px.colors.qualitative.Pastel
                    )
                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.info("No gender data available")

            with demo_col2:
                st.markdown("#### Age Distribution")

                # Get patients with birth dates
                success, patients_with_dob = execute_fhir_query(
                    st.session_state.server_url,
                    "Patient?_elements=birthDate&_count=100"
                )

                if success and "entry" in patients_with_dob:
                    # Extract birth dates and calculate ages
                    from datetime import datetime
                    current_year = datetime.now().year

                    ages = []
                    for entry in patients_with_dob["entry"]:
                        if "resource" in entry and "birthDate" in entry["resource"]:
                            try:
                                birth_year = int(entry["resource"]["birthDate"][:4])
                                age = current_year - birth_year
                                if 0 <= age <= 120:  # Basic validation
                                    ages.append(age)
                            except (ValueError, TypeError):
                                continue

                    if ages:
                        # Create age histogram
                        age_df = pd.DataFrame({"Age": ages})
                        fig = px.histogram(
                            age_df,
                            x="Age",
                            nbins=20,
                            title="Patient Age Distribution",
                            labels={"Age": "Age (years)", "count": "Number of Patients"},
                            color_discrete_sequence=px.colors.qualitative.Pastel
                        )

                        # Add mean age line
                        mean_age = sum(ages) / len(ages)
                        fig.add_vline(x=mean_age, line_dash="dash", line_color="red",
                                     annotation_text=f"Mean: {mean_age:.1f} years",
                                     annotation_position="top right")

                        st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.info("No age data available")
                else:
                    st.info("No age data available")

            # 3. Data timeline
            st.subheader("Data Timeline")

            # Get observations with dates
            success, observations_with_date = execute_fhir_query(
                st.session_state.server_url,
                "Observation?_elements=effectiveDateTime&_count=500&_sort=date"
            )

            if success and "entry" in observations_with_date:
                # Extract dates
                dates = []
                for entry in observations_with_date["entry"]:
                    if "resource" in entry and "effectiveDateTime" in entry["resource"]:
                        try:
                            date_str = entry["resource"]["effectiveDateTime"]
                            # Just keep the date part (YYYY-MM-DD)
                            if "T" in date_str:
                                date_str = date_str.split("T")[0]
                            dates.append(date_str)
                        except (ValueError, TypeError):
                            continue

                if dates:
                    # Count observations by date
                    from collections import Counter
                    date_counts = Counter(dates)

                    # Create timeline dataframe
                    timeline_df = pd.DataFrame({
                        "Date": list(date_counts.keys()),
                        "Count": list(date_counts.values())
                    })

                    # Sort by date
                    timeline_df["Date"] = pd.to_datetime(timeline_df["Date"])
                    timeline_df = timeline_df.sort_values("Date")

                    # Create timeline chart
                    fig = px.line(
                        timeline_df,
                        x="Date",
                        y="Count",
                        title="Observations Over Time",
                        labels={"Date": "Date", "Count": "Number of Observations"},
                        markers=True
                    )

                    st.plotly_chart(fig, use_container_width=True)
                else:
                    st.info("No timeline data available")
            else:
                st.info("No timeline data available")

        except Exception as e:
            st.error(f"Error loading database statistics: {str(e)}")
            logger.error(f"Error in database overview: {str(e)}")

def render_clinical_insights():
    """
    Render the Clinical Insights dashboard.

    This dashboard provides clinical insights from the FHIR data, including:
    - Top diagnoses and their prevalence
    - Common lab measurements and their distributions
    - Vital signs trends and outliers
    """

    # Create tabs for different clinical insights
    clinical_tabs = st.tabs([
        "Diagnoses",
        "Lab Measurements",
        "Vital Signs"
    ])

    # Tab 1: Diagnoses
    with clinical_tabs[0]:
        st.markdown("### Top Diagnoses")

        with st.spinner("Loading condition data..."):
            try:
                # Get conditions with code information
                success, conditions = execute_fhir_query(
                    st.session_state.server_url,
                    "Condition?_elements=code,clinicalStatus&_count=200"
                )

                if success and "entry" in conditions and conditions["entry"]:
                    # Extract condition codes and names
                    condition_data = []

                    for entry in conditions["entry"]:
                        if "resource" in entry and "code" in entry["resource"]:
                            code_obj = entry["resource"]["code"]

                            # Get condition name from coding or text
                            condition_name = "Unknown"
                            condition_code = "Unknown"

                            if "coding" in code_obj and code_obj["coding"]:
                                coding = code_obj["coding"][0]
                                condition_name = coding.get("display", "Unknown")
                                condition_code = coding.get("code", "Unknown")
                            elif "text" in code_obj:
                                condition_name = code_obj["text"]

                            # Get clinical status
                            clinical_status = "Unknown"
                            if "clinicalStatus" in entry["resource"] and "coding" in entry["resource"]["clinicalStatus"]:
                                status_coding = entry["resource"]["clinicalStatus"]["coding"][0]
                                clinical_status = status_coding.get("code", "Unknown")

                            condition_data.append({
                                "name": condition_name,
                                "code": condition_code,
                                "status": clinical_status
                            })

                    if condition_data:
                        # Convert to DataFrame
                        condition_df = pd.DataFrame(condition_data)

                        # Count conditions by name
                        condition_counts = condition_df["name"].value_counts().reset_index()
                        condition_counts.columns = ["Condition", "Count"]

                        # Get top 15 conditions
                        top_conditions = condition_counts.head(15)

                        # Create bar chart
                        fig = px.bar(
                            top_conditions,
                            x="Count",
                            y="Condition",
                            title="Top 15 Diagnoses",
                            orientation="h",
                            labels={"Count": "Number of Patients", "Condition": "Diagnosis"},
                            color_discrete_sequence=px.colors.qualitative.Pastel
                        )

                        # Improve layout
                        fig.update_layout(
                            yaxis={"categoryorder": "total ascending"},
                            height=500
                        )

                        st.plotly_chart(fig, use_container_width=True)

                        # Clinical status breakdown
                        st.markdown("### Clinical Status Distribution")

                        # Count by status
                        status_counts = condition_df["status"].value_counts().reset_index()
                        status_counts.columns = ["Status", "Count"]

                        # Create pie chart
                        fig = px.pie(
                            status_counts,
                            values="Count",
                            names="Status",
                            title="Distribution of Clinical Statuses",
                            hole=0.4,
                            color_discrete_sequence=px.colors.qualitative.Pastel
                        )

                        st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.info("No condition data available")
                else:
                    st.info("No condition data available")
            except Exception as e:
                st.error(f"Error loading condition data: {str(e)}")
                logger.error(f"Error in clinical insights - diagnoses: {str(e)}")

    # Tab 2: Lab Measurements
    with clinical_tabs[1]:
        st.markdown("### Lab Measurement Distributions")

        with st.spinner("Loading lab measurement data..."):
            try:
                # Get common lab measurements
                lab_codes = {
                    "Glucose": "2339-0",
                    "Hemoglobin A1c": "4548-4",
                    "Cholesterol": "2093-3",
                    "HDL Cholesterol": "2085-9",
                    "LDL Cholesterol": "18262-6",
                    "Triglycerides": "2571-8",
                    "Creatinine": "2160-0",
                    "Sodium": "2951-2",
                    "Potassium": "2823-3"
                }

                # Create a selectbox for lab test selection
                selected_lab = st.selectbox(
                    "Select Lab Test:",
                    list(lab_codes.keys())
                )

                if selected_lab:
                    # Get observations for the selected lab test
                    loinc_code = lab_codes[selected_lab]

                    success, lab_results = execute_fhir_query(
                        st.session_state.server_url,
                        f"Observation?code=http://loinc.org|{loinc_code}&_count=200"
                    )

                    if success and "entry" in lab_results and lab_results["entry"]:
                        # Extract values
                        values = []
                        dates = []

                        for entry in lab_results["entry"]:
                            if "resource" in entry:
                                resource = entry["resource"]

                                # Get value
                                if "valueQuantity" in resource:
                                    value = resource["valueQuantity"].get("value")
                                    unit = resource["valueQuantity"].get("unit", "")

                                    if value is not None:
                                        values.append(float(value))

                                        # Get date if available
                                        if "effectiveDateTime" in resource:
                                            date_str = resource["effectiveDateTime"]
                                            if "T" in date_str:
                                                date_str = date_str.split("T")[0]
                                            dates.append(date_str)
                                        else:
                                            dates.append(None)

                        if values:
                            # Create DataFrame
                            lab_df = pd.DataFrame({
                                "Value": values,
                                "Date": dates
                            })

                            # Convert dates to datetime
                            lab_df["Date"] = pd.to_datetime(lab_df["Date"], errors="coerce")

                            # Create distribution histogram
                            fig = px.histogram(
                                lab_df,
                                x="Value",
                                nbins=20,
                                title=f"{selected_lab} Distribution",
                                labels={"Value": f"Value {unit}", "count": "Frequency"},
                                color_discrete_sequence=px.colors.qualitative.Pastel
                            )

                            # Add reference range if available
                            if selected_lab == "Glucose":
                                fig.add_vrect(x0=70, x1=100, fillcolor="green", opacity=0.2,
                                             annotation_text="Normal Range", annotation_position="top left")
                            elif selected_lab == "Hemoglobin A1c":
                                fig.add_vrect(x0=4.0, x1=5.7, fillcolor="green", opacity=0.2,
                                             annotation_text="Normal Range", annotation_position="top left")

                            st.plotly_chart(fig, use_container_width=True)

                            # Create time series if dates are available
                            if not lab_df["Date"].isna().all():
                                lab_df_with_dates = lab_df.dropna(subset=["Date"])
                                lab_df_with_dates = lab_df_with_dates.sort_values("Date")

                                fig = px.line(
                                    lab_df_with_dates,
                                    x="Date",
                                    y="Value",
                                    title=f"{selected_lab} Trends Over Time",
                                    labels={"Value": f"Value {unit}", "Date": "Date"},
                                    markers=True
                                )

                                st.plotly_chart(fig, use_container_width=True)

                                # Basic statistics
                                st.markdown("### Statistical Summary")

                                col1, col2, col3, col4 = st.columns(4)
                                with col1:
                                    st.metric("Mean", f"{lab_df['Value'].mean():.2f}")
                                with col2:
                                    st.metric("Median", f"{lab_df['Value'].median():.2f}")
                                with col3:
                                    st.metric("Min", f"{lab_df['Value'].min():.2f}")
                                with col4:
                                    st.metric("Max", f"{lab_df['Value'].max():.2f}")
                        else:
                            st.info(f"No {selected_lab} measurements found")
                    else:
                        st.info(f"No {selected_lab} measurements found")
            except Exception as e:
                st.error(f"Error loading lab measurement data: {str(e)}")
                logger.error(f"Error in clinical insights - lab measurements: {str(e)}")

    # Tab 3: Vital Signs
    with clinical_tabs[2]:
        st.markdown("### Vital Signs Trends")

        with st.spinner("Loading vital signs data..."):
            try:
                # Common vital signs
                vital_signs = {
                    "Blood Pressure": "85354-9",
                    "Heart Rate": "8867-4",
                    "Respiratory Rate": "9279-1",
                    "Body Temperature": "8310-5",
                    "Oxygen Saturation": "59408-5",
                    "Body Weight": "29463-7",
                    "Body Height": "8302-2",
                    "BMI": "39156-5"
                }

                # Create a selectbox for vital sign selection
                selected_vital = st.selectbox(
                    "Select Vital Sign:",
                    list(vital_signs.keys())
                )

                if selected_vital:
                    # Get observations for the selected vital sign
                    loinc_code = vital_signs[selected_vital]

                    success, vital_results = execute_fhir_query(
                        st.session_state.server_url,
                        f"Observation?code=http://loinc.org|{loinc_code}&_count=200"
                    )

                    if success and "entry" in vital_results and vital_results["entry"]:
                        # Special handling for blood pressure (has components)
                        if selected_vital == "Blood Pressure":
                            # Extract systolic and diastolic values
                            bp_data = []

                            for entry in vital_results["entry"]:
                                if "resource" in entry and "component" in entry["resource"]:
                                    resource = entry["resource"]

                                    # Get date
                                    date_str = None
                                    if "effectiveDateTime" in resource:
                                        date_str = resource["effectiveDateTime"]
                                        if "T" in date_str:
                                            date_str = date_str.split("T")[0]

                                    # Get patient
                                    patient_id = None
                                    if "subject" in resource and "reference" in resource["subject"]:
                                        patient_ref = resource["subject"]["reference"]
                                        if patient_ref.startswith("Patient/"):
                                            patient_id = patient_ref.replace("Patient/", "")

                                    # Extract systolic and diastolic
                                    systolic = None
                                    diastolic = None

                                    for component in resource["component"]:
                                        if "code" in component and "coding" in component["code"]:
                                            for coding in component["code"]["coding"]:
                                                code = coding.get("code")
                                                if code == "8480-6" and "valueQuantity" in component:  # Systolic
                                                    systolic = component["valueQuantity"].get("value")
                                                elif code == "8462-4" and "valueQuantity" in component:  # Diastolic
                                                    diastolic = component["valueQuantity"].get("value")

                                    if systolic is not None and diastolic is not None:
                                        bp_data.append({
                                            "Date": date_str,
                                            "Patient": patient_id,
                                            "Systolic": float(systolic),
                                            "Diastolic": float(diastolic)
                                        })

                            if bp_data:
                                # Create DataFrame
                                bp_df = pd.DataFrame(bp_data)

                                # Convert dates to datetime
                                bp_df["Date"] = pd.to_datetime(bp_df["Date"], errors="coerce")

                                # Create scatter plot
                                fig = px.scatter(
                                    bp_df,
                                    x="Systolic",
                                    y="Diastolic",
                                    title="Blood Pressure Measurements",
                                    labels={"Systolic": "Systolic (mmHg)", "Diastolic": "Diastolic (mmHg)"},
                                    color_discrete_sequence=px.colors.qualitative.Pastel
                                )

                                # Add reference ranges
                                fig.add_shape(
                                    type="rect",
                                    x0=90, x1=120,
                                    y0=60, y1=80,
                                    fillcolor="green", opacity=0.2,
                                    line=dict(width=0),
                                    name="Normal"
                                )

                                fig.add_shape(
                                    type="rect",
                                    x0=120, x1=140,
                                    y0=80, y1=90,
                                    fillcolor="yellow", opacity=0.2,
                                    line=dict(width=0),
                                    name="Elevated"
                                )

                                fig.add_shape(
                                    type="rect",
                                    x0=140, x1=180,
                                    y0=90, y1=120,
                                    fillcolor="orange", opacity=0.2,
                                    line=dict(width=0),
                                    name="Stage 1"
                                )

                                fig.add_shape(
                                    type="rect",
                                    x0=180, x1=200,
                                    y0=120, y1=130,
                                    fillcolor="red", opacity=0.2,
                                    line=dict(width=0),
                                    name="Stage 2"
                                )

                                # Add annotations
                                fig.add_annotation(
                                    x=105, y=70,
                                    text="Normal",
                                    showarrow=False,
                                    font=dict(color="black")
                                )

                                fig.add_annotation(
                                    x=130, y=85,
                                    text="Elevated",
                                    showarrow=False,
                                    font=dict(color="black")
                                )

                                fig.add_annotation(
                                    x=160, y=105,
                                    text="Stage 1 Hypertension",
                                    showarrow=False,
                                    font=dict(color="black")
                                )

                                fig.add_annotation(
                                    x=190, y=125,
                                    text="Stage 2",
                                    showarrow=False,
                                    font=dict(color="black")
                                )

                                st.plotly_chart(fig, use_container_width=True)

                                # Time series if dates are available
                                if not bp_df["Date"].isna().all():
                                    bp_df_with_dates = bp_df.dropna(subset=["Date"])
                                    bp_df_with_dates = bp_df_with_dates.sort_values("Date")

                                    # Create a figure with two y-axes
                                    fig = go.Figure()

                                    # Add systolic line
                                    fig.add_trace(go.Scatter(
                                        x=bp_df_with_dates["Date"],
                                        y=bp_df_with_dates["Systolic"],
                                        mode="lines+markers",
                                        name="Systolic",
                                        line=dict(color="red")
                                    ))

                                    # Add diastolic line
                                    fig.add_trace(go.Scatter(
                                        x=bp_df_with_dates["Date"],
                                        y=bp_df_with_dates["Diastolic"],
                                        mode="lines+markers",
                                        name="Diastolic",
                                        line=dict(color="blue")
                                    ))

                                    # Update layout
                                    fig.update_layout(
                                        title="Blood Pressure Trends Over Time",
                                        xaxis_title="Date",
                                        yaxis_title="Blood Pressure (mmHg)",
                                        legend_title="Measurement",
                                        hovermode="x unified"
                                    )

                                    st.plotly_chart(fig, use_container_width=True)
                            else:
                                st.info("No blood pressure measurements found")
                        else:
                            # Handle other vital signs
                            vital_data = []

                            for entry in vital_results["entry"]:
                                if "resource" in entry:
                                    resource = entry["resource"]

                                    # Get value
                                    if "valueQuantity" in resource:
                                        value = resource["valueQuantity"].get("value")
                                        unit = resource["valueQuantity"].get("unit", "")

                                        if value is not None:
                                            # Get date
                                            date_str = None
                                            if "effectiveDateTime" in resource:
                                                date_str = resource["effectiveDateTime"]
                                                if "T" in date_str:
                                                    date_str = date_str.split("T")[0]

                                            # Get patient
                                            patient_id = None
                                            if "subject" in resource and "reference" in resource["subject"]:
                                                patient_ref = resource["subject"]["reference"]
                                                if patient_ref.startswith("Patient/"):
                                                    patient_id = patient_ref.replace("Patient/", "")

                                            vital_data.append({
                                                "Date": date_str,
                                                "Patient": patient_id,
                                                "Value": float(value),
                                                "Unit": unit
                                            })

                            if vital_data:
                                # Create DataFrame
                                vital_df = pd.DataFrame(vital_data)

                                # Convert dates to datetime
                                vital_df["Date"] = pd.to_datetime(vital_df["Date"], errors="coerce")

                                # Create distribution histogram
                                fig = px.histogram(
                                    vital_df,
                                    x="Value",
                                    nbins=20,
                                    title=f"{selected_vital} Distribution",
                                    labels={"Value": f"Value ({vital_df['Unit'].iloc[0]})", "count": "Frequency"},
                                    color_discrete_sequence=px.colors.qualitative.Pastel
                                )

                                # Add reference ranges based on vital sign
                                if selected_vital == "Heart Rate":
                                    fig.add_vrect(x0=60, x1=100, fillcolor="green", opacity=0.2,
                                                 annotation_text="Normal Range", annotation_position="top left")
                                elif selected_vital == "Body Temperature":
                                    fig.add_vrect(x0=36.5, x1=37.5, fillcolor="green", opacity=0.2,
                                                 annotation_text="Normal Range", annotation_position="top left")
                                elif selected_vital == "Oxygen Saturation":
                                    fig.add_vrect(x0=95, x1=100, fillcolor="green", opacity=0.2,
                                                 annotation_text="Normal Range", annotation_position="top left")

                                st.plotly_chart(fig, use_container_width=True)

                                # Time series if dates are available
                                if not vital_df["Date"].isna().all():
                                    vital_df_with_dates = vital_df.dropna(subset=["Date"])
                                    vital_df_with_dates = vital_df_with_dates.sort_values("Date")

                                    # Group by patient if multiple patients
                                    if vital_df_with_dates["Patient"].nunique() > 1:
                                        fig = px.line(
                                            vital_df_with_dates,
                                            x="Date",
                                            y="Value",
                                            color="Patient",
                                            title=f"{selected_vital} Trends Over Time",
                                            labels={"Value": f"Value ({vital_df['Unit'].iloc[0]})", "Date": "Date"},
                                            markers=True
                                        )
                                    else:
                                        fig = px.line(
                                            vital_df_with_dates,
                                            x="Date",
                                            y="Value",
                                            title=f"{selected_vital} Trends Over Time",
                                            labels={"Value": f"Value ({vital_df['Unit'].iloc[0]})", "Date": "Date"},
                                            markers=True
                                        )

                                    st.plotly_chart(fig, use_container_width=True)

                                    # Detect outliers
                                    if len(vital_df_with_dates) >= 5:  # Need enough data for outlier detection
                                        st.markdown("### Outlier Detection")

                                        # Calculate z-scores
                                        mean = vital_df_with_dates["Value"].mean()
                                        std = vital_df_with_dates["Value"].std()
                                        vital_df_with_dates["z_score"] = (vital_df_with_dates["Value"] - mean) / std

                                        # Flag outliers (|z| > 2)
                                        vital_df_with_dates["is_outlier"] = abs(vital_df_with_dates["z_score"]) > 2

                                        # Create scatter plot with outliers highlighted
                                        fig = px.scatter(
                                            vital_df_with_dates,
                                            x="Date",
                                            y="Value",
                                            color="is_outlier",
                                            title=f"{selected_vital} Outliers",
                                            labels={"Value": f"Value ({vital_df['Unit'].iloc[0]})", "Date": "Date", "is_outlier": "Is Outlier"},
                                            color_discrete_map={True: "red", False: "blue"}
                                        )

                                        st.plotly_chart(fig, use_container_width=True)

                                        # Show outlier data
                                        outliers = vital_df_with_dates[vital_df_with_dates["is_outlier"]]
                                        if not outliers.empty:
                                            st.markdown(f"**{len(outliers)} outliers detected:**")
                                            st.dataframe(outliers[["Date", "Value", "z_score"]].reset_index(drop=True))
                                        else:
                                            st.info("No outliers detected")
                            else:
                                st.info(f"No {selected_vital} measurements found")
                    else:
                        st.info(f"No {selected_vital} measurements found")
            except Exception as e:
                st.error(f"Error loading vital signs data: {str(e)}")
                logger.error(f"Error in clinical insights - vital signs: {str(e)}")

def render_epidemiological_analysis():
    """
    Render the Epidemiological Analysis dashboard.

    This dashboard provides epidemiological analysis of the FHIR data, including:
    - Condition prevalence by demographic factors
    - Temporal patterns in diagnoses
    - Comorbidity analysis
    """

    # Create tabs for different epidemiological analyses
    epi_tabs = st.tabs([
        "Prevalence by Demographics",
        "Temporal Patterns",
        "Comorbidity Analysis"
    ])

    # Tab 1: Prevalence by Demographics
    with epi_tabs[0]:

        with st.spinner("Loading demographic prevalence data..."):
            try:
                # Get conditions with patient references
                success, conditions = execute_fhir_query(
                    st.session_state.server_url,
                    "Condition?_include=Condition:patient&_count=200"
                )

                if success and "entry" in conditions and conditions["entry"]:
                    # Extract condition and patient data
                    condition_data = []
                    patient_data = {}

                    # First, extract patient data
                    for entry in conditions["entry"]:
                        if "resource" in entry and entry["resource"]["resourceType"] == "Patient":
                            patient = entry["resource"]
                            patient_id = patient.get("id", "")

                            # Extract gender
                            gender = patient.get("gender", "unknown")

                            # Extract age
                            age = None
                            if "birthDate" in patient:
                                try:
                                    from datetime import datetime
                                    birth_year = int(patient["birthDate"][:4])
                                    current_year = datetime.now().year
                                    age = current_year - birth_year
                                except (ValueError, TypeError):
                                    pass

                            # Store patient data
                            patient_data[patient_id] = {
                                "gender": gender,
                                "age": age
                            }

                    # Then, extract condition data with patient references
                    for entry in conditions["entry"]:
                        if "resource" in entry and entry["resource"]["resourceType"] == "Condition":
                            condition = entry["resource"]

                            # Get condition name
                            condition_name = "Unknown"
                            if "code" in condition and "coding" in condition["code"] and condition["code"]["coding"]:
                                coding = condition["code"]["coding"][0]
                                condition_name = coding.get("display", "Unknown")
                            elif "code" in condition and "text" in condition["code"]:
                                condition_name = condition["code"]["text"]

                            # Get patient reference
                            patient_id = None
                            if "subject" in condition and "reference" in condition["subject"]:
                                patient_ref = condition["subject"]["reference"]
                                if patient_ref.startswith("Patient/"):
                                    patient_id = patient_ref.replace("Patient/", "")

                            # Get patient demographics if available
                            gender = "unknown"
                            age_group = "unknown"

                            if patient_id and patient_id in patient_data:
                                gender = patient_data[patient_id]["gender"]

                                # Create age groups
                                age = patient_data[patient_id]["age"]
                                if age is not None:
                                    if age < 18:
                                        age_group = "<18"
                                    elif age < 30:
                                        age_group = "18-29"
                                    elif age < 45:
                                        age_group = "30-44"
                                    elif age < 60:
                                        age_group = "45-59"
                                    elif age < 75:
                                        age_group = "60-74"
                                    else:
                                        age_group = "75+"

                            condition_data.append({
                                "condition": condition_name,
                                "patient_id": patient_id,
                                "gender": gender,
                                "age_group": age_group
                            })

                    if condition_data:
                        # Convert to DataFrame
                        condition_df = pd.DataFrame(condition_data)

                        # Create filters for condition selection
                        # Get top conditions for the filter
                        top_conditions = condition_df["condition"].value_counts().head(10).index.tolist()

                        selected_condition = st.selectbox(
                            "Select Condition for Analysis:",
                            ["All Conditions"] + top_conditions
                        )

                        # Filter data based on selection
                        if selected_condition != "All Conditions":
                            filtered_df = condition_df[condition_df["condition"] == selected_condition]
                            title_prefix = selected_condition
                        else:
                            filtered_df = condition_df
                            title_prefix = "All Conditions"

                        # Create two columns for gender and age group analysis
                        demo_col1, demo_col2 = st.columns(2)

                        with demo_col1:
                            st.markdown(f"#### {title_prefix} by Gender")

                            # Count by gender
                            gender_counts = filtered_df["gender"].value_counts().reset_index()
                            gender_counts.columns = ["Gender", "Count"]

                            # Create pie chart
                            fig = px.pie(
                                gender_counts,
                                values="Count",
                                names="Gender",
                                title=f"{title_prefix} Distribution by Gender",
                                color_discrete_sequence=px.colors.qualitative.Pastel
                            )

                            st.plotly_chart(fig, use_container_width=True)

                        with demo_col2:
                            st.markdown(f"#### {title_prefix} by Age Group")

                            # Count by age group
                            age_counts = filtered_df["age_group"].value_counts().reset_index()
                            age_counts.columns = ["Age Group", "Count"]

                            # Sort age groups in correct order
                            age_order = ["<18", "18-29", "30-44", "45-59", "60-74", "75+", "unknown"]
                            age_counts["Age Group"] = pd.Categorical(
                                age_counts["Age Group"],
                                categories=age_order,
                                ordered=True
                            )
                            age_counts = age_counts.sort_values("Age Group")

                            # Create bar chart
                            fig = px.bar(
                                age_counts,
                                x="Age Group",
                                y="Count",
                                title=f"{title_prefix} Distribution by Age Group",
                                color="Age Group",
                                color_discrete_sequence=px.colors.qualitative.Pastel
                            )

                            st.plotly_chart(fig, use_container_width=True)

                        # Create heatmap of conditions by age and gender
                        st.markdown("#### Condition Prevalence by Demographics")

                        # Get top 5 conditions for heatmap
                        top_5_conditions = condition_df["condition"].value_counts().head(5).index.tolist()

                        # Filter to top conditions
                        top_conditions_df = condition_df[condition_df["condition"].isin(top_5_conditions)]

                        # Create pivot table
                        if "age_group" in top_conditions_df.columns and "gender" in top_conditions_df.columns:
                            # Create a copy of the dataframe to avoid SettingWithCopyWarning
                            top_conditions_df = top_conditions_df.copy()
                            # Add a combined column for better visualization
                            top_conditions_df["condition_gender"] = top_conditions_df["condition"] + " (" + top_conditions_df["gender"].str.capitalize() + ")"

                            # Group by combined condition-gender, and age_group
                            heatmap_data = top_conditions_df.groupby(["condition_gender", "age_group"]).size().reset_index(name="count")

                            # Create a single combined heatmap
                            if not heatmap_data.empty:
                                # Create pivot table
                                pivot_data = heatmap_data.pivot_table(
                                    values="count",
                                    index="condition_gender",
                                    columns="age_group",
                                    fill_value=0
                                )

                                # Sort age groups
                                age_cols = [col for col in age_order if col in pivot_data.columns]
                                pivot_data = pivot_data[age_cols]

                                # Sort conditions by gender and then by name
                                pivot_data = pivot_data.reindex(sorted(pivot_data.index, key=lambda x: (x.split("(")[1], x.split("(")[0])))

                                # Create heatmap
                                fig = px.imshow(
                                    pivot_data,
                                    labels=dict(x="Age Group", y="Condition by Gender", color="Count"),
                                    title="Condition Prevalence by Age Group and Gender",
                                    color_continuous_scale="YlOrRd",
                                    aspect="auto"  # Adjust aspect ratio automatically
                                )

                                # Improve layout
                                fig.update_layout(
                                    xaxis={"title": "Age Group"},
                                    yaxis={"title": "Condition (Gender)"},
                                    height=400,  # Compact height
                                    margin=dict(l=10, r=10, t=40, b=10)  # Reduce margins
                                )

                                # Add hover template for better information
                                fig.update_traces(
                                    hovertemplate="<b>%{y}</b><br>Age Group: %{x}<br>Count: %{z}<extra></extra>"
                                )

                                st.plotly_chart(fig, use_container_width=True)

                                # Add explanatory text
                                st.markdown("""
                                <div style="background-color: rgba(0,0,0,0.05); padding: 10px; border-radius: 5px; font-size: 0.9em;">
                                <b>How to interpret:</b> This heatmap shows the prevalence of top conditions across different age groups and genders.
                                Darker colors indicate higher prevalence. The visualization helps identify patterns in how conditions affect
                                different demographic groups, which can inform targeted interventions and resource allocation.
                                </div>
                                """, unsafe_allow_html=True)
                    else:
                        st.info("No condition data with patient demographics available")
                else:
                    st.info("No condition data available")
            except Exception as e:
                st.error(f"Error loading demographic prevalence data: {str(e)}")
                logger.error(f"Error in epidemiological analysis - demographics: {str(e)}")

    # Tab 2: Temporal Patterns
    with epi_tabs[1]:
        st.markdown("### Temporal Patterns in Diagnoses")

        with st.spinner("Loading temporal pattern data..."):
            try:
                # Get conditions with onset dates
                success, conditions = execute_fhir_query(
                    st.session_state.server_url,
                    "Condition?_elements=code,onsetDateTime,recordedDate&_count=200"
                )

                if success and "entry" in conditions and conditions["entry"]:
                    # Extract condition and date data
                    temporal_data = []

                    for entry in conditions["entry"]:
                        if "resource" in entry:
                            condition = entry["resource"]

                            # Get condition name
                            condition_name = "Unknown"
                            if "code" in condition and "coding" in condition["code"] and condition["code"]["coding"]:
                                coding = condition["code"]["coding"][0]
                                condition_name = coding.get("display", "Unknown")
                            elif "code" in condition and "text" in condition["code"]:
                                condition_name = condition["code"]["text"]

                            # Get date (prefer onset date, fall back to recorded date)
                            date_str = None
                            date_type = None

                            if "onsetDateTime" in condition:
                                date_str = condition["onsetDateTime"]
                                date_type = "onset"
                            elif "recordedDate" in condition:
                                date_str = condition["recordedDate"]
                                date_type = "recorded"

                            if date_str:
                                # Extract just the date part (YYYY-MM-DD)
                                if "T" in date_str:
                                    date_str = date_str.split("T")[0]

                                temporal_data.append({
                                    "condition": condition_name,
                                    "date": date_str,
                                    "date_type": date_type
                                })

                    if temporal_data:
                        # Convert to DataFrame
                        temporal_df = pd.DataFrame(temporal_data)

                        # Convert dates to datetime
                        temporal_df["date"] = pd.to_datetime(temporal_df["date"], errors="coerce")

                        # Drop rows with invalid dates
                        temporal_df = temporal_df.dropna(subset=["date"])

                        if not temporal_df.empty:
                            # Add year and month columns
                            temporal_df["year"] = temporal_df["date"].dt.year
                            temporal_df["month"] = temporal_df["date"].dt.month
                            temporal_df["year_month"] = temporal_df["date"].dt.strftime("%Y-%m")

                            # Create filters for condition selection
                            # Get top conditions for the filter
                            top_conditions = temporal_df["condition"].value_counts().head(10).index.tolist()

                            selected_condition = st.selectbox(
                                "Select Condition for Temporal Analysis:",
                                ["All Conditions"] + top_conditions,
                                key="temporal_condition"
                            )

                            # Filter data based on selection
                            if selected_condition != "All Conditions":
                                filtered_df = temporal_df[temporal_df["condition"] == selected_condition]
                                title_prefix = selected_condition
                            else:
                                filtered_df = temporal_df
                                title_prefix = "All Conditions"

                            # Create time series by month
                            st.markdown(f"#### {title_prefix} Over Time")

                            # Group by year-month
                            monthly_counts = filtered_df.groupby("year_month").size().reset_index(name="count")
                            monthly_counts["date"] = pd.to_datetime(monthly_counts["year_month"])
                            monthly_counts = monthly_counts.sort_values("date")

                            # Create line chart
                            fig = px.line(
                                monthly_counts,
                                x="date",
                                y="count",
                                title=f"{title_prefix} Occurrences Over Time",
                                labels={"date": "Date", "count": "Number of Cases"},
                                markers=True
                            )

                            st.plotly_chart(fig, use_container_width=True)

                            # Create seasonal analysis
                            st.markdown("#### Seasonal Patterns")

                            # Group by month
                            seasonal_counts = filtered_df.groupby("month").size().reset_index(name="count")

                            # Map month numbers to names
                            month_names = {
                                1: "Jan", 2: "Feb", 3: "Mar", 4: "Apr", 5: "May", 6: "Jun",
                                7: "Jul", 8: "Aug", 9: "Sep", 10: "Oct", 11: "Nov", 12: "Dec"
                            }
                            seasonal_counts["month_name"] = seasonal_counts["month"].map(month_names)

                            # Sort by month
                            seasonal_counts = seasonal_counts.sort_values("month")

                            # Create bar chart
                            fig = px.bar(
                                seasonal_counts,
                                x="month_name",
                                y="count",
                                title=f"{title_prefix} Seasonal Pattern",
                                labels={"month_name": "Month", "count": "Number of Cases"},
                                color="count",
                                color_continuous_scale="Viridis"
                            )

                            st.plotly_chart(fig, use_container_width=True)

                            # Create year-over-year comparison - improved version
                            if filtered_df["year"].nunique() > 1:
                                st.markdown("#### Year-over-Year Comparison")

                                # Get the years with the most data (top 3)
                                top_years = filtered_df["year"].value_counts().head(3).index.tolist()

                                # Create a multi-select for years with the top years pre-selected
                                all_years = sorted(filtered_df["year"].unique())
                                selected_years = st.multiselect(
                                    "Select years to compare:",
                                    options=all_years,
                                    default=top_years,
                                    key="year_comparison_select"
                                )

                                if selected_years:
                                    # Filter to selected years
                                    year_filtered_df = filtered_df[filtered_df["year"].isin(selected_years)]

                                    # Group by year and month
                                    yearly_counts = year_filtered_df.groupby(["year", "month"]).size().reset_index(name="count")
                                    yearly_counts["month_name"] = yearly_counts["month"].map(month_names)

                                    # Create two visualization options
                                    viz_type = st.radio(
                                        "Select visualization type:",
                                        ["Line Chart", "Heatmap"],
                                        horizontal=True,
                                        key="year_comparison_viz_type"
                                    )

                                    if viz_type == "Line Chart":
                                        # Create line chart with selected years
                                        fig = px.line(
                                            yearly_counts,
                                            x="month",
                                            y="count",
                                            color="year",
                                            title=f"{title_prefix} Monthly Comparison by Year",
                                            labels={"month": "Month", "count": "Number of Cases", "year": "Year"},
                                            markers=True,
                                            line_shape="linear"  # Use linear lines for clearer trends
                                        )

                                        # Update x-axis to show month names
                                        fig.update_layout(
                                            xaxis=dict(
                                                tickmode="array",
                                                tickvals=list(range(1, 13)),
                                                ticktext=list(month_names.values())
                                            ),
                                            legend=dict(
                                                orientation="h",
                                                yanchor="bottom",
                                                y=1.02,
                                                xanchor="right",
                                                x=1
                                            ),
                                            height=400
                                        )
                                    else:  # Heatmap
                                        # Create pivot table for heatmap
                                        heatmap_data = yearly_counts.pivot_table(
                                            values="count",
                                            index="year",
                                            columns="month_name",
                                            fill_value=0
                                        )

                                        # Reorder columns to match month order
                                        month_order = [month_names[i] for i in range(1, 13)]
                                        heatmap_data = heatmap_data.reindex(columns=[m for m in month_order if m in heatmap_data.columns])

                                        # Create heatmap
                                        fig = px.imshow(
                                            heatmap_data,
                                            labels=dict(x="Month", y="Year", color="Count"),
                                            title=f"{title_prefix} Monthly Patterns by Year",
                                            color_continuous_scale="YlOrRd",
                                            text_auto=True,  # Show values in cells
                                            aspect="auto"
                                        )

                                        fig.update_layout(
                                            height=350,
                                            margin=dict(l=10, r=10, t=40, b=10)
                                        )

                                    st.plotly_chart(fig, use_container_width=True)

                                    # Add explanatory text
                                    st.markdown("""
                                    <div style="background-color: rgba(0,0,0,0.05); padding: 10px; border-radius: 5px; font-size: 0.9em;">
                                    <b>How to interpret:</b> This visualization shows the monthly distribution of cases across different years.
                                    It helps identify seasonal patterns, yearly trends, and anomalies in condition occurrence.
                                    </div>
                                    """, unsafe_allow_html=True)
                                else:
                                    st.info("Please select at least one year to display the comparison.")
                        else:
                            st.info("No valid date data available for temporal analysis")
                    else:
                        st.info("No temporal data available")
                else:
                    st.info("No condition data available")
            except Exception as e:
                st.error(f"Error loading temporal pattern data: {str(e)}")
                logger.error(f"Error in epidemiological analysis - temporal: {str(e)}")

    # Tab 3: Comorbidity Analysis
    with epi_tabs[2]:
        st.markdown("### Comorbidity Analysis")

        with st.spinner("Loading comorbidity data..."):
            try:
                # Get conditions with patient references
                success, conditions = execute_fhir_query(
                    st.session_state.server_url,
                    "Condition?_elements=code,subject&_count=500"
                )

                if success and "entry" in conditions and conditions["entry"]:
                    # Extract condition and patient data
                    comorbidity_data = []

                    for entry in conditions["entry"]:
                        if "resource" in entry:
                            condition = entry["resource"]

                            # Get condition name
                            condition_name = "Unknown"
                            if "code" in condition and "coding" in condition["code"] and condition["code"]["coding"]:
                                coding = condition["code"]["coding"][0]
                                condition_name = coding.get("display", "Unknown")
                            elif "code" in condition and "text" in condition["code"]:
                                condition_name = condition["code"]["text"]

                            # Get patient reference
                            patient_id = None
                            if "subject" in condition and "reference" in condition["subject"]:
                                patient_ref = condition["subject"]["reference"]
                                if patient_ref.startswith("Patient/"):
                                    patient_id = patient_ref.replace("Patient/", "")

                            if patient_id:
                                comorbidity_data.append({
                                    "condition": condition_name,
                                    "patient_id": patient_id
                                })

                    if comorbidity_data:
                        # Convert to DataFrame
                        comorbidity_df = pd.DataFrame(comorbidity_data)

                        # Get top conditions for analysis
                        condition_counts = comorbidity_df["condition"].value_counts()
                        top_conditions = condition_counts[condition_counts >= 5].index.tolist()

                        if len(top_conditions) >= 2:  # Need at least 2 conditions for comorbidity
                            # Filter to top conditions
                            filtered_df = comorbidity_df[comorbidity_df["condition"].isin(top_conditions)]

                            # Create comorbidity matrix
                            # For each patient, create a set of their conditions
                            patient_conditions = filtered_df.groupby("patient_id")["condition"].apply(set)

                            # Initialize comorbidity matrix
                            comorbidity_matrix = pd.DataFrame(
                                0,
                                index=top_conditions,
                                columns=top_conditions
                            )

                            # Fill the matrix
                            for conditions in patient_conditions:
                                for cond1 in conditions:
                                    for cond2 in conditions:
                                        if cond1 in top_conditions and cond2 in top_conditions:
                                            comorbidity_matrix.loc[cond1, cond2] += 1

                            # Normalize the matrix for better visualization
                            # Diagonal values (self-connections) are often much higher
                            # Create a copy to avoid modifying the original used for network graph
                            display_matrix = comorbidity_matrix.copy()

                            # Set diagonal to NaN to exclude from color scaling
                            for i in range(len(display_matrix)):
                                display_matrix.iloc[i, i] = np.nan

                            # Shorten condition names if they're too long
                            max_length = 30
                            shortened_conditions = {}
                            for cond in display_matrix.index:
                                if len(cond) > max_length:
                                    shortened = cond[:max_length-3] + "..."
                                    shortened_conditions[cond] = shortened

                            if shortened_conditions:
                                display_matrix = display_matrix.rename(index=shortened_conditions, columns=shortened_conditions)

                            # Colores adaptados para tema oscuro
                            bg_color = "rgba(0,0,0,0)"  # Transparente para adaptarse al tema
                            text_color = "#ffffff"      # Blanco para texto en tema oscuro

                            # Create improved heatmap con colores adaptados al tema oscuro
                            fig = px.imshow(
                                display_matrix,
                                labels=dict(x="Condition", y="Condition", color="Co-occurrence"),
                                title="Condition Comorbidity Matrix",
                                color_continuous_scale="Oranges",  # Escala de color más visible en tema oscuro
                                text_auto=True,  # Show values in cells
                                aspect="auto"    # Adjust aspect ratio automatically
                            )

                            # Improve layout
                            fig.update_layout(
                                height=min(600, 100 + len(top_conditions) * 30),  # Dynamic height based on number of conditions
                                margin=dict(l=10, r=10, t=50, b=50),
                                xaxis=dict(tickangle=15),  # Angle x-axis labels for better readability
                                plot_bgcolor=bg_color,
                                paper_bgcolor=bg_color,
                                font={'color': text_color}
                            )

                            # Mejorar la visibilidad del texto en las celdas
                            fig.update_traces(
                                textfont=dict(color='rgba(0,0,0,0.8)'),  # Texto oscuro para celdas claras
                                texttemplate='%{z}'
                            )

                            # Add hover template to show full condition names if shortened
                            if shortened_conditions:
                                hover_template = "<b>%{y}</b> and <b>%{x}</b><br>Co-occurrence: %{z}<extra></extra>"
                                fig.update_traces(hovertemplate=hover_template)

                            st.plotly_chart(fig, use_container_width=True)

                            # Add explanation
                            st.markdown("""
                            <div style="background-color: rgba(0,0,0,0.05); padding: 10px; border-radius: 5px; font-size: 0.9em; margin-bottom: 20px;">
                            <b>How to interpret:</b> This matrix shows how often conditions occur together in the same patients.
                            Higher numbers indicate conditions that frequently co-occur. The diagonal (self-connections) is excluded from color scaling.
                            </div>
                            """, unsafe_allow_html=True)

                            # Create network graph
                            st.markdown("#### Comorbidity Network")

                            # Prepare data for network graph
                            network_data = []

                            for i, cond1 in enumerate(top_conditions):
                                for j, cond2 in enumerate(top_conditions):
                                    if i < j:  # Avoid duplicates and self-connections
                                        weight = comorbidity_matrix.loc[cond1, cond2]
                                        if weight > 0:
                                            network_data.append({
                                                "source": cond1,
                                                "target": cond2,
                                                "weight": weight
                                            })

                            if network_data:
                                # Create network DataFrame
                                network_df = pd.DataFrame(network_data)

                                # Filter edges to show only stronger connections
                                # Allow user to control the threshold
                                min_weight = 1
                                max_weight = network_df["weight"].max()

                                if max_weight > 1:
                                    weight_threshold = st.slider(
                                        "Minimum co-occurrence threshold:",
                                        min_value=min_weight,
                                        max_value=max_weight,
                                        value=max(1, int(max_weight * 0.2)),  # Default to 20% of max
                                        step=1,
                                        key="comorbidity_threshold"
                                    )

                                    # Filter based on threshold
                                    filtered_network_df = network_df[network_df["weight"] >= weight_threshold]
                                else:
                                    filtered_network_df = network_df

                                # Create network graph using plotly
                                import networkx as nx

                                # Create graph
                                G = nx.Graph()

                                # Add nodes (only those that have connections after filtering)
                                all_nodes = set(filtered_network_df["source"].unique()) | set(filtered_network_df["target"].unique())
                                for condition in all_nodes:
                                    G.add_node(condition)

                                # Add edges with weights
                                for _, row in filtered_network_df.iterrows():
                                    G.add_edge(row["source"], row["target"], weight=row["weight"])

                                # Skip if no edges after filtering
                                if len(G.edges()) == 0:
                                    st.info("No connections meet the threshold criteria. Try lowering the threshold.")
                                else:
                                    # Get positions - use a more spaced layout
                                    pos = nx.spring_layout(G, k=0.5, iterations=50, seed=42)

                                    # Calculate edge widths based on weights
                                    max_width = 10
                                    min_width = 1

                                    if filtered_network_df["weight"].max() > filtered_network_df["weight"].min():
                                        edge_width_scale = (max_width - min_width) / (filtered_network_df["weight"].max() - filtered_network_df["weight"].min())
                                    else:
                                        edge_width_scale = 1

                                    # Create edge traces with different widths based on weight
                                    edge_traces = []

                                    for edge in G.edges(data=True):
                                        x0, y0 = pos[edge[0]]
                                        x1, y1 = pos[edge[1]]
                                        weight = edge[2]["weight"]

                                        # Calculate width based on weight
                                        width = min_width + (weight - filtered_network_df["weight"].min()) * edge_width_scale

                                        # Create a separate trace for each edge for better hover info
                                        edge_trace = go.Scatter(
                                            x=[x0, x1, None],
                                            y=[y0, y1, None],
                                            line=dict(width=width, color="#6175c1"),
                                            hoverinfo="text",
                                            text=f"{edge[0]} ↔ {edge[1]}<br>Co-occurrence: {weight}",
                                            mode="lines"
                                        )
                                        edge_traces.append(edge_trace)

                                    # Create node trace
                                    node_x = []
                                    node_y = []
                                    node_text = []
                                    node_sizes = []
                                    node_hover_texts = []

                                    # Calculate node metrics
                                    degree_dict = dict(G.degree())

                                    # Normalize node sizes
                                    max_degree = max(degree_dict.values()) if degree_dict else 1
                                    min_node_size = 15
                                    max_node_size = 50

                                    for node in G.nodes():
                                        x, y = pos[node]
                                        node_x.append(x)
                                        node_y.append(y)

                                        # Shorten node text if too long
                                        if len(node) > 20:
                                            display_text = node[:17] + "..."
                                        else:
                                            display_text = node

                                        node_text.append(display_text)

                                        # Node size based on degree (normalized)
                                        degree = degree_dict[node]
                                        size = min_node_size + (degree / max_degree) * (max_node_size - min_node_size)
                                        node_sizes.append(size)

                                        # Create detailed hover text
                                        connections = [n for n in G.neighbors(node)]
                                        connections_text = "<br>".join([f"• {c}" for c in connections[:5]])
                                        if len(connections) > 5:
                                            connections_text += f"<br>• ...and {len(connections) - 5} more"

                                        hover_text = f"<b>{node}</b><br>Connections: {degree}<br><br>Connected to:<br>{connections_text}"
                                        node_hover_texts.append(hover_text)

                                    node_trace = go.Scatter(
                                        x=node_x, y=node_y,
                                        mode="markers+text",
                                        text=node_text,
                                        textposition="top center",
                                        hoverinfo="text",
                                        hovertext=node_hover_texts,
                                        marker=dict(
                                            color="#f28e2b",
                                            size=node_sizes,
                                            line=dict(width=2, color="#ffffff")
                                        )
                                    )

                                    # Detectar si estamos en modo oscuro o claro
                                    # En Streamlit, podemos adaptar nuestros gráficos al tema
                                    # Usamos colores que funcionan bien en ambos temas

                                    # Colores adaptados para tema oscuro
                                    bg_color = "rgba(0,0,0,0)"  # Transparente para adaptarse al tema
                                    node_color = "#ff9e4a"      # Naranja más visible en tema oscuro
                                    edge_color = "#6e8eff"      # Azul más visible en tema oscuro
                                    text_color = "#ffffff"      # Blanco para texto en tema oscuro

                                    # Actualizar el color de los bordes
                                    for i in range(len(edge_traces)):
                                        edge_traces[i].line.color = edge_color

                                    # Actualizar el color de los nodos
                                    node_trace.marker.color = node_color
                                    node_trace.marker.line = dict(width=2, color="#444444")

                                    # Create figure with all traces
                                    fig = go.Figure(data=edge_traces + [node_trace],
                                                 layout=go.Layout(
                                                     title={
                                                         'text': "Condition Comorbidity Network",
                                                         'y':0.95,
                                                         'x':0.5,
                                                         'xanchor': 'center',
                                                         'yanchor': 'top',
                                                         'font': {'color': text_color}
                                                     },
                                                     showlegend=False,
                                                     hovermode="closest",
                                                     margin=dict(b=20, l=20, r=20, t=40),
                                                     xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                                                     yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                                                     height=600,
                                                     plot_bgcolor=bg_color,
                                                     paper_bgcolor=bg_color,
                                                     font={'color': text_color}
                                                 ))

                                    st.plotly_chart(fig, use_container_width=True)

                                    # Add explanation in a more visually appealing format
                                    st.markdown("""
                                    <div style="background-color: rgba(0,0,0,0.05); padding: 15px; border-radius: 5px; margin-top: 10px;">
                                    <h4 style="margin-top: 0;">How to interpret the comorbidity network:</h4>
                                    <ul>
                                      <li><b>Nodes (circles):</b> Each node represents a condition</li>
                                      <li><b>Node size:</b> Larger nodes have more connections to other conditions</li>
                                      <li><b>Edges (lines):</b> Connections between nodes indicate conditions that occur together</li>
                                      <li><b>Edge thickness:</b> Thicker lines indicate conditions that co-occur more frequently</li>
                                    </ul>
                                    <p><i>Hover over nodes and edges for more detailed information.</i></p>
                                    <p><i>Use the threshold slider to focus on stronger relationships.</i></p>
                                    </div>
                                    """, unsafe_allow_html=True)
                            else:
                                st.info("Insufficient comorbidity data for network visualization")
                        else:
                            st.info("Insufficient condition data for comorbidity analysis")
                    else:
                        st.info("No comorbidity data available")
                else:
                    st.info("No condition data available")
            except Exception as e:
                st.error(f"Error loading comorbidity data: {str(e)}")
                logger.error(f"Error in epidemiological analysis - comorbidity: {str(e)}")

# Helper functions for data processing and visualization

@ttl_cache()
def get_resource_counts():
    """
    Get counts of different resource types in the FHIR server.
    Results are cached to improve performance.

    Returns:
        pd.DataFrame: DataFrame with resource types and their counts
    """
    logger.info("Fetching resource counts (not from cache)")

    resource_types = [
        "Patient", "Observation", "Condition",
        "Encounter", "Procedure", "MedicationRequest",
        "AllergyIntolerance", "DiagnosticReport", "Immunization"
    ]

    counts = []
    for resource_type in resource_types:
        try:
            success, result = execute_fhir_query(
                st.session_state.server_url,
                f"{resource_type}?_summary=count"
            )

            if success and "total" in result:
                counts.append({
                    "Resource Type": resource_type,
                    "Count": result["total"]
                })
            else:
                counts.append({
                    "Resource Type": resource_type,
                    "Count": 0
                })
        except Exception as e:
            logger.error(f"Error getting count for {resource_type}: {str(e)}")
            counts.append({
                "Resource Type": resource_type,
                "Count": 0
            })

    return pd.DataFrame(counts)

@st.cache_data(ttl=CACHE_TTL)
def create_resource_count_chart(df):
    """
    Create visualizations of resource counts with options for log scale and excluding dominant types.
    Results are cached using Streamlit's cache_data to improve performance.

    Args:
        df (pd.DataFrame): DataFrame with resource types and counts

    Returns:
        plotly.graph_objects.Figure: Bar chart of resource counts
    """
    # Filter out zero counts for cleaner visualization
    df_filtered = df[df["Count"] > 0].copy()

    # Sort by count for better visualization
    df_filtered = df_filtered.sort_values("Count", ascending=False)

    # Create tabs for different visualization options
    viz_tab1, viz_tab2, viz_tab3 = st.tabs(["Standard Scale", "Log Scale", "Excluding Dominant Types"])

    with viz_tab1:
        # Standard scale visualization
        fig1 = px.bar(
            df_filtered,
            x="Resource Type",
            y="Count",
            title="Resource Counts by Type",
            color="Resource Type",
            labels={"Count": "Number of Resources", "Resource Type": "FHIR Resource Type"},
            color_discrete_sequence=px.colors.qualitative.Pastel
        )

        fig1.update_layout(
            xaxis_title="Resource Type",
            yaxis_title="Count",
            legend_title="Resource Type",
            height=500
        )

        st.plotly_chart(fig1, use_container_width=True)

    with viz_tab2:
        # Log scale visualization
        fig2 = px.bar(
            df_filtered,
            x="Resource Type",
            y="Count",
            title="Resource Counts by Type (Log Scale)",
            color="Resource Type",
            labels={"Count": "Number of Resources (log scale)", "Resource Type": "FHIR Resource Type"},
            color_discrete_sequence=px.colors.qualitative.Pastel,
            log_y=True  # Use log scale for y-axis
        )

        fig2.update_layout(
            xaxis_title="Resource Type",
            yaxis_title="Count (log scale)",
            legend_title="Resource Type",
            height=500
        )

        st.plotly_chart(fig2, use_container_width=True)

    with viz_tab3:
        # Create a copy of the dataframe
        df_no_dominant = df_filtered.copy()

        # Identify dominant resource type (usually Observation)
        dominant_type = df_filtered.iloc[0]["Resource Type"] if not df_filtered.empty else None

        if dominant_type and len(df_filtered) > 1:
            # Filter out the dominant type
            df_no_dominant = df_filtered[df_filtered["Resource Type"] != dominant_type]

            # Create visualization without dominant type
            fig3 = px.bar(
                df_no_dominant,
                x="Resource Type",
                y="Count",
                title=f"Resource Counts by Type (Excluding {dominant_type})",
                color="Resource Type",
                labels={"Count": "Number of Resources", "Resource Type": "FHIR Resource Type"},
                color_discrete_sequence=px.colors.qualitative.Pastel
            )

            fig3.update_layout(
                xaxis_title="Resource Type",
                yaxis_title="Count",
                legend_title="Resource Type",
                height=500
            )

            st.plotly_chart(fig3, use_container_width=True)

            # Show info about excluded type
            st.info(f"Excluded {dominant_type}: {df_filtered[df_filtered['Resource Type'] == dominant_type].iloc[0]['Count']} resources")
        else:
            st.info("Not enough data to create this visualization")

    # Return the first figure for compatibility with existing code
    return px.bar(
        df_filtered,
        x="Resource Type",
        y="Count",
        title="Resource Counts by Type",
        color="Resource Type",
        labels={"Count": "Number of Resources", "Resource Type": "FHIR Resource Type"},
        color_discrete_sequence=px.colors.qualitative.Pastel
    )
