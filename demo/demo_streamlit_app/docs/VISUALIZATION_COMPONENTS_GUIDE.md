# FHIR Data Visualization Components Guide

This document provides an overview of the visualizations implemented in `visualization.py` for the FHIR-OMOP Streamlit demo application. These visualizations are designed to help clinical data scientists and researchers understand patterns and generate insights from healthcare data stored in FHIR format.

## Table of Contents
- [Visualization Overview](#visualization-overview)
- [Visualization Components](#visualization-components)
  - [Database Overview](#database-overview)
  - [Clinical Insights](#clinical-insights)
  - [Epidemiological Analysis](#epidemiological-analysis)
- [Visualization Reference Table](#visualization-reference-table)
- [Technical Implementation](#technical-implementation)

## Visualization Overview

The visualization module provides comprehensive dashboards for analyzing FHIR data with a focus on clinical and epidemiological insights. The visualizations are organized into three main categories:

1. **Database Overview**: Resource counts, patient demographics, and data timeline
2. **Clinical Insights**: Diagnoses prevalence, lab measurements, and vital signs trends
3. **Epidemiological Analysis**: Condition prevalence by demographics, temporal patterns, and comorbidity analysis

## Visualization Components

### Database Overview

#### Resource Distribution Chart
- **Purpose**: Shows the distribution of different FHIR resource types in the database
- **Interpretation**: Taller bars indicate more resources of that type, providing insight into the composition of the database
- **Technical Details**: Bar chart with resource types on x-axis and counts on y-axis

#### Patient Demographics
- **Purpose**: Displays gender distribution and age distribution of patients
- **Interpretation**: Shows the demographic makeup of the patient population, useful for understanding representation
- **Technical Details**: Pie chart for gender distribution, histogram for age distribution

#### Data Timeline
- **Purpose**: Shows the temporal distribution of observations in the database
- **Interpretation**: Peaks indicate periods with more data, useful for identifying data collection patterns
- **Technical Details**: Line chart with dates on x-axis and observation counts on y-axis

### Clinical Insights

#### Top Diagnoses
- **Purpose**: Shows the most common diagnoses in the database
- **Interpretation**: Longer bars indicate more prevalent conditions
- **Technical Details**: Horizontal bar chart with conditions on y-axis and counts on x-axis

#### Clinical Status Distribution
- **Purpose**: Shows the distribution of clinical statuses for conditions
- **Interpretation**: Segments represent proportion of each status (active, resolved, etc.)
- **Technical Details**: Donut chart with statuses as segments

#### Lab Measurements
- **Purpose**: Displays distribution and trends of selected lab measurements
- **Interpretation**: 
  - Histogram shows value distribution with normal ranges highlighted
  - Line chart shows trends over time
  - Statistical summary provides key metrics
- **Technical Details**: Histogram, line chart, and metric displays

#### Vital Signs
- **Purpose**: Shows trends and outliers in vital sign measurements
- **Interpretation**: 
  - Line chart shows trends over time
  - Outliers are highlighted and listed separately
- **Technical Details**: Line chart with outlier detection using z-scores

### Epidemiological Analysis

#### Condition Prevalence by Demographics
- **Purpose**: Shows how conditions are distributed across different age groups and genders
- **Interpretation**: Darker cells indicate higher prevalence in that demographic group
- **Technical Details**: Heatmap with age groups on x-axis and conditions by gender on y-axis

#### Temporal Patterns
- **Purpose**: Analyzes seasonal and yearly patterns in condition occurrence
- **Interpretation**: 
  - Line chart shows overall trends over time
  - Bar chart shows seasonal patterns
  - Year comparison shows patterns across different years
- **Technical Details**: Line charts, bar charts, and heatmaps for temporal analysis

#### Comorbidity Analysis

##### Comorbidity Matrix
- **Purpose**: Shows how often different conditions co-occur in the same patients
- **Interpretation**: 
  - Each cell shows the number of patients who have both conditions
  - Higher numbers (darker colors) indicate conditions that frequently co-occur
  - The diagonal (self-connections) is excluded from color scaling
- **Technical Details**: Heatmap with conditions on both axes, values representing co-occurrence counts

##### Comorbidity Network
- **Purpose**: Visualizes relationships between conditions as a network graph
- **Interpretation**: 
  - Nodes (circles) represent conditions
  - Node size indicates how many connections a condition has to other conditions
  - Edges (lines) connect conditions that co-occur in patients
  - Edge thickness indicates how frequently the conditions co-occur
  - The threshold slider allows focusing on stronger relationships
- **Technical Details**: Interactive network graph using Plotly and NetworkX

## Visualization Reference Table

| Visualization | Category | Purpose | Interpretation | Key Features |
|---------------|----------|---------|----------------|--------------|
| Resource Distribution | Database Overview | Show resource type distribution | Taller bars = more resources | Color-coded by resource type |
| Patient Demographics | Database Overview | Show patient population makeup | Segments/bins show distribution | Gender pie chart, age histogram |
| Data Timeline | Database Overview | Show temporal data distribution | Peaks = periods with more data | Interactive time series |
| Top Diagnoses | Clinical Insights | Show most common conditions | Longer bars = more prevalent | Sorted by frequency |
| Clinical Status | Clinical Insights | Show condition status distribution | Segments = proportion of statuses | Donut chart visualization |
| Lab Measurements | Clinical Insights | Analyze lab test results | Distribution and trends over time | Normal ranges highlighted |
| Vital Signs | Clinical Insights | Track vital sign measurements | Trends and outliers | Outlier detection with z-scores |
| Demographic Prevalence | Epidemiological | Show condition distribution by demographics | Darker cells = higher prevalence | Heatmap by age and gender |
| Temporal Patterns | Epidemiological | Analyze seasonal and yearly trends | Peaks = high occurrence periods | Multiple temporal views |
| Comorbidity Matrix | Epidemiological | Show condition co-occurrence | Darker cells = frequent co-occurrence | Color-coded heatmap |
| Comorbidity Network | Epidemiological | Visualize condition relationships | Node size and edge thickness show relationship strength | Interactive network with threshold control |

## Technical Implementation

The visualizations are implemented using:

- **Plotly**: For interactive charts and graphs
- **NetworkX**: For network graph data structures
- **Pandas**: For data manipulation and analysis
- **Streamlit**: For the web interface and interactive controls

The visualization module is designed to be:

1. **Responsive**: Adapts to different screen sizes and data volumes
2. **Interactive**: Allows users to filter, zoom, and explore data
3. **Informative**: Provides clear interpretations and context
4. **Efficient**: Uses caching to improve performance with large datasets
5. **Theme-compatible**: Adapts to Streamlit's light and dark themes

For implementation details, refer to the code in `demo/demo_streamlit_app/ui_components/visualization.py`.
