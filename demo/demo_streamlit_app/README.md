# FHIR Server Demo Streamlit Application

This application provides a visual interface to interact with a FHIR server, allowing you to load data, explore resources, and visualize performance statistics. It serves as a demonstration tool for the FHIR server capabilities, especially useful for presentations to clients or stakeholders.

## Project Overview

The demo application is a visual interface developed with Streamlit that allows:

1. Connecting to local or cloud FHIR servers
2. Managing the local FHIR server (start, stop, restart)
3. Loading FHIR data using different methods
4. Visualizing detailed statistics of the loading process
5. Exploring loaded resources through FHIR queries
6. Displaying performance reports with interactive visualizations

## Features

- **FHIR Server Connection**: Connect to local or cloud FHIR servers
- **Local Server Management**: Start, stop, and restart the local FHIR server
- **Data Loading**: Load FHIR data using different methods
  - Batch transaction method (fast)
  - Selective loading method (safe)
- **Data Exploration**: Execute FHIR queries and visualize the results
- **Visualization**: Display statistics and charts of loaded resources
- **Detailed Reports**: Visualize comprehensive reports of the loading process

## Project Structure

```
demo_streamlit_app/
├── .streamlit/
│   └── config.toml         # Streamlit configuration
├── .vscode/
│   └── launch.json         # VS Code debugging configuration
├── performance_reports/
│   └── performance_data_*.json  # Sample performance data
├── app.py                  # Main Streamlit application
├── environment.yml         # Conda environment dependencies
├── README.md               # General application documentation
├── setup_and_run.sh        # Script to set up and run the application
└── utils.py                # Utility functions for the application
```

## Technologies Used

- **Python 3.9+**: Main programming language
- **Streamlit**: User interface framework
- **Pandas**: Data manipulation and analysis
- **Plotly**: Interactive visualizations
- **Requests**: HTTP communication with the FHIR server
- **Conda**: Environment and dependency management

## Development Environment Setup

### Prerequisites

- Python 3.9 or higher
- Miniconda or Anaconda
- Git

### Steps to Set Up the Environment

1. Clone the repository if you haven't already:
   ```bash
   git clone <repository-url>
   cd fhir-omop
   ```

2. Set up the conda environment using the provided script:
   ```bash
   cd demo/demo_streamlit_app
   chmod +x setup_and_run.sh
   ./setup_and_run.sh
   ```

   Alternatively, you can create the environment manually:
   ```bash
   conda env create -f environment.yml
   conda activate fhir-demo
   ```

3. Run the application:
   ```bash
   streamlit run app.py
   ```

### VS Code Debugging Configuration

A `.vscode/launch.json` file is already configured to debug the Streamlit application. To use it:

1. Open the project in VS Code
2. Open the `app.py` file
3. Set breakpoints where needed
4. Press F5 or select "Run and Debug" in the side panel

Alternatively, you can use Python's built-in debugger (pdb) by uncommenting the `pdb.set_trace()` lines in the code.

## Application Architecture

### Main Components

1. **app.py**: Main entry point that defines the Streamlit user interface and application flow.

2. **utils.py**: Contains utility functions that interact with FHIR server scripts, process data, and handle errors.

3. **Configuration (.streamlit/config.toml)**: Defines Streamlit-specific configurations such as log level and server options.

### Data Flow

1. The user selects a FHIR server (local or cloud) in the interface.
2. The application verifies connectivity with the server.
3. The user selects a dataset and a loading method.
4. The application invokes the corresponding scripts to load the data.
5. Results and statistics are extracted from the scripts' output.
6. The application processes and visualizes the results in the interface.

### Integration with Existing Scripts

The application uses existing scripts in `servers/fhir-server/scripts/` to perform operations such as:

- Server connectivity testing (`test_fhir_server.py`)
- NDJSON to bundles conversion (`ndjson_to_bundle.py`)
- Sending bundles to the server (`send_bundle.py`)
- Selective data loading (`selective_loader.py`)
- Loading all bundles with metrics (`load_all_bundles.py`)

## Application Tabs

The application is organized into tabs:

1. **Home**: General information and quick connection test
2. **Data Loading**: Loading FHIR data with different methods
3. **Data Exploration**: Executing FHIR queries and visualizing results
4. **Visualization**: Visualizing resource statistics

## Data Loading and Report Visualization

The application allows loading FHIR data using two main methods:

### Batch Transaction Method (Fast Loading)

This method converts NDJSON files into transaction bundles and sends them directly to the server. It's faster but requires the server to be in permissive mode.

1. Select a dataset
2. Choose "Fast Loading (Transaction Bundles)"
3. Configure the batch size
4. Click on "Load Data"

### Selective Loading Method (Safe Loading)

This method analyzes references between resources and loads only those with satisfied dependencies. It's safer but may load fewer resources.

1. Select a dataset
2. Choose "Safe Loading (Selective Loader)"
3. Configure the batch size
4. Click on "Load Data"

### Detailed Report Visualization

After loading the data, the application displays a detailed report with the following sections:

#### 1. Resource Breakdown by Type

Displays a table and bar chart with:
- Loaded resource types
- Number of bundles per type
- Number of resources per type
- Loading time per type
- Success rate per type

#### 2. General Statistics

Shows general statistics of the loading process:
- Total processing time
- Processed bundles (total and percentage)
- Processed resources (total and percentage)

#### 3. Performance Metrics

Shows detailed performance metrics:
- Resources per second
- Bundles per second
- Average resource size

#### 4. System Resource Usage

Shows information about system resource usage:
- CPU usage (peak and average)
- Memory usage (peak and average)
- Information about cores and total memory

#### 5. Top Resource Types by Volume

Shows a ranking of the most common resource types:
- Pie chart of resource distribution
- Table with percentages by type

#### 6. Failed Bundles

Shows information about bundles that failed during loading:
- Resource type
- File name
- Number of resources
- Error message

## Error Handling

The application is designed to handle errors robustly:

- **Connection Errors**: Displays clear messages when it cannot connect to the server
- **Partial Loading Errors**: Shows the detailed report even when some bundles fail
- **Validation Errors**: Provides suggestions to resolve validation issues

## Data Exploration

The "Data Exploration" tab allows executing FHIR queries:

- Predefined queries for common resource types
- Custom queries with FHIR syntax
- Tabular visualization of results
- Export results in CSV or JSON format

## Data Visualization

The "Visualization" tab provides visualizations of the loaded data:

- Resource distribution with multiple view options:
  - Standard scale view
  - Logarithmic scale view (for comparing disproportionate data)
  - View excluding dominant resource types
- Patient demographics (gender and age distribution)
- Clinical insights with condition prevalence
- General server statistics

## Server Configuration

The application allows configuring and managing the FHIR server:

- Switch between local and cloud server
- Start, stop, and restart the local server
- Check server status
- Test server connection

## Areas for Improvement

### UI/UX Improvements

1. **Responsive Design**: Optimize the interface for different screen sizes.
2. **Custom Themes**: Implement a visual theme consistent with the project's identity.
3. **Tooltips and Contextual Help**: Add help information to guide users.
4. **Minimalist Interface**: Continue refining the UI to be more minimalist while maintaining clarity and functionality.

### Functional Improvements

1. **Authentication**: Add support for different authentication methods to the FHIR server.
2. **File Upload**: Improve the functionality for uploading custom files.
3. **Data Export**: Allow exporting query results and reports in different formats.
4. **Data Validation**: Implement more robust validation of data before loading.

### Technical Improvements

1. **Unit Tests**: Add unit tests for the functions in `utils.py`.
2. **Error Handling**: Improve error handling and visualization.
3. **Logging**: Implement a more detailed logging system.
4. **Performance Optimization**: Improve application performance for large datasets.
5. **Visualization Enhancements**: Continue optimizing visualizations for disproportionate data, particularly for resource distribution charts.

### OMOP Integration

1. **FHIR-OMOP Mapping Visualization**: Add visualizations showing how FHIR data maps to OMOP.
2. **OMOP Queries**: Implement functionality to execute SQL queries on the OMOP database.
3. **Transformation Metrics**: Add metrics about the FHIR to OMOP transformation process.

## Code Conventions

- Follow PEP 8 for Python code style.
- Use NumPy format docstrings to document functions and classes.
- Keep functions small and with a single responsibility.

## Contribution

To contribute to this project:

1. Fork the repository
2. Create a branch for your feature (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

## Proprietary Notice

This project is proprietary to AIO (Artificial Intelligence Orchestrator). All rights reserved. Unauthorized copying, distribution, or use is strictly prohibited.
