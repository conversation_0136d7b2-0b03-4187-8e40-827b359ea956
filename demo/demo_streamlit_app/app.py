"""
FHIR Server Demo Streamlit Application

This application provides a visual interface to demonstrate the capabilities
of the FHIR server, including connectivity, data loading, and data exploration.
"""

import streamlit as st
import logging
import sys
import os
import pdb  # Import Python debugger

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Print a test message to verify logging is working
print("LOGGING TEST: Streamlit app starting")

# Create a logger for this module
logger = logging.getLogger(__name__)
from utils import (
    test_fhir_connection,
    get_server_status,
    start_local_server,
    stop_local_server,
    restart_local_server
)

# Import UI components
from ui_components import render_data_loading_tab, render_data_exploration_tab, render_visualization_tab

# Configure the Streamlit page
st.set_page_config(
    page_title="AIO FHIR Intelligence",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Main title with elegant styling
st.markdown("""
<div style="display: flex; flex-direction: column; align-items: center; margin-bottom: 1rem;">
    <h1 style="margin-bottom: 0.2rem;">AIO FHIR Intelligence</h1>
    <p style="color: #888; font-size: 1.1rem; margin-top: 0;">Interactive Healthcare Data Platform</p>
</div>
""", unsafe_allow_html=True)

# Initialize session state for server configuration
if "server_url" not in st.session_state:
    st.session_state.server_url = "http://localhost:8080/fhir"

# Sidebar for server configuration
with st.sidebar:
    # Add company logo with proper centering using HTML/CSS
    # Use a path that works whether the app is run from the root or from the demo folder
    logo_path = "static/AIO-logo-nuevo-claro.png"

    # Find the correct path for the logo
    actual_logo_path = None
    if os.path.exists(logo_path):
        actual_logo_path = logo_path
    elif os.path.exists("demo/demo_streamlit_app/" + logo_path):
        actual_logo_path = "demo/demo_streamlit_app/" + logo_path
    elif os.path.exists("demo_streamlit_app/" + logo_path):
        actual_logo_path = "demo_streamlit_app/" + logo_path

    # Center the logo using columns
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        if actual_logo_path:
            st.image(actual_logo_path, width=180, use_container_width=True)
        else:
            st.markdown("<h3 style='text-align: center;'>AIO Technology</h3>", unsafe_allow_html=True)

    # Add link to company website (centered)
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(
            """
            <div style="text-align: center; margin-bottom: 1rem;">
                <a href="https://aiorchestrator.ai/" target="_blank" style="color: #4d9bf5; text-decoration: none; font-size: 0.8rem;">
                    aiorchestrator.ai
                </a>
            </div>
            """,
            unsafe_allow_html=True
        )
    st.markdown("---")

    st.header("Server Configuration")

    # Server environment selection
    server_env = st.radio(
        "Select environment:",
        ["Local Server", "Cloud Server"]
    )

    # Configure URL based on selection
    if server_env == "Local Server":
        server_url = st.text_input("FHIR server URL:", "http://localhost:8080/fhir")

        # Add controls for managing the local server
        st.subheader("Local Server Management")

        # Check current status
        is_running, status_msg = get_server_status("local")

        if is_running:
            st.success(f"✅ Local server: {status_msg}")
        else:
            st.warning(f"⚠️ Local server: {status_msg}")

        # Control buttons
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("Start"):
                with st.spinner("Starting local server..."):
                    success, result = start_local_server()

                if success:
                    st.success("✅ Server started")
                    st.experimental_rerun()  # Reload to update status
                else:
                    st.error(f"❌ Error: {result}")

        with col2:
            if st.button("Stop"):
                with st.spinner("Stopping local server..."):
                    success, result = stop_local_server()

                if success:
                    st.success("✅ Server stopped")
                    st.experimental_rerun()  # Reload to update status
                else:
                    st.error(f"❌ Error: {result}")

        with col3:
            if st.button("Restart"):
                with st.spinner("Restarting local server..."):
                    success, result = restart_local_server()

                if success:
                    st.success("✅ Server restarted")
                    st.experimental_rerun()  # Reload to update status
                else:
                    st.error(f"❌ Error: {result}")
    else:
        server_url = st.text_input("FHIR server URL:", "https://fhir.aiotek.ai/fhir")

        # Check cloud server status
        is_running, status_msg = get_server_status("cloud")

        if is_running:
            st.success(f"✅ Cloud server: {status_msg}")
        else:
            st.warning(f"⚠️ Cloud server: {status_msg}")

    # Save URL in session state
    st.session_state.server_url = server_url

    # Connection test button
    if st.button("Test Connection"):
        logger.info(f"Testing connection to server: {server_url}")
        # Uncomment the next line to enable debugging at this point
        # pdb.set_trace()  # DEBUG: Connection test
        with st.spinner("Connecting to FHIR server..."):
            success, response = test_fhir_connection(server_url)

        if success:
            logger.info(f"Connection successful to {server_url}")
            st.success("✅ Connection successful")
            # Extract basic server information
            if "software" in response:
                server_info = f"Server: {response['software'].get('name', 'Unknown')} {response['software'].get('version', '')}"
                logger.info(server_info)
                st.info(server_info)
            fhir_version = f"FHIR Version: {response.get('fhirVersion', 'Unknown')}"
            logger.info(fhir_version)
            st.info(fhir_version)
        else:
            logger.error(f"Connection error to {server_url}: {response}")
            st.error(f"❌ Connection error: {response}")

# Create tabs for different functionalities with elegant emojis
tab1, tab2, tab3, tab4 = st.tabs([
    "🏠 Home",
    "📥 Data Loading",
    "🔍 Data Exploration",
    "📊 Dashboard"
])

# Tab 1: Home
with tab1:
    st.header("Welcome to AIO FHIR Intelligence")

    # Create two columns for better layout
    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("Interactive FHIR Server Interface")

        # Use a simpler approach with standard Streamlit components
        col_a, col_b = st.columns([1, 20])
        with col_a:
            st.markdown("📥")
        with col_b:
            st.markdown("**Data Loading**")
            st.caption("Transform and load clinical data into FHIR format with transaction bundles")

        col_a, col_b = st.columns([1, 20])
        with col_a:
            st.markdown("🔍")
        with col_b:
            st.markdown("**Data Exploration**")
            st.caption("Browse patient records, clinical observations, and resource relationships")

        col_a, col_b = st.columns([1, 20])
        with col_a:
            st.markdown("📊")
        with col_b:
            st.markdown("**Analytics & Visualization**")
            st.caption("Analyze healthcare data patterns and visualize clinical insights with interactive charts")

        st.caption("Navigate through the tabs above to access different functionalities.")

    with col2:
        # Display server information in a clean card
        st.subheader("Server Status")

        # Use standard Streamlit components for server status
        st.markdown("🔌 **Active Server**")
        st.code(st.session_state.server_url, language=None)

        # Quick connection test
        if st.button("Test Connection", key="home_test_connection"):
            with st.spinner("Testing connection..."):
                success, response = test_fhir_connection(st.session_state.server_url)

            if success:
                st.success("✅ Server is online and responding")
            else:
                st.error(f"❌ Server is not responding: {response}")

    # Add a getting started section
    st.markdown("---")
    st.subheader("Getting Started")

    # Use a grid layout with standard Streamlit components
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**1. Connect**")
        st.caption("Ensure server connection is active using the test button")

        st.markdown("**2. Load Data**")
        st.caption("Navigate to 📥 Data Loading to import clinical data")

    with col2:
        st.markdown("**3. Explore**")
        st.caption("Use 🔍 Data Exploration to query patient records")

        st.markdown("**4. Analyze**")
        st.caption("Visit 📊 Dashboard to visualize clinical patterns")

# Tab 2: Data Loading
with tab2:
    # Use the modular component for data loading
    render_data_loading_tab()

# Tab 3: Data Exploration
with tab3:
    # Use the modular component for data exploration
    render_data_exploration_tab()

# Tab 4: Visualization
with tab4:
    # Use the modular component for visualization
    render_visualization_tab()

# Footer with elegant styling
st.markdown("---")
st.markdown("""
<div style="display: flex; justify-content: space-between; align-items: center; padding: 10px 0;">
    <span style="color: #888; font-size: 0.8em;">AIO FHIR Intelligence • Developed with Streamlit</span>
    <span style="color: #888; font-size: 0.8em;">
        © 2024 <a href="https://aiorchestrator.ai/" target="_blank" style="color: #4d9bf5; text-decoration: none;">AIO Technology</a>
    </span>
</div>
""", unsafe_allow_html=True)
