# LOINC to OMOP Standard Concepts Mapping

## Overview

LOINC (Logical Observation Identifiers Names and Codes) is a universal standard for identifying medical laboratory observations and clinical measurements. When transforming FHIR data containing LOINC codes to OMOP CDM, a systematic mapping process is required to ensure that the LOINC codes are properly translated to OMOP standard concepts.

## LOINC in OMOP CDM

In the OMOP Common Data Model, LOINC codes are primarily used in the MEASUREMENT domain, though they can occasionally appear in the OBSERVATION domain as well. LOINC is one of the standard vocabularies recognized by OMOP, and LOINC codes are included in the CONCEPT table with vocabulary_id = 'LOINC'.

## Mapping Process

The process of mapping LOINC codes from FHIR resources to OMOP standard concepts involves several steps:

1. **Extract LOINC Code from FHIR Resource**: 
   - In FHIR, LOINC codes are typically found in Observation resources in the `code.coding` element where `system = "http://loinc.org"`.

2. **Look Up LOINC Code in OMOP Vocabulary**:
   - Use the CONCEPT table to find the corresponding concept_id for the LOINC code.
   - Query the CONCEPT table where concept_code = [LOINC code] and vocabulary_id = 'LOINC'.

3. **Determine Standard Concept**:
   - Check if the found concept is a standard concept (standard_concept = 'S').
   - If it is not a standard concept, use the CONCEPT_RELATIONSHIP table to find the standard concept it maps to.

4. **Assign to Appropriate Domain**:
   - Based on the domain_id of the standard concept, determine which OMOP table the data should be stored in (typically MEASUREMENT).

5. **Populate OMOP Fields**:
   - Set measurement_concept_id to the standard concept_id.
   - Set measurement_source_value to the original LOINC code.
   - Set measurement_source_concept_id to the source concept_id (if different from the standard concept_id).

## SQL Code Examples

### Example 1: Finding OMOP Concept ID for a LOINC Code

```sql
-- Find the OMOP concept_id for LOINC code '34714-6' (Blood pressure systolic & diastolic)
SELECT 
    concept_id,
    concept_name,
    domain_id,
    vocabulary_id,
    concept_code,
    standard_concept
FROM 
    concept
WHERE 
    concept_code = '34714-6'
    AND vocabulary_id = 'LOINC';
```

### Example 2: Finding Standard Concept for Non-Standard LOINC Concept

```sql
-- If the LOINC concept is not standard, find its standard mapping
SELECT 
    c1.concept_id AS source_concept_id,
    c1.concept_name AS source_concept_name,
    c2.concept_id AS standard_concept_id,
    c2.concept_name AS standard_concept_name,
    c2.domain_id,
    c2.vocabulary_id
FROM 
    concept c1
JOIN 
    concept_relationship cr ON c1.concept_id = cr.concept_id_1
JOIN 
    concept c2 ON cr.concept_id_2 = c2.concept_id
WHERE 
    c1.concept_code = '34714-6'
    AND c1.vocabulary_id = 'LOINC'
    AND cr.relationship_id = 'Maps to'
    AND c2.standard_concept = 'S';
```

### Example 3: Complete FHIR Observation to OMOP Measurement Mapping

```sql
-- Pseudocode for mapping FHIR Observation with LOINC code to OMOP Measurement
INSERT INTO measurement (
    person_id,
    measurement_concept_id,
    measurement_date,
    measurement_datetime,
    measurement_type_concept_id,
    value_as_number,
    value_as_concept_id,
    unit_concept_id,
    measurement_source_value,
    measurement_source_concept_id
)
SELECT
    person_id, -- Mapped from FHIR Observation.subject reference
    standard_concept_id, -- From the mapping query above
    observation_date, -- From FHIR Observation.effectiveDateTime
    observation_datetime, -- From FHIR Observation.effectiveDateTime
    type_concept_id, -- Based on provenance of the observation
    value_as_number, -- From FHIR Observation.valueQuantity.value
    value_as_concept_id, -- If the value is coded
    unit_concept_id, -- Mapped from FHIR Observation.valueQuantity.code
    loinc_code, -- Original LOINC code from FHIR Observation.code.coding.code
    source_concept_id -- From the initial concept lookup
FROM
    fhir_observation_staging;
```

## Common Challenges and Solutions

1. **Non-standard LOINC Codes**:
   - Some LOINC codes may not be standard concepts in OMOP.
   - Solution: Use the CONCEPT_RELATIONSHIP table to find the standard concept that the LOINC code maps to.

2. **LOINC Version Differences**:
   - FHIR systems may use different versions of LOINC than what's in the OMOP vocabulary.
   - Solution: Regularly update the OMOP vocabulary from Athena to ensure the latest LOINC codes are available.

3. **Special Characters in LOINC Codes**:
   - LOINC codes contain hyphens which can sometimes cause issues in string comparisons.
   - Solution: Ensure proper string handling in queries, potentially using TRIM() or other string functions to normalize codes.

4. **Domain Assignment**:
   - Some LOINC codes might map to concepts in the OBSERVATION domain rather than MEASUREMENT.
   - Solution: Always check the domain_id of the standard concept and route the data to the appropriate OMOP table.

## Python Code Example for FHIR to OMOP LOINC Mapping

```python
import pandas as pd
import sqlalchemy as sa

# Connect to the OMOP database
engine = sa.create_engine('postgresql://username:password@localhost:5432/omop_cdm')

def map_loinc_to_omop(loinc_code):
    """
    Map a LOINC code to its corresponding OMOP standard concept
    
    Args:
        loinc_code (str): The LOINC code to map
        
    Returns:
        dict: A dictionary containing the mapping information
    """
    # Query to find the concept and its standard mapping if needed
    query = """
    WITH source_concept AS (
        SELECT 
            concept_id,
            concept_name,
            domain_id,
            standard_concept
        FROM 
            concept
        WHERE 
            concept_code = :loinc_code
            AND vocabulary_id = 'LOINC'
    )
    SELECT 
        sc.concept_id AS source_concept_id,
        sc.concept_name AS source_concept_name,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.concept_id
            ELSE c2.concept_id 
        END AS standard_concept_id,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.concept_name
            ELSE c2.concept_name 
        END AS standard_concept_name,
        CASE 
            WHEN sc.standard_concept = 'S' THEN sc.domain_id
            ELSE c2.domain_id 
        END AS domain_id
    FROM 
        source_concept sc
    LEFT JOIN 
        concept_relationship cr ON sc.concept_id = cr.concept_id_1 AND cr.relationship_id = 'Maps to'
    LEFT JOIN 
        concept c2 ON cr.concept_id_2 = c2.concept_id AND c2.standard_concept = 'S'
    """
    
    with engine.connect() as conn:
        result = conn.execute(sa.text(query), {"loinc_code": loinc_code}).fetchone()
    
    if result is None:
        return {"error": f"LOINC code {loinc_code} not found in OMOP vocabulary"}
    
    return {
        "source_concept_id": result.source_concept_id,
        "source_concept_name": result.source_concept_name,
        "standard_concept_id": result.standard_concept_id,
        "standard_concept_name": result.standard_concept_name,
        "domain_id": result.domain_id
    }

def process_fhir_observation(fhir_observation_json):
    """
    Process a FHIR Observation resource and map its LOINC code to OMOP
    
    Args:
        fhir_observation_json (dict): The FHIR Observation resource as a JSON object
        
    Returns:
        dict: The data ready to be inserted into the appropriate OMOP table
    """
    # Extract the LOINC code from the FHIR Observation
    loinc_code = None
    for coding in fhir_observation_json.get("code", {}).get("coding", []):
        if coding.get("system") == "http://loinc.org":
            loinc_code = coding.get("code")
            break
    
    if not loinc_code:
        return {"error": "No LOINC code found in the FHIR Observation"}
    
    # Map the LOINC code to OMOP
    mapping = map_loinc_to_omop(loinc_code)
    if "error" in mapping:
        return mapping
    
    # Determine the appropriate OMOP table based on the domain
    omop_table = "measurement" if mapping["domain_id"] == "Measurement" else "observation"
    
    # Extract other relevant data from the FHIR Observation
    # (This is simplified and would need to be expanded for a real implementation)
    person_id = extract_person_id(fhir_observation_json.get("subject", {}).get("reference"))
    observation_datetime = fhir_observation_json.get("effectiveDateTime")
    
    # Extract the value
    value_as_number = None
    value_as_concept_id = None
    unit_concept_id = None
    
    if "valueQuantity" in fhir_observation_json:
        value_as_number = fhir_observation_json["valueQuantity"].get("value")
        unit_code = fhir_observation_json["valueQuantity"].get("code")
        if unit_code:
            unit_concept_id = map_unit_to_concept_id(unit_code)
    elif "valueCodeableConcept" in fhir_observation_json:
        for coding in fhir_observation_json["valueCodeableConcept"].get("coding", []):
            value_code = coding.get("code")
            value_system = coding.get("system")
            if value_code and value_system:
                value_as_concept_id = map_code_to_concept_id(value_code, value_system)
                break
    
    # Prepare the data for insertion into the appropriate OMOP table
    omop_data = {
        "person_id": person_id,
        f"{omop_table}_concept_id": mapping["standard_concept_id"],
        f"{omop_table}_date": observation_datetime.split("T")[0] if observation_datetime else None,
        f"{omop_table}_datetime": observation_datetime,
        f"{omop_table}_type_concept_id": 32879,  # EHR
        f"{omop_table}_source_value": loinc_code,
        f"{omop_table}_source_concept_id": mapping["source_concept_id"]
    }
    
    if omop_table == "measurement":
        omop_data.update({
            "value_as_number": value_as_number,
            "value_as_concept_id": value_as_concept_id,
            "unit_concept_id": unit_concept_id
        })
    else:
        omop_data.update({
            "value_as_number": value_as_number,
            "value_as_concept_id": value_as_concept_id,
            "unit_concept_id": unit_concept_id
        })
    
    return {
        "table": omop_table,
        "data": omop_data
    }

# Helper functions (would need to be implemented)
def extract_person_id(subject_reference):
    # Extract person_id from FHIR reference (e.g., "Patient/123")
    if subject_reference and subject_reference.startswith("Patient/"):
        return subject_reference.split("/")[1]
    return None

def map_unit_to_concept_id(unit_code):
    # Map UCUM unit code to OMOP concept_id
    # This would require a lookup in the CONCEPT table
    return None  # Placeholder

def map_code_to_concept_id(code, system):
    # Map a code from a coding system to an OMOP concept_id
    # This would require a lookup in the CONCEPT table
    return None  # Placeholder
```

## Best Practices for LOINC to OMOP Mapping

1. **Use Athena for Vocabulary Lookup**:
   - Athena (https://athena.ohdsi.org) is the official OHDSI tool for browsing and downloading the standardized vocabularies.
   - Use it to look up LOINC codes and understand their mappings.

2. **Handle LOINC Panels Appropriately**:
   - LOINC includes panel codes that represent groups of related tests.
   - In OMOP, each individual component should be mapped separately rather than using the panel code.

3. **Maintain Source Values**:
   - Always preserve the original LOINC code in the source_value field for traceability.

4. **Validate Mappings**:
   - Regularly validate that your mappings are correct by comparing the meaning of the LOINC term with the mapped OMOP concept.

5. **Stay Updated with Vocabulary Releases**:
   - OMOP vocabularies are updated regularly. Ensure your system uses the latest version to have the most comprehensive LOINC coverage.

## References

1. OHDSI. "The Book of OHDSI: Chapter 5 Standardized Vocabularies." https://ohdsi.github.io/TheBookOfOhdsi/StandardizedVocabularies.html
2. HL7. "FHIR Coded Source Data to OMOP Patterns." https://confluence.hl7.org/pages/viewpage.action?pageId=256517831
3. OHDSI Forums. "How to map local specific terminology into standardized concepts?" https://forums.ohdsi.org/t/how-to-map-local-specific-terminology-into-standardized-concepts/14804
4. Stack Overflow. "ETL Mapping LOINC vocabulary on OMOP Common Data Model." https://stackoverflow.com/questions/74562441/etl-mapping-loinc-vocabulary-on-omop-common-data-model
