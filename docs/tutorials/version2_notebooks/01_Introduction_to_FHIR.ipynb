{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to FHIR (Fast Healthcare Interoperability Resources)\n", "\n", "## Overview\n", "\n", "This tutorial introduces the fundamentals of FHIR (Fast Healthcare Interoperability Resources), the modern standard for healthcare data exchange. By the end of this tutorial, you'll understand:\n", "\n", "- What FHIR is and why it was developed\n", "- The core components of the FHIR standard\n", "- How FHIR resources are structured\n", "- How to work with FHIR data using Python\n", "- How to query FHIR servers using REST APIs\n", "\n", "## Prerequisites\n", "\n", "- Basic Python programming knowledge\n", "- Familiarity with healthcare concepts\n", "- Understanding of JSON data structures\n", "\n", "Let's begin by importing the libraries we'll need for this tutorial:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import requests\n", "import json\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import os\n", "\n", "# Set up visualization style\n", "plt.style.use('ggplot')\n", "sns.set(style=\"whitegrid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. What is FHIR?\n", "\n", "FHIR (Fast Healthcare Interoperability Resources) is a standard for healthcare data exchange, developed by HL7 International. It's designed to address the challenges of sharing healthcare information across different systems and organizations.\n", "\n", "FHIR combines the best features of previous HL7 standards (like HL7 v2 and v3) with modern web technologies:\n", "\n", "- Uses RESTful APIs for data exchange\n", "- Represents data in structured formats like JSON and XML\n", "- Supports both human-readable and machine-processable content\n", "- Designed with implementation in mind\n", "\n", "FHIR has gained widespread adoption because it balances the complex requirements of healthcare with the technical needs of modern software development."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Core Components of FHIR\n", "\n", "FHIR is built around several key components:\n", "\n", "### Resources\n", "\n", "Resources are the fundamental building blocks of FHIR. Each resource represents a discrete unit of healthcare information, such as:\n", "\n", "- Patient: demographic information about an individual\n", "- Observation: measurements and simple assertions about a patient\n", "- Condition: clinical conditions or diagnoses\n", "- Medication: definition and properties of a medication\n", "- Procedure: actions performed for or on a patient\n", "\n", "There are over 140 different resource types in FHIR R4 (Release 4)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Example: Patient Resource\n", "\n", "Here's a simplified example of a FHIR Patient resource in JSON format:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "  \"resourceType\": \"Patient\",\n", "  \"id\": \"example\",\n", "  \"active\": true,\n", "  \"name\": [\n", "    {\n", "      \"use\": \"official\",\n", "      \"family\": \"<PERSON>\",\n", "      \"given\": [\n", "        \"<PERSON>\",\n", "        \"Adam\"\n", "      ]\n", "    }\n", "  ],\n", "  \"gender\": \"male\",\n", "  \"birthDate\": \"1974-12-25\",\n", "  \"address\": [\n", "    {\n", "      \"use\": \"home\",\n", "      \"line\": [\n", "        \"123 Main St\"\n", "      ],\n", "      \"city\": \"Anytown\",\n", "      \"state\": \"CA\",\n", "      \"postalCode\": \"12345\",\n", "      \"country\": \"USA\"\n", "    }\n", "  ],\n", "  \"telecom\": [\n", "    {\n", "      \"system\": \"phone\",\n", "      \"value\": \"************\",\n", "      \"use\": \"home\"\n", "    },\n", "    {\n", "      \"system\": \"email\",\n", "      \"value\": \"<EMAIL>\"\n", "    }\n", "  ]\n", "}\n"]}], "source": ["# Example of a FHIR Patient resource\n", "patient_example = {\n", "  \"resourceType\": \"Patient\",\n", "  \"id\": \"example\",\n", "  \"active\": True,\n", "  \"name\": [\n", "    {\n", "      \"use\": \"official\",\n", "      \"family\": \"<PERSON>\",\n", "      \"given\": [\"<PERSON>\", \"<PERSON>\"]\n", "    }\n", "  ],\n", "  \"gender\": \"male\",\n", "  \"birthDate\": \"1974-12-25\",\n", "  \"address\": [\n", "    {\n", "      \"use\": \"home\",\n", "      \"line\": [\"123 Main St\"],\n", "      \"city\": \"Anytown\",\n", "      \"state\": \"CA\",\n", "      \"postalCode\": \"12345\",\n", "      \"country\": \"USA\"\n", "    }\n", "  ],\n", "  \"telecom\": [\n", "    {\n", "      \"system\": \"phone\",\n", "      \"value\": \"************\",\n", "      \"use\": \"home\"\n", "    },\n", "    {\n", "      \"system\": \"email\",\n", "      \"value\": \"<EMAIL>\"\n", "    }\n", "  ]\n", "}\n", "\n", "print(json.dumps(patient_example, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Types\n", "\n", "FHIR defines various data types used across resources:\n", "\n", "- Primitive types: string, boolean, integer, decimal, etc.\n", "- Complex types: Address, HumanName, ContactPoint, etc.\n", "- Resource references: links between resources\n", "\n", "### Extensions\n", "\n", "Extensions allow for customizing resources to include additional information not covered by the base standard. This flexibility is crucial for adapting FHIR to specific use cases while maintaining compatibility."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. FHIR Resource Structure\n", "\n", "All FHIR resources share a common structure:\n", "\n", "- **resourceType**: Identifies the type of resource\n", "- **id**: Unique identifier for this instance of the resource\n", "- **meta**: Metadata about the resource\n", "- **Resource-specific data elements**: The actual content of the resource\n", "\n", "Resources can be represented in different formats, with JSON and XML being the most common."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Working with FHIR Data in Python\n", "\n", "Python is an excellent language for working with FHIR data due to its rich ecosystem of libraries. Let's explore how to:\n", "\n", "1. Fetch data from a FHIR server\n", "2. Parse and analyze FHIR resources\n", "3. Create and update FHIR resources"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Function to fetch data from a FHIR server\n", "def fetch_fhir_resource(server_url, resource_type, resource_id=None, search_params=None):\n", "    \"\"\"\n", "    Fetch FHIR resources from a server\n", "    \n", "    Parameters:\n", "    - server_url: Base URL of the FHIR server\n", "    - resource_type: Type of resource to fetch (e.g., 'Patient', 'Observation')\n", "    - resource_id: Optional ID of a specific resource\n", "    - search_params: Optional dictionary of search parameters\n", "    \n", "    Returns:\n", "    - JSON response from the server\n", "    \"\"\"\n", "    # Construct the URL\n", "    if resource_id:\n", "        url = f\"{server_url}/{resource_type}/{resource_id}\"\n", "    else:\n", "        url = f\"{server_url}/{resource_type}\"\n", "    \n", "    # Add search parameters if provided\n", "    if search_params:\n", "        query_string = \"&\".join([f\"{k}={v}\" for k, v in search_params.items()])\n", "        url = f\"{url}?{query_string}\"\n", "    \n", "    # Set headers for JSON response\n", "    headers = {\n", "        'Accept': 'application/fhir+json',\n", "        'Content-Type': 'application/fhir+json'\n", "    }\n", "    \n", "    try:\n", "        response = requests.get(url, headers=headers)\n", "        response.raise_for_status()  # Raise exception for HTTP errors\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Error fetching FHIR resource: {e}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's use a public FHIR server to demonstrate fetching data:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved 2 patients\n", "\n", "First patient details:\n", "{\n", "  \"resourceType\": \"Patient\",\n", "  \"id\": \"patient-1\",\n", "  \"name\": [\n", "    {\n", "      \"family\": \"<PERSON>\",\n", "      \"given\": [\n", "        \"<PERSON>\"\n", "      ]\n", "    }\n", "  ],\n", "  \"gender\": \"female\",\n", "  \"birthDate\": \"1985-08-12\"\n", "}\n"]}], "source": ["# Example: Fetching patient data from a public FHIR server\n", "# Note: This is a demonstration using a public test server\n", "server_url = \"https://hapi.fhir.org/baseR4\"\n", "resource_type = \"Patient\"\n", "search_params = {\"_count\": \"5\"}  # Limit to 5 results\n", "\n", "# This code is commented out to avoid making actual API calls during tutorial execution\n", "# Uncomment to run against a real FHIR server\n", "\"\"\"\n", "patients = fetch_fhir_resource(server_url, resource_type, search_params=search_params)\n", "if patients and 'entry' in patients:\n", "    print(f\"Retrieved {len(patients['entry'])} patients\")\n", "    \n", "    # Display the first patient\n", "    if len(patients['entry']) > 0:\n", "        first_patient = patients['entry'][0]['resource']\n", "        print(\"\\nFirst patient details:\")\n", "        print(json.dumps(first_patient, indent=2))\n", "else:\n", "    print(\"No patients retrieved or error occurred\")\n", "\"\"\"\n", "\n", "# For tutorial purposes, we'll use a simulated response\n", "simulated_response = {\n", "  \"resourceType\": \"Bundle\",\n", "  \"type\": \"searchset\",\n", "  \"total\": 5,\n", "  \"entry\": [\n", "    {\n", "      \"resource\": {\n", "        \"resourceType\": \"Patient\",\n", "        \"id\": \"patient-1\",\n", "        \"name\": [{\"family\": \"<PERSON>\", \"given\": [\"<PERSON>\"]}],\n", "        \"gender\": \"female\",\n", "        \"birthDate\": \"1985-08-12\"\n", "      }\n", "    },\n", "    {\n", "      \"resource\": {\n", "        \"resourceType\": \"Patient\",\n", "        \"id\": \"patient-2\",\n", "        \"name\": [{\"family\": \"<PERSON>\", \"given\": [\"<PERSON>\"]}],\n", "        \"gender\": \"male\",\n", "        \"birthDate\": \"1964-03-25\"\n", "      }\n", "    }\n", "  ]\n", "}\n", "\n", "print(f\"Retrieved {len(simulated_response['entry'])} patients\")\n", "print(\"\\nFirst patient details:\")\n", "print(json.dumps(simulated_response['entry'][0]['resource'], indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyzing FHIR Data with <PERSON>das\n", "\n", "We can convert FHIR resources to pandas DataFrames for analysis:"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          id  gender  birth_date family_name given_names\n", "0  patient-1  female  1985-08-12     <PERSON>\n", "1  patient-2    male  1964-03-25    <PERSON>\n"]}], "source": ["# Function to convert FHIR Patient resources to a pandas DataFrame\n", "def patients_to_dataframe(patients_bundle):\n", "    \"\"\"Convert a bundle of FHIR Patient resources to a pandas DataFrame\"\"\"\n", "    if 'entry' not in patients_bundle:\n", "        return pd.DataFrame()\n", "    \n", "    patients_data = []\n", "    \n", "    for entry in patients_bundle['entry']:\n", "        patient = entry['resource']\n", "        \n", "        # Extract basic information\n", "        patient_data = {\n", "            'id': patient.get('id', ''),\n", "            'gender': patient.get('gender', ''),\n", "            'birth_date': patient.get('birthDate', '')\n", "        }\n", "        \n", "        # Extract name (using the first name in the list if available)\n", "        if 'name' in patient and len(patient['name']) > 0:\n", "            name = patient['name'][0]\n", "            patient_data['family_name'] = name.get('family', '')\n", "            patient_data['given_names'] = ' '.join(name.get('given', []))\n", "        \n", "        patients_data.append(patient_data)\n", "    \n", "    return pd.DataFrame(patients_data)\n", "\n", "# Convert our simulated response to a DataFrame\n", "patients_df = patients_to_dataframe(simulated_response)\n", "print(patients_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating and Updating FHIR Resources"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Example of a new Patient resource that could be created:\n", "{\n", "  \"resourceType\": \"Patient\",\n", "  \"active\": true,\n", "  \"name\": [\n", "    {\n", "      \"use\": \"official\",\n", "      \"family\": \"Doe\",\n", "      \"given\": [\n", "        \"Jane\"\n", "      ]\n", "    }\n", "  ],\n", "  \"gender\": \"female\",\n", "  \"birthDate\": \"1990-01-15\",\n", "  \"address\": [\n", "    {\n", "      \"use\": \"home\",\n", "      \"line\": [\n", "        \"456 Oak Street\"\n", "      ],\n", "      \"city\": \"Springfield\",\n", "      \"state\": \"IL\",\n", "      \"postalCode\": \"62704\"\n", "    }\n", "  ]\n", "}\n"]}], "source": ["# Function to create a new FHIR resource\n", "def create_fhir_resource(server_url, resource):\n", "    \"\"\"\n", "    Create a new FHIR resource on the server\n", "    \n", "    Parameters:\n", "    - server_url: Base URL of the FHIR server\n", "    - resource: Dictionary containing the FHIR resource\n", "    \n", "    Returns:\n", "    - Response from the server\n", "    \"\"\"\n", "    resource_type = resource.get('resourceType')\n", "    if not resource_type:\n", "        raise ValueError(\"Resource must have a resourceType\")\n", "    \n", "    url = f\"{server_url}/{resource_type}\"\n", "    \n", "    headers = {\n", "        'Content-Type': 'application/fhir+json',\n", "        'Accept': 'application/fhir+json'\n", "    }\n", "    \n", "    try:\n", "        response = requests.post(url, headers=headers, json=resource)\n", "        response.raise_for_status()\n", "        return response.json()\n", "    except requests.exceptions.RequestException as e:\n", "        print(f\"Error creating FHIR resource: {e}\")\n", "        return None\n", "\n", "# Example: Creating a new Patient resource\n", "new_patient = {\n", "    \"resourceType\": \"Patient\",\n", "    \"active\": True,\n", "    \"name\": [\n", "        {\n", "            \"use\": \"official\",\n", "            \"family\": \"Doe\",\n", "            \"given\": [\"<PERSON>\"]\n", "        }\n", "    ],\n", "    \"gender\": \"female\",\n", "    \"birthDate\": \"1990-01-15\",\n", "    \"address\": [\n", "        {\n", "            \"use\": \"home\",\n", "            \"line\": [\"456 Oak Street\"],\n", "            \"city\": \"Springfield\",\n", "            \"state\": \"IL\",\n", "            \"postalCode\": \"62704\"\n", "        }\n", "    ]\n", "}\n", "\n", "# This code is commented out to avoid making actual API calls during tutorial execution\n", "\"\"\"\n", "created_patient = create_fhir_resource(server_url, new_patient)\n", "if created_patient:\n", "    print(\"Patient created successfully:\")\n", "    print(json.dumps(created_patient, indent=2))\n", "else:\n", "    print(\"Failed to create patient\")\n", "\"\"\"\n", "\n", "print(\"Example of a new Patient resource that could be created:\")\n", "print(json.dumps(new_patient, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Querying FHIR Servers\n", "\n", "FHIR servers support a rich query language based on REST principles. You can search for resources based on various parameters."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Example FHIR search URLs:\n", "Female patients: https://hapi.fhir.org/baseR4/Patient?gender=female\n", "Patients named <PERSON>: https://hapi.fhir.org/baseR4/Patient?family=<PERSON>\n", "Patients born after 2000: https://hapi.fhir.org/baseR4/Patient?birthdate=gt2000-01-01\n", "Complex search: https://hapi.fhir.org/baseR4/Patient?gender=male&family=Johnson&_sort=birthdate&_count=10\n"]}], "source": ["# Examples of FHIR search queries\n", "\n", "# Search for all female patients\n", "female_search = {\"gender\": \"female\"}\n", "\n", "# Search for patients with a specific family name\n", "name_search = {\"family\": \"<PERSON>\"}\n", "\n", "# Search for patients born after a certain date\n", "date_search = {\"birthdate\": \"gt2000-01-01\"}\n", "\n", "# Search for patients with multiple criteria\n", "complex_search = {\n", "    \"gender\": \"male\",\n", "    \"family\": \"<PERSON>\",\n", "    \"_sort\": \"birthdate\",\n", "    \"_count\": \"10\"\n", "}\n", "\n", "# Print example URLs for these searches\n", "base_url = \"https://hapi.fhir.org/baseR4/Patient\"\n", "\n", "print(\"Example FHIR search URLs:\")\n", "for name, params in [\n", "    (\"Female patients\", female_search),\n", "    (\"Patients named <PERSON>\", name_search),\n", "    (\"Patients born after 2000\", date_search),\n", "    (\"Complex search\", complex_search)\n", "]:\n", "    query_string = \"&\".join([f\"{k}={v}\" for k, v in params.items()])\n", "    print(f\"{name}: {base_url}?{query_string}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. FHIR Versions and Compatibility\n", "\n", "FHIR has evolved through several versions:\n", "\n", "- DSTU1 (Draft Standard for Trial Use 1): Initial draft\n", "- DSTU2: Significant improvements and wider adoption\n", "- STU3 (Standard for Trial Use 3): Further refinements\n", "- R4 (Release 4): First normative content, current stable version\n", "- R5: Latest version with additional resources and capabilities\n", "\n", "Most implementations today use R4, which includes normative content that won't change in future versions without backward compatibility.\n", "\n", "When working with FHIR, it's important to know which version you're targeting, as there can be differences in resource structures and available features."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Conc<PERSON>\n", "\n", "In this tutorial, we've covered the fundamentals of FHIR:\n", "\n", "- FHIR is a modern standard for healthcare data exchange\n", "- Resources are the building blocks of FHIR, representing discrete units of healthcare information\n", "- FHIR uses RESTful APIs and common formats like JSON\n", "- Python provides excellent tools for working with FHIR data\n", "\n", "In the next tutorial, we'll explore the OMOP Common Data Model and begin to understand how FHIR data can be mapped to OMOP."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Additional Resources\n", "\n", "- [Official FHIR Documentation](https://www.hl7.org/fhir/)\n", "- [FHIR Resource List](https://www.hl7.org/fhir/resourcelist.html)\n", "- [Public FHIR Test Servers](https://wiki.hl7.org/Publicly_Available_FHIR_Servers_for_testing)\n", "- [FHIR Python Client (fhir.resources)](https://github.com/nazrulworld/fhir.resources)"]}], "metadata": {"kernelspec": {"display_name": "fhir-omop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 4}