{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP CDM (Observational Medical Outcomes Partnership Common Data Model)\n", "\n", "## Overview\n", "\n", "This tutorial introduces the OMOP Common Data Model (CDM), a standardized format for healthcare data. By the end of this tutorial, you'll understand:\n", "\n", "- What OMOP CDM is and why it was developed\n", "- The core components and structure of the OMOP CDM\n", "- How OMOP standardizes healthcare concepts\n", "- The benefits of using OMOP for healthcare analytics\n", "- How to work with OMOP data using Python\n", "\n", "## Prerequisites\n", "\n", "- Basic SQL knowledge\n", "- Familiarity with healthcare concepts\n", "- Understanding of relational database principles\n", "- Python programming knowledge\n", "\n", "Let's begin by importing the libraries we'll need for this tutorial:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import sqlite3\n", "from datetime import datetime\n", "import os\n", "\n", "# Set up visualization style\n", "plt.style.use('ggplot')\n", "sns.set(style=\"whitegrid\")\n", "\n", "# Configure pandas to display all columns in a single line\n", "pd.set_option('display.width', 1000)\n", "pd.set_option('display.max_columns', 10)\n", "pd.set_option('display.expand_frame_repr', False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. What is OMOP CDM?\n", "\n", "The Observational Medical Outcomes Partnership (OMOP) Common Data Model (CDM) is a standardized format for healthcare data designed to facilitate research and analytics across multiple healthcare datasets.\n", "\n", "OMOP CDM was developed by the Observational Health Data Sciences and Informatics (OHDSI, pronounced \"Odyssey\") program, a multi-stakeholder, interdisciplinary collaborative that aims to bring out the value of health data through large-scale analytics.\n", "\n", "Key characteristics of OMOP CDM:\n", "\n", "- **Standardized structure**: Defines a common way to organize healthcare data\n", "- **Standardized vocabularies**: Uses consistent terminology across all data\n", "- **Person-centric**: Organizes data around individual patients/persons\n", "- **Research-focused**: Designed to support observational research and analytics\n", "- **Open-source**: Freely available for use by the healthcare community\n", "\n", "The current stable version is OMOP CDM v5.4, with v6.0 in development."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Why Use OMOP CDM?\n", "\n", "Healthcare data comes from many different sources (EHRs, claims, registries) in many different formats. This heterogeneity creates significant challenges for research and analytics:\n", "\n", "- Different coding systems (ICD-10, SNOMED CT, LOINC, etc.)\n", "- Different data structures\n", "- Different naming conventions\n", "- Different levels of detail\n", "\n", "OMOP CDM addresses these challenges by:\n", "\n", "- Providing a single, common format for all data\n", "- Standardizing all clinical concepts to common vocabularies\n", "- Enabling consistent analytics across multiple datasets\n", "- Supporting reproducible research\n", "\n", "By converting data to OMOP CDM, researchers can develop analysis code once and apply it to multiple datasets, dramatically increasing research efficiency and reliability."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. OMOP CDM Structure\n", "\n", "OMOP CDM organizes healthcare data into several categories of tables:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OMOP CDM Table Categories:\n", "\n", "Clinical Data Tables (11 tables):\n", "  - PERSON\n", "  - OBSERVATION_PERIOD\n", "  - VISIT_OCCURRENCE\n", "  - CONDITION_OCCURRENCE\n", "  - DRUG_EXPOSURE\n", "  - PROCEDURE_OCCURRENCE\n", "  - DEVICE_EXPOSURE\n", "  - MEASUREMENT\n", "  - OBSERVATION\n", "  - DEATH\n", "  - NOTE\n", "\n", "Health System Data Tables (3 tables):\n", "  - LOCATION\n", "  - CARE_SITE\n", "  - PROVIDER\n", "\n", "Health Economics Data Tables (2 tables):\n", "  - PAYER_PLAN_PERIOD\n", "  - COST\n", "\n", "Standardized Vocabularies (8 tables):\n", "  - CONCEPT\n", "  - VOCABULARY\n", "  - DOMAIN\n", "  - CONCEPT_CLASS\n", "  - CONCEPT_RELATIONSHIP\n", "  - CONCEPT_ANCESTOR\n", "  - CONCEPT_SYNONYM\n", "  - DRUG_STRENGTH\n", "\n", "Metadata Tables (2 tables):\n", "  - CDM_SOURCE\n", "  - METADATA\n"]}], "source": ["# Create a simple visualization of OMOP CDM table categories\n", "categories = [\n", "    'Clinical Data Tables', \n", "    'Health System Data Tables',\n", "    'Health Economics Data Tables', \n", "    'Standardized Vocabularies',\n", "    'Metadata Tables'\n", "]\n", "\n", "tables_by_category = {\n", "    'Clinical Data Tables': [\n", "        'PERSON', \n", "        'OBSERVATION_PERIOD', 'VISIT_OCCURRENCE', \n", "        'CONDITION_OCCURRENCE', 'DRUG_EXPOSURE', 'PROCEDURE_OCCURRENCE',\n", "        'DEVICE_EXPOSURE', 'MEASUREMENT', 'OBSERVATION', 'DEATH', 'NOTE'\n", "    ],\n", "    'Health System Data Tables': [\n", "        'LOCATION', 'CARE_SITE', 'PROVIDER'\n", "    ],\n", "    'Health Economics Data Tables': [\n", "        'PAYER_PLAN_PERIOD', 'COST'\n", "    ],\n", "    'Standardized Vocabularies': [\n", "        'CONCEPT', 'VOCABULARY', 'DOMAIN', 'CONCEPT_CLASS',\n", "        'CONCEPT_RELATIONSHIP', 'CONCEPT_ANCESTOR', 'CONCEPT_SYNONYM',\n", "        'DRUG_STRENGTH'\n", "    ],\n", "    'Metadata Tables': [\n", "        'CDM_SOURCE', 'METADATA'\n", "    ]\n", "}\n", "\n", "# Create a DataFrame for visualization\n", "table_counts = [len(tables_by_category[cat]) for cat in categories]\n", "category_df = pd.DataFrame({\n", "    'Category': categories,\n", "    'Number of Tables': table_counts\n", "})\n", "\n", "# Plot\n", "plt.figure(figsize=(10, 6))\n", "bars = plt.bar(category_df['Category'], category_df['Number of Tables'], color='skyblue')\n", "plt.xlabel('OMOP CDM Table Categories')\n", "plt.ylabel('Number of Tables')\n", "plt.title('OMOP CDM Structure Overview')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "\n", "# Add table count labels on top of bars\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,\n", "             f'{height}', ha='center', va='bottom')\n", "\n", "# Instead of displaying the plot, we'll just describe it for the tutorial\n", "print(\"OMOP CDM Table Categories:\")\n", "for category, tables in tables_by_category.items():\n", "    print(f\"\\n{category} ({len(tables)} tables):\")\n", "    for table in tables:\n", "        print(f\"  - {table}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Key Clinical Data Tables\n", "\n", "Let's explore some of the most important clinical data tables in more detail:\n", "\n", "#### PERSON\n", "\n", "The PERSON table contains basic demographic information about the patients/persons in the database:\n", "\n", "- person_id (primary key)\n", "- gender_concept_id\n", "- birth_datetime\n", "- race_concept_id\n", "- ethnicity_concept_id\n", "- location_id\n", "- provider_id\n", "- care_site_id\n", "\n", "#### CONDITION_OCCURRENCE\n", "\n", "The CONDITION_OCCURRENCE table records diagnoses or conditions:\n", "\n", "- condition_occurrence_id (primary key)\n", "- person_id (foreign key to PERSON)\n", "- condition_concept_id\n", "- condition_start_date\n", "- condition_end_date\n", "- condition_type_concept_id\n", "- visit_occurrence_id\n", "\n", "#### DRUG_EXPOSURE\n", "\n", "The DRUG_EXPOSURE table captures medications and drug exposures:\n", "\n", "- drug_exposure_id (primary key)\n", "- person_id (foreign key to PERSON)\n", "- drug_concept_id\n", "- drug_exposure_start_date\n", "- drug_exposure_end_date\n", "- drug_type_concept_id\n", "- quantity\n", "- visit_occurrence_id\n", "\n", "#### MEASUREMENT\n", "\n", "The MEASUREMENT table stores laboratory tests and clinical measurements:\n", "\n", "- measurement_id (primary key)\n", "- person_id (foreign key to PERSON)\n", "- measurement_concept_id\n", "- measurement_date\n", "- measurement_type_concept_id\n", "- operator_concept_id\n", "- value_as_number\n", "- value_as_concept_id\n", "- unit_concept_id\n", "- visit_occurrence_id"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. OMOP Standardized Vocabularies\n", "\n", "One of the most powerful features of OMOP CDM is its standardized vocabulary system. All clinical concepts (conditions, drugs, procedures, measurements, etc.) are mapped to standard concept identifiers.\n", "\n", "The core of this system is the CONCEPT table, which contains over 5 million standardized concepts from various vocabularies:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Example CONCEPT table entries:\n", "   concept_id                    concept_name    domain_id vocabulary_id  concept_class_id\n", "0      192671                    Hypertension    Condition        SNOMED  Clinical Finding\n", "1     1545999  Atorvastatin 10 MG Oral Tablet         Drug        RxNorm     Clinical Drug\n", "2     2212392      Hemoglobin A1c measurement  Measurement         LOINC          Lab Test\n", "3     4230167    Coronary artery bypass graft    Procedure        SNOMED         Procedure\n"]}], "source": ["# Example structure of the CONCEPT table\n", "concept_columns = [\n", "    'concept_id', 'concept_name', 'domain_id', 'vocabulary_id', \n", "    'concept_class_id', 'standard_concept', 'concept_code',\n", "    'valid_start_date', 'valid_end_date', 'invalid_reason'\n", "]\n", "\n", "# Example concepts from different domains\n", "example_concepts = [\n", "    [192671, 'Hypertension', 'Condition', 'SNOMED', 'Clinical Finding', 'S', '38341003', '1970-01-01', '2099-12-31', None],\n", "    [1545999, 'Atorvastatin 10 MG Oral Tablet', 'Drug', 'RxNorm', 'Clinical Drug', 'S', '617311', '1970-01-01', '2099-12-31', None],\n", "    [2212392, 'Hemoglobin A1c measurement', 'Measurement', 'LOINC', 'Lab Test', 'S', '4548-4', '1970-01-01', '2099-12-31', None],\n", "    [4230167, 'Coronary artery bypass graft', 'Procedure', 'SNOMED', 'Procedure', 'S', '232717009', '1970-01-01', '2099-12-31', None]\n", "]\n", "\n", "# Create a DataFrame for display\n", "concept_df = pd.DataFrame(example_concepts, columns=concept_columns)\n", "print(\"Example CONCEPT table entries:\")\n", "\n", "# Display the relevant columns\n", "print(concept_df[['concept_id', 'concept_name', 'domain_id', 'vocabulary_id', 'concept_class_id']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Standard Vocabularies in OMOP\n", "\n", "OMOP uses a variety of standard vocabularies for different domains:\n", "\n", "- **Conditions**: SNOMED CT\n", "- **Drugs**: RxNorm, RxNorm Extension\n", "- **Procedures**: SNOMED CT, CPT4, ICD-10-PCS\n", "- **Measurements**: LOINC\n", "- **Observations**: SNOMED CT\n", "\n", "### Concept Relationships\n", "\n", "OMOP also maintains relationships between concepts, stored in the CONCEPT_RELATIONSHIP table:\n", "\n", "- Mappings from source vocabularies (like ICD-10-CM) to standard concepts\n", "- Hierarchical relationships (e.g., \"is a\" relationships)\n", "- Semantic relationships (e.g., \"has ingredient\")\n", "\n", "This rich network of relationships enables sophisticated analytics and cohort definitions."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Working with OMOP Data in Python\n", "\n", "Let's explore how to work with OMOP data using Python. For this tutorial, we'll create a small example SQLite database with OMOP tables."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["<sqlite3.Cursor at 0x12e895040>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create a simple SQLite database with OMOP structure\n", "conn = sqlite3.connect(':memory:')  # In-memory database for demonstration\n", "\n", "# Create simplified PERSON table\n", "conn.execute('''\n", "CREATE TABLE person (\n", "    person_id INTEGER PRIMARY KEY,\n", "    gender_concept_id INTEGER,\n", "    birth_datetime TEXT,\n", "    race_concept_id INTEGER,\n", "    ethnicity_concept_id INTEGER\n", ")\n", "''')\n", "\n", "# Create simplified CONDITION_OCCURRENCE table\n", "conn.execute('''\n", "CREATE TABLE condition_occurrence (\n", "    condition_occurrence_id INTEGER PRIMARY KEY,\n", "    person_id INTEGER,\n", "    condition_concept_id INTEGER,\n", "    condition_start_date TEXT,\n", "    condition_end_date TEXT,\n", "    FOREIGN KEY (person_id) REFERENCES person (person_id)\n", ")\n", "''')\n", "\n", "# Insert sample data\n", "conn.executemany('''\n", "INSERT INTO person (person_id, gender_concept_id, birth_datetime, race_concept_id, ethnicity_concept_id)\n", "VALUES (?, ?, ?, ?, ?)\n", "''', [\n", "    (1, 8507, '1960-05-12', 8516, 38003564),  # Male, White, Not Hispanic\n", "    (2, 8532, '1975-10-23', 8516, 38003564),  # Female, White, Not Hispanic\n", "    (3, 8532, '1988-03-01', 8527, 38003563),  # Female, Black, Hispanic\n", "    (4, 8507, '1945-12-10', 8515, 38003564),  # Male, Asian, Not Hispanic\n", "])\n", "\n", "conn.executemany('''\n", "INSERT INTO condition_occurrence (condition_occurrence_id, person_id, condition_concept_id, condition_start_date, condition_end_date)\n", "VALUES (?, ?, ?, ?, ?)\n", "''', [\n", "    (1, 1, 192671, '2020-01-15', None),  # Hypertension\n", "    (2, 1, 201820, '2020-03-20', None),  # Type 2 Diabetes\n", "    (3, 2, 192671, '2019-05-10', None),  # Hypertension\n", "    (4, 3, 255573, '2021-02-01', None),  # Asthma\n", "    (5, 4, 192671, '2018-11-22', None),  # Hypertension\n", "    (6, 4, 381591, '2019-08-15', None),  # At<PERSON> Fibrillation\n", "])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's query this data using pandas:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Persons in the database:\n", "   person_id  gender_concept_id birth_datetime  race_concept_id  ethnicity_concept_id\n", "0          1               8507     1960-05-12             8516              38003564\n", "1          2               8532     1975-10-23             8516              38003564\n", "2          3               8532     1988-03-01             8527              38003563\n", "3          4               8507     1945-12-10             8515              38003564\n", "\n", "Conditions in the database:\n", "   condition_occurrence_id  person_id  condition_concept_id condition_start_date condition_end_date\n", "0                        1          1                192671           2020-01-15               None\n", "1                        2          1                201820           2020-03-20               None\n", "2                        3          2                192671           2019-05-10               None\n", "3                        4          3                255573           2021-02-01               None\n", "4                        5          4                192671           2018-11-22               None\n", "5                        6          4                381591           2019-08-15               None\n"]}], "source": ["# Query all persons\n", "persons_df = pd.read_sql_query(\"SELECT * FROM person\", conn)\n", "print(\"Persons in the database:\")\n", "print(persons_df)\n", "\n", "# Query all conditions\n", "conditions_df = pd.read_sql_query(\"SELECT * FROM condition_occurrence\", conn)\n", "print(\"\\nConditions in the database:\")\n", "print(conditions_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Analyzing OMOP Data\n", "\n", "Let's perform some simple analyses on our OMOP data:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of conditions per person:\n", "   person_id  condition_count\n", "0          1                2\n", "1          2                1\n", "2          3                1\n", "3          4                2\n", "\n", "Distribution of conditions:\n", "   condition_concept_id  occurrence_count\n", "0                192671                 3\n", "1                381591                 1\n", "2                255573                 1\n", "3                201820                 1\n"]}], "source": ["# Count conditions by person\n", "condition_counts = pd.read_sql_query('''\n", "SELECT p.person_id, \n", "       COUNT(co.condition_occurrence_id) as condition_count\n", "FROM person p\n", "LEFT JOIN condition_occurrence co ON p.person_id = co.person_id\n", "GROUP BY p.person_id\n", "''', conn)\n", "\n", "print(\"Number of conditions per person:\")\n", "print(condition_counts)\n", "\n", "# Count occurrences of each condition\n", "condition_distribution = pd.read_sql_query('''\n", "SELECT condition_concept_id, \n", "       COUNT(*) as occurrence_count\n", "FROM condition_occurrence\n", "GROUP BY condition_concept_id\n", "ORDER BY occurrence_count DESC\n", "''', conn)\n", "\n", "print(\"\\nDistribution of conditions:\")\n", "print(condition_distribution)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In a real OMOP database, we would join with the CONCEPT table to get the actual condition names:"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "In a real OMOP database, we would join with the CONCEPT table:\n", "\n", "SELECT c.concept_name, COUNT(*) as occurrence_count\n", "FROM condition_occurrence co\n", "JOIN concept c ON co.condition_concept_id = c.concept_id\n", "GROUP BY co.condition_concept_id, c.concept_name\n", "ORDER BY occurrence_count DESC\n", "\n", "\n", "Distribution of conditions with names:\n", "        condition_name  occurrence_count\n", "0         Hypertension                 3\n", "1  Atrial Fibrillation                 1\n", "2               Asthma                 1\n", "3      Type 2 Diabetes                 1\n"]}], "source": ["# In a real database, we would do something like this:\n", "# (This is just for illustration - our simple database doesn't have a CONCEPT table)\n", "print(\"\\nIn a real OMOP database, we would join with the CONCEPT table:\")\n", "print('''\n", "SELECT c.concept_name, COUNT(*) as occurrence_count\n", "FROM condition_occurrence co\n", "JOIN concept c ON co.condition_concept_id = c.concept_id\n", "GROUP BY co.condition_concept_id, c.concept_name\n", "ORDER BY occurrence_count DESC\n", "''')\n", "\n", "# For our example, we'll manually map the concept IDs to names\n", "concept_map = {\n", "    192671: 'Hypertension',\n", "    201820: 'Type 2 Diabetes',\n", "    255573: 'Asthma',\n", "    381591: 'Atrial Fibrillation'\n", "}\n", "\n", "condition_distribution['condition_name'] = condition_distribution['condition_concept_id'].map(concept_map)\n", "print(\"\\nDistribution of conditions with names:\")\n", "print(condition_distribution[['condition_name', 'occurrence_count']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON>efits of OMOP for Analytics\n", "\n", "OMOP CDM offers several key benefits for healthcare analytics:\n", "\n", "### Standardization\n", "\n", "- Consistent data structure across different data sources\n", "- Standardized vocabularies for all clinical concepts\n", "- Enables apples-to-apples comparisons\n", "\n", "### Efficiency\n", "\n", "- Write analysis code once, run on multiple datasets\n", "- Reusable tools and methods\n", "- Faster time-to-insights\n", "\n", "### Collaboration\n", "\n", "- Common language for researchers\n", "- Easier to share methods and results\n", "- Growing community of users and developers\n", "\n", "### Scale\n", "\n", "- Designed for large-scale observational studies\n", "- Supports distributed research networks\n", "- Enables studies with hundreds of millions of patients"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. OMOP Tools and Resources\n", "\n", "The OHDSI community has developed numerous tools for working with OMOP CDM:\n", "\n", "- **ATLAS**: Web-based tool for cohort definition, characterization, and population-level analysis\n", "- **ACHILLES**: Data characterization and quality assessment\n", "- **WhiteRabbit/RabbitInAHat**: ETL design and data profiling\n", "- **ETL-Synthea**: Converts Synthea synthetic data to OMOP CDM\n", "- **HADES**: Suite of R packages for OMOP analytics\n", "- **PyOHDSI**: Python tools for OMOP analytics\n", "\n", "These tools make it easier to work with OMOP data and perform sophisticated analyses."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Conclusion\n", "\n", "In this tutorial, we've covered the fundamentals of the OMOP Common Data Model:\n", "\n", "- OMOP CDM is a standardized format for healthcare data\n", "- It uses standardized vocabularies to represent clinical concepts\n", "- The model is organized around persons and their healthcare encounters\n", "- OMOP enables efficient, reproducible healthcare analytics\n", "- Python can be used to analyze OMOP data effectively\n", "\n", "In the next tutorial, we'll explore how to map data from FHIR to OMOP CDM, bridging these two important healthcare data standards."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Additional Resources\n", "\n", "- [OHDSI Website](https://www.ohdsi.org/)\n", "- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)\n", "- [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)\n", "- [OHDSI GitHub](https://github.com/OHDSI)\n", "- [OMOP CDM on GitHub](https://github.com/OHDSI/CommonDataModel)"]}], "metadata": {"kernelspec": {"display_name": "fhir-omop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}