# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import sqlite3
from datetime import datetime
import os

# Set up visualization style
plt.style.use('ggplot')
sns.set(style="whitegrid")

# Configure pandas to display all columns in a single line
pd.set_option('display.width', 1000)
pd.set_option('display.max_columns', 10)
pd.set_option('display.expand_frame_repr', False)

# Create a simple visualization of OMOP CDM table categories
categories = [
    'Clinical Data Tables', 
    'Health System Data Tables',
    'Health Economics Data Tables', 
    'Standardized Vocabularies',
    'Metadata Tables'
]

tables_by_category = {
    'Clinical Data Tables': [
        'PERSON', 
        'OBSERVATION_PERIOD', 'VISIT_OCCURRENCE', 
        'CONDITION_OCCURRENCE', 'DRUG_EXPOSURE', 'PROCEDURE_OCCURRENCE',
        'DEVICE_EXPOSURE', 'MEASUREMENT', 'OBSERVATION', 'DEATH', 'NOTE'
    ],
    'Health System Data Tables': [
        'LOCATION', 'CARE_SITE', 'PROVIDER'
    ],
    'Health Economics Data Tables': [
        'PAYER_PLAN_PERIOD', 'COST'
    ],
    'Standardized Vocabularies': [
        'CONCEPT', 'VOCABULARY', 'DOMAIN', 'CONCEPT_CLASS',
        'CONCEPT_RELATIONSHIP', 'CONCEPT_ANCESTOR', 'CONCEPT_SYNONYM',
        'DRUG_STRENGTH'
    ],
    'Metadata Tables': [
        'CDM_SOURCE', 'METADATA'
    ]
}

# Create a DataFrame for visualization
table_counts = [len(tables_by_category[cat]) for cat in categories]
category_df = pd.DataFrame({
    'Category': categories,
    'Number of Tables': table_counts
})

# Plot
plt.figure(figsize=(10, 6))
bars = plt.bar(category_df['Category'], category_df['Number of Tables'], color='skyblue')
plt.xlabel('OMOP CDM Table Categories')
plt.ylabel('Number of Tables')
plt.title('OMOP CDM Structure Overview')
plt.xticks(rotation=45, ha='right')
plt.tight_layout()

# Add table count labels on top of bars
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
             f'{height}', ha='center', va='bottom')

# Instead of displaying the plot, we'll just describe it for the tutorial
print("OMOP CDM Table Categories:")
for category, tables in tables_by_category.items():
    print(f"\n{category} ({len(tables)} tables):")
    for table in tables:
        print(f"  - {table}")

# Example structure of the CONCEPT table
concept_columns = [
    'concept_id', 'concept_name', 'domain_id', 'vocabulary_id', 
    'concept_class_id', 'standard_concept', 'concept_code',
    'valid_start_date', 'valid_end_date', 'invalid_reason'
]

# Example concepts from different domains
example_concepts = [
    [192671, 'Hypertension', 'Condition', 'SNOMED', 'Clinical Finding', 'S', '38341003', '1970-01-01', '2099-12-31', None],
    [1545999, 'Atorvastatin 10 MG Oral Tablet', 'Drug', 'RxNorm', 'Clinical Drug', 'S', '617311', '1970-01-01', '2099-12-31', None],
    [2212392, 'Hemoglobin A1c measurement', 'Measurement', 'LOINC', 'Lab Test', 'S', '4548-4', '1970-01-01', '2099-12-31', None],
    [4230167, 'Coronary artery bypass graft', 'Procedure', 'SNOMED', 'Procedure', 'S', '232717009', '1970-01-01', '2099-12-31', None]
]

# Create a DataFrame for display
concept_df = pd.DataFrame(example_concepts, columns=concept_columns)
print("Example CONCEPT table entries:")

# Display the relevant columns
print(concept_df[['concept_id', 'concept_name', 'domain_id', 'vocabulary_id', 'concept_class_id']])

# Create a simple SQLite database with OMOP structure
conn = sqlite3.connect(':memory:')  # In-memory database for demonstration

# Create simplified PERSON table
conn.execute('''
CREATE TABLE person (
    person_id INTEGER PRIMARY KEY,
    gender_concept_id INTEGER,
    birth_datetime TEXT,
    race_concept_id INTEGER,
    ethnicity_concept_id INTEGER
)
''')

# Create simplified CONDITION_OCCURRENCE table
conn.execute('''
CREATE TABLE condition_occurrence (
    condition_occurrence_id INTEGER PRIMARY KEY,
    person_id INTEGER,
    condition_concept_id INTEGER,
    condition_start_date TEXT,
    condition_end_date TEXT,
    FOREIGN KEY (person_id) REFERENCES person (person_id)
)
''')

# Insert sample data
conn.executemany('''
INSERT INTO person (person_id, gender_concept_id, birth_datetime, race_concept_id, ethnicity_concept_id)
VALUES (?, ?, ?, ?, ?)
''', [
    (1, 8507, '1960-05-12', 8516, 38003564),  # Male, White, Not Hispanic
    (2, 8532, '1975-10-23', 8516, 38003564),  # Female, White, Not Hispanic
    (3, 8532, '1988-03-01', 8527, 38003563),  # Female, Black, Hispanic
    (4, 8507, '1945-12-10', 8515, 38003564),  # Male, Asian, Not Hispanic
])

conn.executemany('''
INSERT INTO condition_occurrence (condition_occurrence_id, person_id, condition_concept_id, condition_start_date, condition_end_date)
VALUES (?, ?, ?, ?, ?)
''', [
    (1, 1, 192671, '2020-01-15', None),  # Hypertension
    (2, 1, 201820, '2020-03-20', None),  # Type 2 Diabetes
    (3, 2, 192671, '2019-05-10', None),  # Hypertension
    (4, 3, 255573, '2021-02-01', None),  # Asthma
    (5, 4, 192671, '2018-11-22', None),  # Hypertension
    (6, 4, 381591, '2019-08-15', None),  # Atrial Fibrillation
])

# Query all persons
persons_df = pd.read_sql_query("SELECT * FROM person", conn)
print("Persons in the database:")
print(persons_df)

# Query all conditions
conditions_df = pd.read_sql_query("SELECT * FROM condition_occurrence", conn)
print("\nConditions in the database:")
print(conditions_df)

# Count conditions by person
condition_counts = pd.read_sql_query('''
SELECT p.person_id, 
       COUNT(co.condition_occurrence_id) as condition_count
FROM person p
LEFT JOIN condition_occurrence co ON p.person_id = co.person_id
GROUP BY p.person_id
''', conn)

print("Number of conditions per person:")
print(condition_counts)

# Count occurrences of each condition
condition_distribution = pd.read_sql_query('''
SELECT condition_concept_id, 
       COUNT(*) as occurrence_count
FROM condition_occurrence
GROUP BY condition_concept_id
ORDER BY occurrence_count DESC
''', conn)

print("\nDistribution of conditions:")
print(condition_distribution)

# In a real database, we would do something like this:
# (This is just for illustration - our simple database doesn't have a CONCEPT table)
print("\nIn a real OMOP database, we would join with the CONCEPT table:")
print('''
SELECT c.concept_name, COUNT(*) as occurrence_count
FROM condition_occurrence co
JOIN concept c ON co.condition_concept_id = c.concept_id
GROUP BY co.condition_concept_id, c.concept_name
ORDER BY occurrence_count DESC
''')

# For our example, we'll manually map the concept IDs to names
concept_map = {
    192671: 'Hypertension',
    201820: 'Type 2 Diabetes',
    255573: 'Asthma',
    381591: 'Atrial Fibrillation'
}

condition_distribution['condition_name'] = condition_distribution['condition_concept_id'].map(concept_map)
print("\nDistribution of conditions with names:")
print(condition_distribution[['condition_name', 'occurrence_count']])