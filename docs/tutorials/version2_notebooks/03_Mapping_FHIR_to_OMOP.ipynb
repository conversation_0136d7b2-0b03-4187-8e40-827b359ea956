{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Mapping FHIR to OMOP CDM", "", "## Overview", "", "This tutorial explores the process of mapping data from HL7 FHIR (Fast Healthcare Interoperability Resources) to the OMOP CDM (Observational Medical Outcomes Partnership Common Data Model). By the end of this tutorial, you'll understand:", "", "- The conceptual differences between FHIR and OMOP CDM", "- Key mapping challenges and strategies", "- How to map common FHIR resources to OMOP tables", "- Practical approaches for implementing FHIR-to-OMOP transformations", "- Tools and libraries that can assist with the mapping process", "", "## Prerequisites", "", "- Understanding of FHIR resources and structure (see Tutorial 1)", "- Understanding of OMOP CDM tables and concepts (see Tutorial 2)", "- Python programming knowledge", "- Basic knowledge of healthcare data", "", "Let's begin by importing the libraries we'll need for this tutorial:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries", "import requests", "import json", "import pandas as pd", "import numpy as np", "import matplotlib.pyplot as plt", "import seaborn as sns", "from datetime import datetime", "import os", "", "# Set up visualization style", "plt.style.use('ggplot')", "sns.set(style=\"whitegrid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Comparing FHIR and OMOP CDM", "", "Before diving into mapping strategies, it's important to understand the fundamental differences between FHIR and OMOP CDM:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a comparison table of FHIR vs OMOP CDM", "comparison_data = {", "    'Aspect': [", "        'Primary Purpose', ", "        'Data Model Type', ", "        'Scope', ", "        'Terminology Approach', ", "        'Update Frequency',", "        'Temporal Data',", "        'Identifiers',", "        'Extensions'", "    ],", "    'FHIR': [", "        'Data exchange and interoperability', ", "        'Resource-based model with RESTful API', ", "        'Clinical care and exchange', ", "        'Multiple terminologies, flexible', ", "        'Frequent updates with backward compatibility',", "        'Point-in-time snapshots with history',", "        'Multiple identifiers per resource',", "        'Extensible through defined extension mechanism'", "    ],", "    'OMOP CDM': [", "        'Research and analytics', ", "        'Relational database model', ", "        'Observational research', ", "        'Standardized vocabularies, strict', ", "        'Less frequent updates, version-controlled',", "        'Start and end dates for clinical events',", "        'Single identifier per entity',", "        'Limited extensibility through custom tables'", "    ]", "}", "", "comparison_df = pd.DataFrame(comparison_data)", "print(\"Comparison of FHIR and OMOP CDM:\")", "for i, row in comparison_df.iterrows():", "    print(f\"\\n{row['Aspect']}:\")", "    print(f\"  FHIR: {row['FHIR']}\")", "    print(f\"  OMOP: {row['OMOP CDM']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Key Differences", "", "1. **Purpose and Design Philosophy**", "   - FHIR is designed for interoperability and data exchange in clinical settings", "   - OMOP CDM is designed for research and analytics across multiple data sources", "", "2. **Data Structure**", "   - FHIR uses a resource-based model with nested structures", "   - OMOP uses a relational model with normalized tables", "", "3. **Terminology**", "   - FHIR allows multiple coding systems within resources", "   - OMOP standardizes all concepts to specific vocabularies", "", "4. **Granularity**", "   - FHIR can capture detailed clinical context", "   - OMOP simplifies data for analytical purposes", "", "These differences create both challenges and opportunities when mapping between the two standards."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. General Mapping Principles", "", "When mapping from FHIR to OMOP CDM, several key principles should guide the process:", "", "### Preserve Clinical Meaning", "", "The primary goal is to preserve the clinical meaning of the data. This requires understanding both:", "- The intent of the original FHIR data", "- The appropriate representation in OMOP", "", "### Standardize Concepts", "", "FHIR resources may use various coding systems. When mapping to OMOP:", "- Identify the source vocabulary (e.g., ICD-10, LOINC)", "- Map to the appropriate standard concept in OMOP", "- Use OMOP concept relationships when direct mappings don't exist", "", "### Handle Structural Differences", "", "FHIR's nested resources must be flattened to fit OMOP's relational structure:", "- One FHIR resource may map to multiple OMOP tables", "- Nested FHIR elements may need to be extracted to separate OMOP records", "- References between FHIR resources must be maintained through OMOP foreign keys", "", "### Manage Data Loss", "", "Some FHIR data may not have a direct equivalent in OMOP:", "- Prioritize clinically significant data", "- Consider custom extensions for OMOP when necessary", "- Document mapping decisions and potential data loss"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Mapping Common FHIR Resources to OMOP Tables", "", "Let's explore how to map some of the most common FHIR resources to OMOP tables:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a mapping table for common FHIR resources to OMOP tables", "mapping_data = {", "    'FHIR Resource': [", "        'Patient', ", "        'Condition', ", "        'Observation (lab)', ", "        'Observation (vital signs)',", "        'Observation (social history)',", "        'MedicationRequest',", "        'MedicationAdministration',", "        'Procedure',", "        'Encounter',", "        'AllergyIntolerance'", "    ],", "    'Primary OMOP Table': [", "        'PERSON', ", "        'CONDITION_OCCURRENCE', ", "        'MEASUREMENT', ", "        'MEASUREMENT',", "        'OBSERVATION',", "        'DRUG_EXPOSURE',", "        'DRUG_EXPOSURE',", "        'PROCEDURE_OCCURRENCE',", "        'VISIT_OCCURRENCE',", "        'CONDITION_OCCURRENCE'", "    ],", "    'Additional OMOP Tables': [", "        'LOCATION, DEATH', ", "        'CONDITION_ERA', ", "        '', ", "        '',", "        '',", "        'DRUG_ERA',", "        '',", "        '',", "        'VISIT_DETAIL',", "        ''", "    ]", "}", "", "mapping_df = pd.DataFrame(mapping_data)", "print(\"Mapping of FHIR Resources to OMOP Tables:\")", "print(mapping_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.1 Patient to PERSON", "", "The FHIR Patient resource maps primarily to the OMOP PERSON table:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example FHIR Patient resource", "fhir_patient = {", "  \"resourceType\": \"Patient\",", "  \"id\": \"example\",", "  \"active\": True,", "  \"name\": [", "    {", "      \"use\": \"official\",", "      \"family\": \"<PERSON>\",", "      \"given\": [\"<PERSON>\", \"<PERSON>\"]", "    }", "  ],", "  \"gender\": \"male\",", "  \"birthDate\": \"1974-12-25\",", "  \"address\": [", "    {", "      \"use\": \"home\",", "      \"line\": [\"123 Main St\"],", "      \"city\": \"Anytown\",", "      \"state\": \"CA\",", "      \"postalCode\": \"12345\",", "      \"country\": \"USA\"", "    }", "  ]", "}", "", "# Corresponding OMOP PERSON record", "omop_person = {", "    \"person_id\": 1,  # Generated or mapped from FHIR id", "    \"gender_concept_id\": 8507,  # Standard concept for 'Male'", "    \"year_of_birth\": 1974,", "    \"month_of_birth\": 12,", "    \"day_of_birth\": 25,", "    \"birth_datetime\": \"1974-12-25T00:00:00\",", "    \"race_concept_id\": 0,  # Not provided in the FHIR resource", "    \"ethnicity_concept_id\": 0,  # Not provided in the FHIR resource", "    \"location_id\": None,  # Would be populated if location is mapped", "    \"provider_id\": None,", "    \"care_site_id\": None,", "    \"person_source_value\": \"example\",  # FHIR id", "    \"gender_source_value\": \"male\",", "    \"gender_source_concept_id\": 0,", "    \"race_source_value\": None,", "    \"race_source_concept_id\": 0,", "    \"ethnicity_source_value\": None,", "    \"ethnicity_source_concept_id\": 0", "}", "", "print(\"FHIR Patient to OMOP PERSON Mapping Example:\")", "print(\"\\nFHIR Patient:\")", "print(json.dumps(fhir_patient, indent=2))", "print(\"\\nOMOP PERSON:\")", "print(json.dumps(omop_person, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.2 Condition to CONDITION_OCCURRENCE", "", "The FHIR Condition resource maps to the OMOP CONDITION_OCCURRENCE table:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example FHIR Condition resource", "fhir_condition = {", "  \"resourceType\": \"Condition\",", "  \"id\": \"example\",", "  \"clinicalStatus\": {", "    \"coding\": [", "      {", "        \"system\": \"http://terminology.hl7.org/CodeSystem/condition-clinical\",", "        \"code\": \"active\",", "        \"display\": \"Active\"", "      }", "    ]", "  },", "  \"verificationStatus\": {", "    \"coding\": [", "      {", "        \"system\": \"http://terminology.hl7.org/CodeSystem/condition-ver-status\",", "        \"code\": \"confirmed\",", "        \"display\": \"Confirmed\"", "      }", "    ]", "  },", "  \"code\": {", "    \"coding\": [", "      {", "        \"system\": \"http://snomed.info/sct\",", "        \"code\": \"38341003\",", "        \"display\": \"Hypertension\"", "      }", "    ],", "    \"text\": \"Hypertension\"", "  },", "  \"subject\": {", "    \"reference\": \"Patient/example\"", "  },", "  \"onsetDateTime\": \"2019-03-15\",", "  \"recordedDate\": \"2019-03-20\"", "}", "", "# Corresponding OMOP CONDITION_OCCURRENCE record", "omop_condition = {", "    \"condition_occurrence_id\": 1,  # Generated", "    \"person_id\": 1,  # Mapped from Patient/example", "    \"condition_concept_id\": 320128,  # Standard concept for 'Hypertension'", "    \"condition_start_date\": \"2019-03-15\",", "    \"condition_start_datetime\": \"2019-03-15T00:00:00\",", "    \"condition_end_date\": None,  # Not provided in the FHIR resource", "    \"condition_end_datetime\": None,", "    \"condition_type_concept_id\": 32020,  # EHR encounter diagnosis", "    \"condition_status_concept_id\": 4230359,  # Confirmed diagnosis", "    \"stop_reason\": None,", "    \"provider_id\": None,", "    \"visit_occurrence_id\": None,", "    \"visit_detail_id\": None,", "    \"condition_source_value\": \"38341003\",", "    \"condition_source_concept_id\": 45905770,  # Source concept for SNOMED 38341003", "    \"condition_status_source_value\": \"confirmed\"", "}", "", "print(\"FHIR Condition to OMOP CONDITION_OCCURRENCE Mapping Example:\")", "print(\"\\nFHIR Condition:\")", "print(json.dumps(fhir_condition, indent=2))", "print(\"\\nOMOP CONDITION_OCCURRENCE:\")", "print(json.dumps(omop_condition, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 3.3 Observation to MEASUREMENT or OBSERVATION", "", "FHIR Observation resources can map to either MEASUREMENT or OBSERVATION in OMOP, depending on the type:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example FHIR Observation resource (lab test)", "fhir_observation_lab = {", "  \"resourceType\": \"Observation\",", "  \"id\": \"example-lab\",", "  \"status\": \"final\",", "  \"category\": [", "    {", "      \"coding\": [", "        {", "          \"system\": \"http://terminology.hl7.org/CodeSystem/observation-category\",", "          \"code\": \"laboratory\",", "          \"display\": \"Laboratory\"", "        }", "      ]", "    }", "  ],", "  \"code\": {", "    \"coding\": [", "      {", "        \"system\": \"http://loinc.org\",", "        \"code\": \"4548-4\",", "        \"display\": \"Hemoglobin A1c\"", "      }", "    ],", "    \"text\": \"Hemoglobin A1c\"", "  },", "  \"subject\": {", "    \"reference\": \"Patient/example\"", "  },", "  \"effectiveDateTime\": \"2020-04-15T08:30:00Z\",", "  \"valueQuantity\": {", "    \"value\": 7.2,", "    \"unit\": \"%\",", "    \"system\": \"http://unitsofmeasure.org\",", "    \"code\": \"%\"", "  }", "}", "", "# Corresponding OMOP MEASUREMENT record", "omop_measurement = {", "    \"measurement_id\": 1,  # Generated", "    \"person_id\": 1,  # Mapped from Patient/example", "    \"measurement_concept_id\": 3004410,  # Standard concept for 'Hemoglobin A1c'", "    \"measurement_date\": \"2020-04-15\",", "    \"measurement_datetime\": \"2020-04-15T08:30:00Z\",", "    \"measurement_type_concept_id\": 5001,  # Lab result", "    \"operator_concept_id\": 0,  # Not applicable", "    \"value_as_number\": 7.2,", "    \"value_as_concept_id\": 0,  # Not applicable", "    \"unit_concept_id\": 8554,  # Standard concept for '%'", "    \"range_low\": None,  # Not provided in the FHIR resource", "    \"range_high\": None,  # Not provided in the FHIR resource", "    \"provider_id\": None,", "    \"visit_occurrence_id\": None,", "    \"visit_detail_id\": None,", "    \"measurement_source_value\": \"4548-4\",", "    \"measurement_source_concept_id\": 3004410,  # Source concept for LOINC 4548-4", "    \"unit_source_value\": \"%\",", "    \"value_source_value\": \"7.2\"", "}", "", "print(\"FHIR Observation (Lab) to OMOP MEASUREMENT Mapping Example:\")", "print(\"\\nFHIR Observation (Lab):\")", "print(json.dumps(fhir_observation_lab, indent=2))", "print(\"\\nOMOP MEASUREMENT:\")", "print(json.dumps(omop_measurement, indent=2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Mapping Challenges and Solutions", "", "Several challenges arise when mapping from FHIR to OMOP:", "", "### 4.1 Terminology Mapping", "", "FHIR resources often use multiple coding systems, while OMOP requires standardized concepts.", "", "**Solution:** Use the OMOP vocabulary tables to map source codes to standard concepts:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example of terminology mapping process", "def map_code_to_standard_concept(code_system, code):", "    \"\"\"", "    Map a source code to an OMOP standard concept", "    ", "    This is a simplified example. In practice, you would query the OMOP", "    CONCEPT and CONCEPT_RELATIONSHIP tables.", "    \"\"\"", "    # Mapping dictionary (simplified example)", "    mapping = {", "        (\"http://snomed.info/sct\", \"38341003\"): 320128,  # Hypertension", "        (\"http://loinc.org\", \"4548-4\"): 3004410,  # Hemoglobin A1c", "        (\"http://www.nlm.nih.gov/research/umls/rxnorm\", \"1545999\"): 1545999  # Atorvastatin 10 MG", "    }", "    ", "    return mapping.get((code_system, code), 0)", "", "# Example usage", "snomed_code = \"38341003\"", "snomed_system = \"http://snomed.info/sct\"", "omop_concept_id = map_code_to_standard_concept(snomed_system, snomed_code)", "", "print(f\"Mapping from {snomed_system} code {snomed_code} to OMOP concept ID: {omop_concept_id}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.2 Handling FHIR Extensions", "", "FHIR resources can include extensions for additional data not covered by the base resource.", "", "**Solution:** Analyze extensions and map to appropriate OMOP tables or consider custom tables for important extensions:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example FHIR resource with extensions", "fhir_patient_with_extension = {", "  \"resourceType\": \"Patient\",", "  \"id\": \"patient-with-extensions\",", "  \"extension\": [", "    {", "      \"url\": \"http://hl7.org/fhir/StructureDefinition/patient-birthPlace\",", "      \"valueAddress\": {", "        \"city\": \"Seattle\",", "        \"state\": \"WA\",", "        \"country\": \"USA\"", "      }", "    },", "    {", "      \"url\": \"http://example.org/fhir/StructureDefinition/ethnicity\",", "      \"valueCodeableConcept\": {", "        \"coding\": [", "          {", "            \"system\": \"urn:oid:2.16.840.1.113883.6.238\",", "            \"code\": \"2135-2\",", "            \"display\": \"Hispanic or Latino\"", "          }", "        ]", "      }", "    }", "  ],", "  \"name\": [", "    {", "      \"family\": \"<PERSON>\",", "      \"given\": [\"<PERSON>\"]", "    }", "  ],", "  \"gender\": \"female\",", "  \"birthDate\": \"1982-05-18\"", "}", "", "# Handling extensions in mapping", "def extract_extensions(fhir_resource):", "    \"\"\"Extract extensions from a FHIR resource\"\"\"", "    extensions = {}", "    ", "    if \"extension\" in fhir_resource:", "        for ext in fhir_resource[\"extension\"]:", "            url = ext[\"url\"]", "            # Extract the value based on the extension type", "            for key in ext:", "                if key.startswith(\"value\"):", "                    extensions[url] = ext[key]", "                    break", "    ", "    return extensions", "", "# Extract extensions from the patient resource", "patient_extensions = extract_extensions(fhir_patient_with_extension)", "print(\"Extracted extensions:\")", "for url, value in patient_extensions.items():", "    print(f\"\\nExtension URL: {url}\")", "    print(f\"Value: {json.dumps(value, indent=2)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 4.3 Handling FHIR References", "", "FHIR resources often reference other resources, which must be maintained in OMOP.", "", "**Solution:** Maintain a mapping of FHIR resource IDs to OMOP entity IDs:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example of handling FHIR references", "class FhirToOmopMapper:", "    def __init__(self):", "        # Dictionary to store mappings from FHIR IDs to OMOP IDs", "        self.id_mappings = {", "            \"Patient\": {},  # Maps Patient/id to person_id", "            \"Encounter\": {},  # Maps Encounter/id to visit_occurrence_id", "            \"Practitioner\": {}  # Maps Practitioner/id to provider_id", "        }", "    ", "    def register_mapping(self, resource_type, fhir_id, omop_id):", "        \"\"\"Register a mapping from FHIR ID to OMOP ID\"\"\"", "        if resource_type in self.id_mappings:", "            self.id_mappings[resource_type][fhir_id] = omop_id", "    ", "    def get_omop_id(self, fhir_reference):", "        \"\"\"Get OMOP ID from a FHIR reference\"\"\"", "        if not fhir_reference or \"/\" not in fhir_reference:", "            return None", "        ", "        parts = fhir_reference.split(\"/\")", "        resource_type = parts[0]", "        fhir_id = parts[1]", "        ", "        if resource_type in self.id_mappings and fhir_id in self.id_mappings[resource_type]:", "            return self.id_mappin", "(Content truncated due to size limit. Use line ranges to read in chunks)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}