# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import sqlite3
from sqlalchemy import create_engine, text
import warnings
import plotly.express as px
import plotly.graph_objects as go
from IPython.display import display, HTML

# Set up visualization style
plt.style.use('ggplot')
sns.set(style="whitegrid")
warnings.filterwarnings('ignore')

# Create a directory for our data if it doesn't exist
os.makedirs('data', exist_ok=True)

# Define the structure of key OMOP CDM tables
omop_tables = {
    "PERSON": [
        "person_id", "gender_concept_id", "year_of_birth", "month_of_birth", "day_of_birth", 
        "birth_datetime", "race_concept_id", "ethnicity_concept_id", "location_id", 
        "provider_id", "care_site_id", "person_source_value", "gender_source_value", 
        "gender_source_concept_id", "race_source_value", "race_source_concept_id", 
        "ethnicity_source_value", "ethnicity_source_concept_id"
    ],
    "VISIT_OCCURRENCE": [
        "visit_occurrence_id", "person_id", "visit_concept_id", "visit_start_date", 
        "visit_start_datetime", "visit_end_date", "visit_end_datetime", "visit_type_concept_id", 
        "provider_id", "care_site_id", "visit_source_value", "visit_source_concept_id", 
        "admitted_from_concept_id", "admitted_from_source_value", "discharge_to_concept_id", 
        "discharge_to_source_value", "preceding_visit_occurrence_id"
    ],
    "CONDITION_OCCURRENCE": [
        "condition_occurrence_id", "person_id", "condition_concept_id", "condition_start_date", 
        "condition_start_datetime", "condition_end_date", "condition_end_datetime", 
        "condition_type_concept_id", "condition_status_concept_id", "stop_reason", 
        "provider_id", "visit_occurrence_id", "visit_detail_id", "condition_source_value", 
        "condition_source_concept_id", "condition_status_source_value"
    ],
    "DRUG_EXPOSURE": [
        "drug_exposure_id", "person_id", "drug_concept_id", "drug_exposure_start_date", 
        "drug_exposure_start_datetime", "drug_exposure_end_date", "drug_exposure_end_datetime", 
        "verbatim_end_date", "drug_type_concept_id", "stop_reason", "refills", "quantity", 
        "days_supply", "sig", "route_concept_id", "lot_number", "provider_id", 
        "visit_occurrence_id", "visit_detail_id", "drug_source_value", "drug_source_concept_id", 
        "route_source_value", "dose_unit_source_value"
    ],
    "MEASUREMENT": [
        "measurement_id", "person_id", "measurement_concept_id", "measurement_date", 
        "measurement_datetime", "measurement_time", "measurement_type_concept_id", 
        "operator_concept_id", "value_as_number", "value_as_concept_id", "unit_concept_id", 
        "range_low", "range_high", "provider_id", "visit_occurrence_id", "visit_detail_id", 
        "measurement_source_value", "measurement_source_concept_id", "unit_source_value", 
        "value_source_value"
    ],
    "CONCEPT": [
        "concept_id", "concept_name", "domain_id", "vocabulary_id", "concept_class_id", 
        "standard_concept", "concept_code", "valid_start_date", "valid_end_date", 
        "invalid_reason"
    ]
}

# Display the structure of the PERSON table
print("PERSON Table Structure:")
for i, column in enumerate(omop_tables["PERSON"]):
    print(f"{i+1}. {column}")

# Display the structure of the CONDITION_OCCURRENCE table
print("\nCONDITION_OCCURRENCE Table Structure:")
for i, column in enumerate(omop_tables["CONDITION_OCCURRENCE"]):
    print(f"{i+1}. {column}")

# Create example CONCEPT data
concept_data = [
    {
        "concept_id": 8507,
        "concept_name": "Male",
        "domain_id": "Gender",
        "vocabulary_id": "Gender",
        "concept_class_id": "Gender",
        "standard_concept": "S",
        "concept_code": "M",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 8532,
        "concept_name": "Female",
        "domain_id": "Gender",
        "vocabulary_id": "Gender",
        "concept_class_id": "Gender",
        "standard_concept": "S",
        "concept_code": "F",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 320128,
        "concept_name": "Essential hypertension",
        "domain_id": "Condition",
        "vocabulary_id": "SNOMED",
        "concept_class_id": "Clinical Finding",
        "standard_concept": "S",
        "concept_code": "59621000",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 44821244,
        "concept_name": "Hypertension",
        "domain_id": "Condition",
        "vocabulary_id": "ICD10CM",
        "concept_class_id": "3-char billing code",
        "standard_concept": None,
        "concept_code": "I10",
        "valid_start_date": "2015-10-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 1112807,
        "concept_name": "Ambulatory blood pressure monitoring",
        "domain_id": "Procedure",
        "vocabulary_id": "SNOMED",
        "concept_class_id": "Procedure",
        "standard_concept": "S",
        "concept_code": "698452002",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 3004249,
        "concept_name": "Diastolic blood pressure",
        "domain_id": "Measurement",
        "vocabulary_id": "LOINC",
        "concept_class_id": "Clinical Observation",
        "standard_concept": "S",
        "concept_code": "8462-4",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 3012888,
        "concept_name": "Systolic blood pressure",
        "domain_id": "Measurement",
        "vocabulary_id": "LOINC",
        "concept_class_id": "Clinical Observation",
        "standard_concept": "S",
        "concept_code": "8480-6",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 1545999,
        "concept_name": "Lisinopril 10 MG Oral Tablet",
        "domain_id": "Drug",
        "vocabulary_id": "RxNorm",
        "concept_class_id": "Clinical Drug",
        "standard_concept": "S",
        "concept_code": "314076",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 1308216,
        "concept_name": "Lisinopril",
        "domain_id": "Drug",
        "vocabulary_id": "RxNorm",
        "concept_class_id": "Ingredient",
        "standard_concept": "S",
        "concept_code": "29046",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id": 9201,
        "concept_name": "Inpatient Visit",
        "domain_id": "Visit",
        "vocabulary_id": "Visit",
        "concept_class_id": "Visit",
        "standard_concept": "S",
        "concept_code": "IP",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    }
]

# Create a DataFrame from the concept data
concept_df = pd.DataFrame(concept_data)

# Display the concept data
concept_df

# Create example CONCEPT_RELATIONSHIP data
concept_relationship_data = [
    {
        "concept_id_1": 44821244,  # ICD-10 Hypertension
        "concept_id_2": 320128,    # SNOMED Essential hypertension
        "relationship_id": "Maps to",
        "valid_start_date": "2015-10-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    },
    {
        "concept_id_1": 1545999,   # Lisinopril 10 MG Oral Tablet
        "concept_id_2": 1308216,   # Lisinopril
        "relationship_id": "Has ingredient",
        "valid_start_date": "1970-01-01",
        "valid_end_date": "2099-12-31",
        "invalid_reason": None
    }
]

# Create a DataFrame from the concept relationship data
concept_relationship_df = pd.DataFrame(concept_relationship_data)

# Display the concept relationship data
print("\nExample CONCEPT_RELATIONSHIP Data:")
display(concept_relationship_df)

# Create a SQLite database with example OMOP tables
def create_example_omop_database():
    """Create a SQLite database with example OMOP tables."""
    # Create a connection to a new SQLite database
    conn = sqlite3.connect('example_omop.db')
    cursor = conn.cursor()
    
    # Create PERSON table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS person (
        person_id INTEGER PRIMARY KEY,
        gender_concept_id INTEGER,
        year_of_birth INTEGER,
        month_of_birth INTEGER,
        day_of_birth INTEGER,
        birth_datetime TEXT,
        race_concept_id INTEGER,
        ethnicity_concept_id INTEGER,
        location_id INTEGER,
        provider_id INTEGER,
        care_site_id INTEGER,
        person_source_value TEXT,
        gender_source_value TEXT,
        gender_source_concept_id INTEGER,
        race_source_value TEXT,
        race_source_concept_id INTEGER,
        ethnicity_source_value TEXT,
        ethnicity_source_concept_id INTEGER
    )
    ''')
    
    # Create VISIT_OCCURRENCE table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS visit_occurrence (
        visit_occurrence_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        visit_concept_id INTEGER,
        visit_start_date TEXT,
        visit_start_datetime TEXT,
        visit_end_date TEXT,
        visit_end_datetime TEXT,
        visit_type_concept_id INTEGER,
        provider_id INTEGER,
        care_site_id INTEGER,
        visit_source_value TEXT,
        visit_source_concept_id INTEGER,
        admitted_from_concept_id INTEGER,
        admitted_from_source_value TEXT,
        discharge_to_concept_id INTEGER,
        discharge_to_source_value TEXT,
        preceding_visit_occurrence_id INTEGER,
        FOREIGN KEY (person_id) REFERENCES person(person_id)
    )
    ''')
    
    # Create CONDITION_OCCURRENCE table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS condition_occurrence (
        condition_occurrence_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        condition_concept_id INTEGER,
        condition_start_date TEXT,
        condition_start_datetime TEXT,
        condition_end_date TEXT,
        condition_end_datetime TEXT,
        condition_type_concept_id INTEGER,
        condition_status_concept_id INTEGER,
        stop_reason TEXT,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        condition_source_value TEXT,
        condition_source_concept_id INTEGER,
        condition_status_source_value TEXT,
        FOREIGN KEY (person_id) REFERENCES person(person_id),
        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)
    )
    ''')
    
    # Create DRUG_EXPOSURE table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS drug_exposure (
        drug_exposure_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        drug_concept_id INTEGER,
        drug_exposure_start_date TEXT,
        drug_exposure_start_datetime TEXT,
        drug_exposure_end_date TEXT,
        drug_exposure_end_datetime TEXT,
        verbatim_end_date TEXT,
        drug_type_concept_id INTEGER,
        stop_reason TEXT,
        refills INTEGER,
        quantity REAL,
        days_supply INTEGER,
        sig TEXT,
        route_concept_id INTEGER,
        lot_number TEXT,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        drug_source_value TEXT,
        drug_source_concept_id INTEGER,
        route_source_value TEXT,
        dose_unit_source_value TEXT,
        FOREIGN KEY (person_id) REFERENCES person(person_id),
        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)
    )
    ''')
    
    # Create MEASUREMENT table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS measurement (
        measurement_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        measurement_concept_id INTEGER,
        measurement_date TEXT,
        measurement_datetime TEXT,
        measurement_time TEXT,
        measurement_type_concept_id INTEGER,
        operator_concept_id INTEGER,
        value_as_number REAL,
        value_as_concept_id INTEGER,
        unit_concept_id INTEGER,
        range_low REAL,
        range_high REAL,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        measurement_source_value TEXT,
        measurement_source_concept_id INTEGER,
        unit_source_value TEXT,
        value_source_value TEXT,
        FOREIGN KEY (person_id) REFERENCES person(person_id),
        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)
    )
    ''')
    
    # Create CONCEPT table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS concept (
        concept_id INTEGER PRIMARY KEY,
        concept_name TEXT,
        domain_id TEXT,
        vocabulary_id TEXT,
        concept_class_id TEXT,
        standard_concept TEXT,
        concept_code TEXT,
        valid_start_date TEXT,
        valid_end_date TEXT,
        invalid_reason TEXT
    )
    ''')
    
    conn.commit()
    return conn

# Introduction to OMOP CDM Database Setup
print("Creating OMOP CDM tables for our tutorial...")

import sqlite3

def create_omop_tables():
    """Create the basic OMOP CDM tables needed for this tutorial"""
    conn = sqlite3.connect('example_omop.db')
    cursor = conn.cursor()
    
    # Create PERSON table
    print("\n1. Creating PERSON table...")
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS person (
        person_id INTEGER PRIMARY KEY,
        gender_concept_id INTEGER,
        year_of_birth INTEGER,
        month_of_birth INTEGER,
        day_of_birth INTEGER,
        birth_datetime TEXT,
        race_concept_id INTEGER,
        ethnicity_concept_id INTEGER,
        location_id INTEGER,
        provider_id INTEGER,
        care_site_id INTEGER,
        person_source_value TEXT,
        gender_source_value TEXT,
        gender_source_concept_id INTEGER,
        race_source_value TEXT,
        race_source_concept_id INTEGER,
        ethnicity_source_value TEXT,
        ethnicity_source_concept_id INTEGER
    )
    ''')
    
    # Create VISIT_OCCURRENCE table
    print("2. Creating VISIT_OCCURRENCE table...")
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS visit_occurrence (
        visit_occurrence_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        visit_concept_id INTEGER,
        visit_start_date TEXT,
        visit_start_datetime TEXT,
        visit_end_date TEXT,
        visit_end_datetime TEXT,
        visit_type_concept_id INTEGER,
        provider_id INTEGER,
        care_site_id INTEGER,
        visit_source_value TEXT,
        visit_source_concept_id INTEGER,
        admitted_from_concept_id INTEGER,
        admitted_from_source_value TEXT,
        discharge_to_concept_id INTEGER,
        discharge_to_source_value TEXT,
        preceding_visit_occurrence_id INTEGER
    )
    ''')
    
    # Create CONDITION_OCCURRENCE table
    print("3. Creating CONDITION_OCCURRENCE table...")
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS condition_occurrence (
        condition_occurrence_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        condition_concept_id INTEGER,
        condition_start_date TEXT,
        condition_start_datetime TEXT,
        condition_end_date TEXT,
        condition_end_datetime TEXT,
        condition_type_concept_id INTEGER,
        condition_status_concept_id INTEGER,
        stop_reason TEXT,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        condition_source_value TEXT,
        condition_source_concept_id INTEGER,
        condition_status_source_value TEXT
    )
    ''')
    
    # Create DRUG_EXPOSURE table
    print("4. Creating DRUG_EXPOSURE table...")
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS drug_exposure (
        drug_exposure_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        drug_concept_id INTEGER,
        drug_exposure_start_date TEXT,
        drug_exposure_start_datetime TEXT,
        drug_exposure_end_date TEXT,
        drug_exposure_end_datetime TEXT,
        verbatim_end_date TEXT,
        drug_type_concept_id INTEGER,
        stop_reason TEXT,
        refills INTEGER,
        quantity REAL,
        days_supply INTEGER,
        sig TEXT,
        route_concept_id INTEGER,
        lot_number TEXT,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        drug_source_value TEXT,
        drug_source_concept_id INTEGER,
        route_source_value TEXT,
        dose_unit_source_value TEXT
    )
    ''')
    
    # Create MEASUREMENT table
    print("5. Creating MEASUREMENT table...")
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS measurement (
        measurement_id INTEGER PRIMARY KEY,
        person_id INTEGER,
        measurement_concept_id INTEGER,
        measurement_date TEXT,
        measurement_datetime TEXT,
        measurement_time TEXT,
        measurement_type_concept_id INTEGER,
        operator_concept_id INTEGER,
        value_as_number REAL,
        value_as_concept_id INTEGER,
        unit_concept_id INTEGER,
        range_low REAL,
        range_high REAL,
        provider_id INTEGER,
        visit_occurrence_id INTEGER,
        visit_detail_id INTEGER,
        measurement_source_value TEXT,
        measurement_source_concept_id INTEGER,
        unit_source_value TEXT,
        value_source_value TEXT
    )
    ''')
    
    # Create CONCEPT table
    print("6. Creating CONCEPT table...")
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS concept (
        concept_id INTEGER PRIMARY KEY,
        concept_name TEXT,
        domain_id TEXT,
        vocabulary_id TEXT,
        concept_class_id TEXT,
        standard_concept TEXT,
        concept_code TEXT,
        valid_start_date TEXT,
        valid_end_date TEXT,
        invalid_reason TEXT
    )
    ''')
    
    conn.commit()
    conn.close()
    print("\nAll OMOP CDM tables have been created successfully!")

# Execute the function to create all tables
create_omop_tables()

# Connect to the database
print("Connecting to the OMOP database...")
conn = sqlite3.connect('example_omop.db')
cursor = conn.cursor()

print("\nPopulating OMOP tables with example data...")

# 1. Insert example data into PERSON table
print("1. Adding person records...")
person_data = [
    (1, 8507, 1960, 1, 1, '1960-01-01 00:00:00', 8516, 38003564, None, None, None, 'PT1', 'M', None, 'Black', None, 'Not Hispanic', None),
    (2, 8532, 1970, 5, 15, '1970-05-15 00:00:00', 8527, 38003564, None, None, None, 'PT2', 'F', None, 'White', None, 'Not Hispanic', None),
    (3, 8507, 1980, 10, 30, '1980-10-30 00:00:00', 8527, 38003563, None, None, None, 'PT3', 'M', None, 'White', None, 'Hispanic', None),
    (4, 8532, 1990, 12, 25, '1990-12-25 00:00:00', 8516, 38003564, None, None, None, 'PT4', 'F', None, 'Black', None, 'Not Hispanic', None)
]

cursor.executemany('''
INSERT INTO person (
    person_id, gender_concept_id, year_of_birth, month_of_birth, day_of_birth, 
    birth_datetime, race_concept_id, ethnicity_concept_id, location_id, 
    provider_id, care_site_id, person_source_value, gender_source_value, 
    gender_source_concept_id, race_source_value, race_source_concept_id, 
    ethnicity_source_value, ethnicity_source_concept_id
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
''', person_data)

# 2. Insert example data into VISIT_OCCURRENCE table
print("2. Adding visit records...")
visit_data = [
    (1, 1, 9201, '2020-01-15', '2020-01-15 08:00:00', '2020-01-20', '2020-01-20 16:00:00', 44818518, None, None, 'VISIT1', None, None, None, None, None, None),
    (2, 1, 9202, '2020-03-10', '2020-03-10 10:30:00', '2020-03-10', '2020-03-10 11:45:00', 44818519, None, None, 'VISIT2', None, None, None, None, None, None),
    (3, 2, 9201, '2020-02-20', '2020-02-20 14:00:00', '2020-02-25', '2020-02-25 11:30:00', 44818518, None, None, 'VISIT3', None, None, None, None, None, None),
    (4, 3, 9202, '2020-04-05', '2020-04-05 09:15:00', '2020-04-05', '2020-04-05 10:30:00', 44818519, None, None, 'VISIT4', None, None, None, None, None, None),
    (5, 4, 9202, '2020-05-12', '2020-05-12 13:45:00', '2020-05-12', '2020-05-12 15:00:00', 44818519, None, None, 'VISIT5', None, None, None, None, None, None)
]

cursor.executemany('''
INSERT INTO visit_occurrence VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
''', visit_data)

# 3. Insert example data into CONDITION_OCCURRENCE table
print("3. Adding condition records...")
condition_data = [
    (1, 1, 320128, '2020-01-15', '2020-01-15 09:30:00', None, None, 32020, None, None, None, 1, None, 'I10', 44821244, None),
    (2, 2, 320128, '2020-02-20', '2020-02-20 15:45:00', None, None, 32020, None, None, None, 3, None, 'I10', 44821244, None),
    (3, 3, 201826, '2020-04-05', '2020-04-05 09:30:00', None, None, 32020, None, None, None, 4, None, 'E11.9', 45542411, None),
    (4, 4, 317009, '2020-05-12', '2020-05-12 14:00:00', None, None, 32020, None, None, None, 5, None, 'J45.909', 45877007, None)
]

cursor.executemany('''
INSERT INTO condition_occurrence VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
''', condition_data)

# 4. Insert example data into DRUG_EXPOSURE table
print("4. Adding drug exposure records...")
drug_data = [
    (1, 1, 1545999, '2020-01-15', '2020-01-15 10:00:00', '2020-02-15', '2020-02-15 00:00:00', None, 38000177, None, 3, 30, 30, 'Take 1 tablet daily', None, None, None, 1, None, 'Lisinopril 10mg', None, 'Oral', None),
    (2, 2, 1545999, '2020-02-20', '2020-02-20 16:30:00', '2020-03-20', '2020-03-20 00:00:00', None, 38000177, None, 3, 30, 30, 'Take 1 tablet daily', None, None, None, 3, None, 'Lisinopril 10mg', None, 'Oral', None),
    (3, 3, 1503297, '2020-04-05', '2020-04-05 10:00:00', '2020-05-05', '2020-05-05 00:00:00', None, 38000177, None, 3, 30, 30, 'Take 1 tablet daily', None, None, None, 4, None, 'Metformin 500mg', None, 'Oral', None)
]

cursor.executemany('''
INSERT INTO drug_exposure VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
''', drug_data)

# 5. Insert example data into MEASUREMENT table
print("5. Adding measurement records...")
measurement_data = [
    (1, 1, 3004249, '2020-01-15', '2020-01-15 09:00:00', None, 44818701, 4172703, 90, None, 8876, 60, 90, None, 1, None, 'Diastolic BP', None, 'mmHg', '90'),
    (2, 1, 3012888, '2020-01-15', '2020-01-15 09:00:00', None, 44818701, 4172703, 140, None, 8876, 90, 140, None, 1, None, 'Systolic BP', None, 'mmHg', '140'),
    (3, 2, 3004249, '2020-02-20', '2020-02-20 15:00:00', None, 44818701, 4172703, 85, None, 8876, 60, 90, None, 3, None, 'Diastolic BP', None, 'mmHg', '85'),
    (4, 2, 3012888, '2020-02-20', '2020-02-20 15:00:00', None, 44818701, 4172703, 135, None, 8876, 90, 140, None, 3, None, 'Systolic BP', None, 'mmHg', '135'),
    (5, 3, 3004501, '2020-04-05', '2020-04-05 09:45:00', None, 44818701, 4172703, 7.2, None, 8554, 4.0, 5.7, None, 4, None, 'HbA1c', None, '%', '7.2')
]

cursor.executemany('''
INSERT INTO measurement VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
''', measurement_data)

# 6. Insert example data into CONCEPT table
print("6. Adding concept records...")
concept_data = [
    {'concept_id': 320128, 'concept_name': 'Essential hypertension', 'domain_id': 'Condition', 'vocabulary_id': 'SNOMED', 'concept_class_id': 'Clinical Finding', 'standard_concept': 'S', 'concept_code': '59621000', 'valid_start_date': '1970-01-01', 'valid_end_date': '2099-12-31', 'invalid_reason': None},
    {'concept_id': 201826, 'concept_name': 'Type 2 diabetes mellitus', 'domain_id': 'Condition', 'vocabulary_id': 'SNOMED', 'concept_class_id': 'Clinical Finding', 'standard_concept': 'S', 'concept_code': '44054006', 'valid_start_date': '1970-01-01', 'valid_end_date': '2099-12-31', 'invalid_reason': None},
    {'concept_id': 317009, 'concept_name': 'Asthma', 'domain_id': 'Condition', 'vocabulary_id': 'SNOMED', 'concept_class_id': 'Clinical Finding', 'standard_concept': 'S', 'concept_code': '195967001', 'valid_start_date': '1970-01-01', 'valid_end_date': '2099-12-31', 'invalid_reason': None}
]

for concept in concept_data:
    cursor.execute('''
    INSERT INTO concept VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        concept['concept_id'], concept['concept_name'], concept['domain_id'],
        concept['vocabulary_id'], concept['concept_class_id'], concept['standard_concept'],
        concept['concept_code'], concept['valid_start_date'], concept['valid_end_date'],
        concept['invalid_reason']
    ))

# Commit changes and close connection
conn.commit()
print("\nAll example data has been successfully loaded into the OMOP database!")
conn.close()

# Function to execute SQL queries and return results as a DataFrame
def run_sql_query(query, engine):
    """Execute a SQL query and return results as a DataFrame."""
    try:
        return pd.read_sql_query(query, engine)
    except Exception as e:
        print(f"Error executing query: {e}")
        return pd.DataFrame()

# Example 1: Basic query to get all patients
query1 = """
SELECT 
    p.person_id,
    p.gender_concept_id,
    c.concept_name as gender,
    p.year_of_birth,
    p.race_concept_id,
    r.concept_name as race,
    p.ethnicity_concept_id,
    e.concept_name as ethnicity
FROM 
    person p
LEFT JOIN 
    concept c ON p.gender_concept_id = c.concept_id
LEFT JOIN 
    concept r ON p.race_concept_id = r.concept_id
LEFT JOIN 
    concept e ON p.ethnicity_concept_id = e.concept_id
"""

patients_df = run_sql_query(query1, engine)
print("All Patients:")
display(patients_df)

# Example 2: Query to get all conditions with concept names
query2 = """
SELECT 
    co.condition_occurrence_id,
    co.person_id,
    co.condition_concept_id,
    c.concept_name as condition_name,
    co.condition_start_date,
    co.visit_occurrence_id,
    co.condition_source_value
FROM 
    condition_occurrence co
LEFT JOIN 
    concept c ON co.condition_concept_id = c.concept_id
"""

conditions_df = run_sql_query(query2, engine)
print("\nAll Conditions:")
display(conditions_df)

# Example 3: Query to get all measurements with concept names
query3 = """
SELECT 
    m.measurement_id,
    m.person_id,
    m.measurement_concept_id,
    c.concept_name as measurement_name,
    m.measurement_date,
    m.value_as_number,
    u.concept_name as unit,
    m.visit_occurrence_id
FROM 
    measurement m
LEFT JOIN 
    concept c ON m.measurement_concept_id = c.concept_id
LEFT JOIN 
    concept u ON m.unit_concept_id = u.concept_id
"""

measurements_df = run_sql_query(query3, engine)
print("\nAll Measurements:")
display(measurements_df)

# Example 4: Find patients with hypertension and their blood pressure measurements
query4 = """
SELECT 
    p.person_id,
    p.year_of_birth,
    STRFTIME('%Y', 'now') - p.year_of_birth as age,
    g.concept_name as gender,
    co.condition_start_date as hypertension_date,
    m_sys.measurement_date,
    m_sys.value_as_number as systolic_bp,
    m_dia.value_as_number as diastolic_bp
FROM 
    person p
JOIN 
    condition_occurrence co ON p.person_id = co.person_id
JOIN 
    concept c_condition ON co.condition_concept_id = c_condition.concept_id
JOIN 
    concept g ON p.gender_concept_id = g.concept_id
JOIN 
    measurement m_sys ON p.person_id = m_sys.person_id
JOIN 
    measurement m_dia ON m_sys.person_id = m_dia.person_id 
                      AND m_sys.measurement_date = m_dia.measurement_date
WHERE 
    c_condition.concept_name = 'Essential hypertension'
    AND m_sys.measurement_concept_id = 3012888  -- Systolic BP
    AND m_dia.measurement_concept_id = 3004249  -- Diastolic BP
"""

hypertension_bp_df = run_sql_query(query4, engine)
print("Patients with Hypertension and their Blood Pressure Measurements:")
display(hypertension_bp_df)

# Example 5: Find patients on antihypertensive medications
query5 = """
SELECT 
    p.person_id,
    p.year_of_birth,
    STRFTIME('%Y', 'now') - p.year_of_birth as age,
    g.concept_name as gender,
    d.drug_exposure_start_date,
    d.drug_exposure_end_date,
    c_drug.concept_name as drug_name,
    d.days_supply,
    d.quantity
FROM 
    person p
JOIN 
    drug_exposure d ON p.person_id = d.person_id
JOIN 
    concept c_drug ON d.drug_concept_id = c_drug.concept_id
JOIN 
    concept g ON p.gender_concept_id = g.concept_id
WHERE 
    c_drug.concept_name LIKE '%Lisinopril%'
"""

antihypertensive_df = run_sql_query(query5, engine)
print("\nPatients on Antihypertensive Medications:")
display(antihypertensive_df)

# Example 6: Find patients with both hypertension and diabetes
query6 = """
SELECT 
    p.person_id,
    p.year_of_birth,
    STRFTIME('%Y', 'now') - p.year_of_birth as age,
    g.concept_name as gender,
    htn.condition_start_date as hypertension_date,
    dm.condition_start_date as diabetes_date
FROM 
    person p
JOIN 
    condition_occurrence htn ON p.person_id = htn.person_id
JOIN 
    concept c_htn ON htn.condition_concept_id = c_htn.concept_id
JOIN 
    condition_occurrence dm ON p.person_id = dm.person_id
JOIN 
    concept c_dm ON dm.condition_concept_id = c_dm.concept_id
JOIN 
    concept g ON p.gender_concept_id = g.concept_id
WHERE 
    c_htn.concept_name = 'Essential hypertension'
    AND c_dm.concept_name LIKE '%diabetes%'
"""

comorbidity_df = run_sql_query(query6, engine)
print("\nPatients with Both Hypertension and Diabetes:")
display(comorbidity_df)

# Load all tables into DataFrames
person_df = pd.read_sql_table('person', engine)
visit_df = pd.read_sql_table('visit_occurrence', engine)
condition_df = pd.read_sql_table('condition_occurrence', engine)
drug_df = pd.read_sql_table('drug_exposure', engine)
measurement_df = pd.read_sql_table('measurement', engine)
concept_df = pd.read_sql_table('concept', engine)

# Display the first few rows of each table
print("Person Table:")
display(person_df.head())

print("\nVisit Occurrence Table:")
display(visit_df.head())

print("\nCondition Occurrence Table:")
display(condition_df.head())

# Calculate age for each person
current_year = datetime.now().year
person_df['age'] = current_year - person_df['year_of_birth']

# Analyze age distribution
print("Age Statistics:")
print(person_df['age'].describe())

# Create age groups
bins = [0, 30, 40, 50, 60, 70, 100]
labels = ['<30', '30-39', '40-49', '50-59', '60-69', '70+']
person_df['age_group'] = pd.cut(person_df['age'], bins=bins, labels=labels, right=False)

# Count patients by age group and gender
age_gender_counts = person_df.groupby(['age_group', 'gender_concept_id']).size().unstack()

# Replace gender concept IDs with names
gender_map = {8507: 'Male', 8532: 'Female'}
age_gender_counts.columns = [gender_map.get(col, col) for col in age_gender_counts.columns]

# Plot age distribution by gender
plt.figure(figsize=(10, 6))
age_gender_counts.plot(kind='bar', stacked=True)
plt.title('Patient Age Distribution by Gender')
plt.xlabel('Age Group')
plt.ylabel('Number of Patients')
plt.xticks(rotation=0)
plt.legend(title='Gender')
plt.tight_layout()
plt.show()

# Merge condition data with concept names
condition_with_names = pd.merge(
    condition_df,
    concept_df[['concept_id', 'concept_name']],
    left_on='condition_concept_id',
    right_on='concept_id',
    how='left'
)

# Count conditions by concept name
condition_counts = condition_with_names['concept_name'].value_counts()

# Plot condition distribution
plt.figure(figsize=(10, 6))
condition_counts.plot(kind='bar')
plt.title('Distribution of Conditions')
plt.xlabel('Condition')
plt.ylabel('Number of Occurrences')
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.show()

# Analyze conditions by gender
condition_by_gender = pd.merge(
    condition_with_names,
    person_df[['person_id', 'gender_concept_id']],
    on='person_id',
    how='left'
)

# Replace gender concept IDs with names
condition_by_gender['gender'] = condition_by_gender['gender_concept_id'].map(gender_map)

# Count conditions by gender
condition_gender_counts = condition_by_gender.groupby(['concept_name', 'gender']).size().unstack()

# Plot conditions by gender
plt.figure(figsize=(10, 6))
condition_gender_counts.plot(kind='bar', stacked=True)
plt.title('Conditions by Gender')
plt.xlabel('Condition')
plt.ylabel('Number of Occurrences')
plt.xticks(rotation=45, ha='right')
plt.legend(title='Gender')
plt.tight_layout()
plt.show()

# Exercise 1: Find all patients with hypertension and their demographics
exercise1_query = """
SELECT 
    p.person_id,
    p.year_of_birth,
    STRFTIME('%Y', 'now') - p.year_of_birth as age,
    g.concept_name as gender,
    r.concept_name as race,
    e.concept_name as ethnicity,
    co.condition_start_date
FROM 
    person p
JOIN 
    condition_occurrence co ON p.person_id = co.person_id
JOIN 
    concept c ON co.condition_concept_id = c.concept_id
LEFT JOIN 
    concept g ON p.gender_concept_id = g.concept_id
LEFT JOIN 
    concept r ON p.race_concept_id = r.concept_id
LEFT JOIN 
    concept e ON p.ethnicity_concept_id = e.concept_id
WHERE 
    c.concept_name = 'Essential hypertension'
"""

exercise1_df = run_sql_query(exercise1_query, engine)
print("Patients with Hypertension and their Demographics:")
display(exercise1_df)