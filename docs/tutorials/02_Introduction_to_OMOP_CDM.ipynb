{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Overview\n", "\n", "This tutorial introduces the Observational Medical Outcomes Partnership (OMOP) Common Data Model (CDM), a standardized format for healthcare data. By the end of this tutorial, you'll understand:\n", "\n", "- What the OMOP CDM is and why it was developed\n", "- The core tables and structure of the OMOP CDM\n", "- The OMOP vocabulary system and standard concepts\n", "- How to query OMOP data using SQL\n", "- How to work with OMOP data using Python\n", "\n", "## Prerequisites\n", "\n", "- Basic SQL knowledge\n", "- Basic Python programming knowledge\n", "- Familiarity with healthcare concepts\n", "- Understanding of relational databases\n", "\n", "Let's begin by importing the libraries we'll need for this tutorial:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime, timedelta\n", "import os\n", "import sqlite3\n", "from sqlalchemy import create_engine, text\n", "import warnings\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from IPython.display import display, HTML\n", "\n", "# Set up visualization style\n", "plt.style.use('ggplot')\n", "sns.set(style=\"whitegrid\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Create a directory for our data if it doesn't exist\n", "os.makedirs('data', exist_ok=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM)\n", "\n", "## 1. What is the OMOP Common Data Model?\n", "\n", "The OMOP Common Data Model (CDM) is a standardized format for healthcare data that enables consistent analysis across disparate data sources. Developed by the Observational Health Data Sciences and Informatics (OHDSI) community, it addresses the fundamental challenge of heterogeneous healthcare data representation.\n", "\n", "> \"The OMOP Common Data Model allows for the systematic analysis of disparate observational databases.\" - [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/CommonDataModel.html)\n", "\n", "### Key Characteristics\n", "\n", "| Feature | Description |\n", "|---------|-------------|\n", "| **Standardized Structure** | Consistent table schemas and relationships |\n", "| **Standardized Vocabularies** | Unified representation of medical concepts |\n", "| **Person-centric Design** | All clinical events linked to individual patients |\n", "| **Observational Focus** | Optimized for real-world evidence generation |\n", "| **Open Community** | Maintained by global collaborative network |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Version | Release Year | Key Features | \n", "|---------|-------------|--------------|\n", "| v4.0 | 2012 | Basic clinical data structure |\n", "| v5.0 | 2014 | Additional data domains |\n", "| v5.1 | 2016 | Enhanced vocabulary tables | \n", "| v5.3 | 2018 | Improved clinical data support |\n", "| v5.4.2 | 2019 | Specialty data tables (latest stable) |\n", "| v6.0 | In development | Advanced metadata and provenance |\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Why OMOP CDM Was Developed\n", "\n", "Prior to OMOP CDM, healthcare data analysis faced several challenges:\n", "\n", "- **Data heterogeneity**: Different systems used different formats and terminologies\n", "- **Limited interoperability**: Difficult to combine data from multiple sources\n", "- **Reproducibility issues**: Analyses were difficult to replicate across datasets\n", "- **Inefficient research**: Each study required custom data transformations\n", "\n", "OMOP CDM was developed to address these challenges by providing a common format that enables:\n", "\n", "- **Standardized analyses**: Methods can be applied across multiple datasets\n", "- **Network studies**: Research can be conducted across multiple institutions\n", "- **Efficient tool development**: Tools can be built once and used everywhere\n", "- **Reproducible research**: Analyses can be easily shared and reproduced\n", "\n", "### OMOP CDM Versions\n", "\n", "The OMOP CDM has evolved through several versions:\n", "\n", "- **v4.0**: Early version with basic clinical data structure\n", "- **v5.0**: Added support for additional data domains\n", "- **v5.1**: Refined vocabulary tables and added metadata\n", "- **v5.3**: Current widely-used version with enhanced clinical data support\n", "- **v5.4.2**: Latest stable version with additional tables for specialty data\n", "- **v6.0**: In development with further enhancements\n", "\n", "This tutorial focuses on OMOP CDM v5.4.2, which is the current stable version.\n", "\n", "### OMOP CDM in the Healthcare Data Ecosystem\n", "\n", "OMOP CDM plays a crucial role in the healthcare data ecosystem by:\n", "\n", "1. **Bridging clinical and research worlds**: Enables the use of clinical data for research purposes\n", "2. **Supporting regulatory decision-making**: Provides evidence for safety surveillance and regulatory submissions\n", "3. **Enabling precision medicine**: Facilitates the analysis of large datasets to identify patient subgroups\n", "4. **Accelerating research**: Reduces the time needed to transform and analyze healthcare data\n", "\n", "The following diagram illustrates how OMOP CDM fits into the healthcare data ecosystem:\n", "\n", "<div style=\"width:100%; text-align:center; margin-bottom:30px;\">\n", "    <img src=\"https://www.ohdsi.org/wp-content/uploads/2020/06/OMOP-CDM-v6.0-Conceptual-ERD-1-768x554.png\" \n", "         alt=\"OMOP CDM in Healthcare Data Ecosystem\" \n", "         style=\"max-width:700px; margin:auto;\">\n", "    <p style=\"font-style:italic; margin-top:10px;\">Figure: OMOP CDM conceptual entity-relationship diagram showing how it organizes healthcare data</p>\n", "</div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## OMOP CDM Structure and Components\n", "\n", "![OMOP CDM v5.4.2 Diagram](https://ohdsi.github.io/TheBookOfOhdsi/images/CommonDataModel/cdmDiagram.png)  \n", "*Figure: Diagram of the OMOP Common Data Model version 6*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## OMOP CDM Structure and Components\n", "\n", "### OMOP CDM Transformation Process  \n", "![OMOP CDM Transformation Process](https://ohdsi.github.io/TheBookOfOhdsi/images/StandardizedVocabularies/source_to_standard_mapping.png)  \n", "*Figure 1: Transformation from source data to OMOP CDM using standardized vocabularies*\n", "\n", "### OMOP CDM Clinical Tables Structure  \n", "![OMOP CDM Clinical Tables Structure](https://www.ohdsi.org/wp-content/uploads/2015/04/OMOP-CDM-V5_Clinical-table-hierarchy.png)  \n", "*Figure 2: Hierarchical structure of clinical tables in OMOP CDM v5*\n", "\n", "### OMOP CDM Concept Tables Structure  \n", "![OMOP CDM Concept Tables Structure](https://www.ohdsi.org/wp-content/uploads/2015/04/OMOP-CDM-V5_Concept-table-hierarchy.png)  \n", "*Figure 3: Hierarchical structure of concept tables in OMOP CDM v5*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. OMOP CDM Structure\n", "\n", "The OMOP CDM organizes healthcare data into a set of standardized tables. These tables are grouped into several categories:\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### Standardized Clinical Data Tables\n", "\n", "These tables contain the core clinical data about patients:\n", "\n", "- **PERSON**: Demographic information about patients\n", "- **OBSERVATION_PERIOD**: Time periods when patients were observed\n", "- **VISIT_OCCURRENCE**: Healthcare encounters (e.g., office visits, hospitalizations)\n", "- **CONDITION_OCCURRENCE**: Diagnoses and medical conditions\n", "- **DRUG_EXPOSURE**: Medications and drug therapies\n", "- **PROCEDURE_OCCURRENCE**: Medical procedures\n", "- **MEASUREMENT**: Laboratory tests and clinical measurements\n", "- **OBSERVATION**: Clinical observations that don't fit in other domains\n", "- **DEVICE_EXPOSURE**: Medical devices used by patients\n", "- **SPECIMEN**: Biological samples collected from patients\n", "- **DEATH**: Information about patient deaths\n", "\n", "### Standardized Health System Data Tables\n", "\n", "These tables contain information about the healthcare system:\n", "\n", "- **LOCATION**: Geographic locations\n", "- **CARE_SITE**: Healthcare facilities\n", "- **PROVIDER**: Healthcare providers\n", "\n", "### Standardized Vocabulary Tables\n", "\n", "These tables define the standardized concepts used throughout the CDM:\n", "\n", "- **CONCEPT**: All concepts used in the CDM\n", "- **VOCABULARY**: Vocabularies included in the CDM\n", "- **DOMAIN**: Domains of the CDM (e.g., Condition, Drug)\n", "- **CONCEPT_CLASS**: Classes of concepts\n", "- **CONCEPT_RELATIONSHIP**: Relationships between concepts\n", "- **RELATIONSHIP**: Types of relationships\n", "- **CONCEPT_ANCESTOR**: Hierarchical relationships between concepts\n", "- **CONCEPT_SYNONYM**: Alternative names for concepts\n", "- **SOURCE_TO_CONCEPT_MAP**: Mappings from source codes to standard concepts\n", "\n", "### Standardized Derived Tables\n", "\n", "These tables contain derived data for specific analyses:\n", "\n", "- **COHORT**: Sets of patients defined for analyses\n", "- **COHORT_DEFINITION**: Definitions of cohorts\n", "- **DRUG_ERA**: Continuous periods of drug exposure\n", "- **DOSE_ERA**: Continuous periods of consistent drug dosage\n", "- **CONDITION_ERA**: Continuous periods of a condition\n", "\n", "Let's look at the structure of some key tables in more detail:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PERSON Table Structure:\n", "1. person_id\n", "2. gender_concept_id\n", "3. year_of_birth\n", "4. month_of_birth\n", "5. day_of_birth\n", "6. birth_datetime\n", "7. race_concept_id\n", "8. ethnicity_concept_id\n", "9. location_id\n", "10. provider_id\n", "11. care_site_id\n", "12. person_source_value\n", "13. gender_source_value\n", "14. gender_source_concept_id\n", "15. race_source_value\n", "16. race_source_concept_id\n", "17. ethnicity_source_value\n", "18. ethnicity_source_concept_id\n", "\n", "CONDITION_OCCURRENCE Table Structure:\n", "1. condition_occurrence_id\n", "2. person_id\n", "3. condition_concept_id\n", "4. condition_start_date\n", "5. condition_start_datetime\n", "6. condition_end_date\n", "7. condition_end_datetime\n", "8. condition_type_concept_id\n", "9. condition_status_concept_id\n", "10. stop_reason\n", "11. provider_id\n", "12. visit_occurrence_id\n", "13. visit_detail_id\n", "14. condition_source_value\n", "15. condition_source_concept_id\n", "16. condition_status_source_value\n"]}], "source": ["# Define the structure of key OMOP CDM tables\n", "omop_tables = {\n", "    \"PERSON\": [\n", "        \"person_id\", \"gender_concept_id\", \"year_of_birth\", \"month_of_birth\", \"day_of_birth\", \n", "        \"birth_datetime\", \"race_concept_id\", \"ethnicity_concept_id\", \"location_id\", \n", "        \"provider_id\", \"care_site_id\", \"person_source_value\", \"gender_source_value\", \n", "        \"gender_source_concept_id\", \"race_source_value\", \"race_source_concept_id\", \n", "        \"ethnicity_source_value\", \"ethnicity_source_concept_id\"\n", "    ],\n", "    \"VISIT_OCCURRENCE\": [\n", "        \"visit_occurrence_id\", \"person_id\", \"visit_concept_id\", \"visit_start_date\", \n", "        \"visit_start_datetime\", \"visit_end_date\", \"visit_end_datetime\", \"visit_type_concept_id\", \n", "        \"provider_id\", \"care_site_id\", \"visit_source_value\", \"visit_source_concept_id\", \n", "        \"admitted_from_concept_id\", \"admitted_from_source_value\", \"discharge_to_concept_id\", \n", "        \"discharge_to_source_value\", \"preceding_visit_occurrence_id\"\n", "    ],\n", "    \"CONDITION_OCCURRENCE\": [\n", "        \"condition_occurrence_id\", \"person_id\", \"condition_concept_id\", \"condition_start_date\", \n", "        \"condition_start_datetime\", \"condition_end_date\", \"condition_end_datetime\", \n", "        \"condition_type_concept_id\", \"condition_status_concept_id\", \"stop_reason\", \n", "        \"provider_id\", \"visit_occurrence_id\", \"visit_detail_id\", \"condition_source_value\", \n", "        \"condition_source_concept_id\", \"condition_status_source_value\"\n", "    ],\n", "    \"DRUG_EXPOSURE\": [\n", "        \"drug_exposure_id\", \"person_id\", \"drug_concept_id\", \"drug_exposure_start_date\", \n", "        \"drug_exposure_start_datetime\", \"drug_exposure_end_date\", \"drug_exposure_end_datetime\", \n", "        \"verbatim_end_date\", \"drug_type_concept_id\", \"stop_reason\", \"refills\", \"quantity\", \n", "        \"days_supply\", \"sig\", \"route_concept_id\", \"lot_number\", \"provider_id\", \n", "        \"visit_occurrence_id\", \"visit_detail_id\", \"drug_source_value\", \"drug_source_concept_id\", \n", "        \"route_source_value\", \"dose_unit_source_value\"\n", "    ],\n", "    \"MEASUREMENT\": [\n", "        \"measurement_id\", \"person_id\", \"measurement_concept_id\", \"measurement_date\", \n", "        \"measurement_datetime\", \"measurement_time\", \"measurement_type_concept_id\", \n", "        \"operator_concept_id\", \"value_as_number\", \"value_as_concept_id\", \"unit_concept_id\", \n", "        \"range_low\", \"range_high\", \"provider_id\", \"visit_occurrence_id\", \"visit_detail_id\", \n", "        \"measurement_source_value\", \"measurement_source_concept_id\", \"unit_source_value\", \n", "        \"value_source_value\"\n", "    ],\n", "    \"CONCEPT\": [\n", "        \"concept_id\", \"concept_name\", \"domain_id\", \"vocabulary_id\", \"concept_class_id\", \n", "        \"standard_concept\", \"concept_code\", \"valid_start_date\", \"valid_end_date\", \n", "        \"invalid_reason\"\n", "    ]\n", "}\n", "\n", "# Display the structure of the PERSON table\n", "print(\"PERSON Table Structure:\")\n", "for i, column in enumerate(omop_tables[\"PERSON\"]):\n", "    print(f\"{i+1}. {column}\")\n", "\n", "# Display the structure of the CONDITION_OCCURRENCE table\n", "print(\"\\nCONDITION_OCCURRENCE Table Structure:\")\n", "for i, column in enumerate(omop_tables[\"CONDITION_OCCURRENCE\"]):\n", "    print(f\"{i+1}. {column}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. OMOP Vocabulary System\n", "\n", "One of the key features of the OMOP CDM is its standardized vocabulary system. This system provides a common representation of healthcare concepts across different source data.\n", "\n", "### Standard Concepts\n", "\n", "In OMOP, healthcare concepts are represented by unique concept_ids. These concepts are organized into:\n", "\n", "- **Standard concepts**: The preferred concepts for analysis\n", "- **Non-standard concepts**: Source concepts that map to standard concepts\n", "\n", "### Vocabularies\n", "\n", "The OMOP vocabulary includes many standard terminologies:\n", "\n", "- **SNOMED CT**: Clinical terms for conditions, procedures, etc.\n", "- **RxNorm**: Medications\n", "- **LOINC**: Laboratory tests and clinical measurements\n", "- **ICD-10-CM/PCS**: Diagnoses and procedures (mapped to SNOMED)\n", "- **CPT-4**: Procedures (mapped to SNOMED)\n", "- **HCPCS**: Healthcare services (mapped to SNOMED)\n", "- **NDC**: Drug products (mapped to RxNorm)\n", "- **ATC**: Anatomical Therapeutic Chemical Classification\n", "\n", "### Domains\n", "\n", "Concepts in OMOP are organized into domains that correspond to the clinical data tables:\n", "\n", "- **Condition**: Medical conditions and diagnoses\n", "- **Drug**: Medications and drug therapies\n", "- **Procedure**: Medical procedures\n", "- **Measurement**: Laboratory tests and clinical measurements\n", "- **Observation**: Clinical observations\n", "- **Device**: Medical devices\n", "- **Visit**: Types of healthcare encounters\n", "\n", "### Concept Relationships\n", "\n", "OMOP defines relationships between concepts, including:\n", "\n", "- **Maps to**: Maps a non-standard concept to a standard concept\n", "- **Is a**: Indicates a hierarchical relationship\n", "- **Subsumes**: Indicates a broader concept that includes narrower concepts\n", "- **Has ingredient**: Links a drug product to its ingredients\n", "\n", "Let's create some example data to illustrate the OMOP vocabulary system:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept_id</th>\n", "      <th>concept_name</th>\n", "      <th>domain_id</th>\n", "      <th>vocabulary_id</th>\n", "      <th>concept_class_id</th>\n", "      <th>standard_concept</th>\n", "      <th>concept_code</th>\n", "      <th>valid_start_date</th>\n", "      <th>valid_end_date</th>\n", "      <th>invalid_reason</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>8507</td>\n", "      <td>Male</td>\n", "      <td>Gender</td>\n", "      <td>Gender</td>\n", "      <td>Gender</td>\n", "      <td>S</td>\n", "      <td>M</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>8532</td>\n", "      <td>Female</td>\n", "      <td>Gender</td>\n", "      <td>Gender</td>\n", "      <td>Gender</td>\n", "      <td>S</td>\n", "      <td>F</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>320128</td>\n", "      <td>Essential hypertension</td>\n", "      <td>Condition</td>\n", "      <td>SNOMED</td>\n", "      <td>Clinical Finding</td>\n", "      <td>S</td>\n", "      <td>59621000</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>44821244</td>\n", "      <td>Hypertension</td>\n", "      <td>Condition</td>\n", "      <td>ICD10CM</td>\n", "      <td>3-char billing code</td>\n", "      <td>None</td>\n", "      <td>I10</td>\n", "      <td>2015-10-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1112807</td>\n", "      <td>Ambulatory blood pressure monitoring</td>\n", "      <td>Procedure</td>\n", "      <td>SNOMED</td>\n", "      <td>Procedure</td>\n", "      <td>S</td>\n", "      <td>698452002</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3004249</td>\n", "      <td>Diastolic blood pressure</td>\n", "      <td>Measurement</td>\n", "      <td>LOINC</td>\n", "      <td>Clinical Observation</td>\n", "      <td>S</td>\n", "      <td>8462-4</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3012888</td>\n", "      <td>Systolic blood pressure</td>\n", "      <td>Measurement</td>\n", "      <td>LOINC</td>\n", "      <td>Clinical Observation</td>\n", "      <td>S</td>\n", "      <td>8480-6</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>1545999</td>\n", "      <td>Lisinopril 10 MG Oral Tablet</td>\n", "      <td>Drug</td>\n", "      <td>RxNorm</td>\n", "      <td>Clinical Drug</td>\n", "      <td>S</td>\n", "      <td>314076</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>1308216</td>\n", "      <td>Lisinopril</td>\n", "      <td>Drug</td>\n", "      <td>RxNorm</td>\n", "      <td>Ingredient</td>\n", "      <td>S</td>\n", "      <td>29046</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>9201</td>\n", "      <td>Inpatient Visit</td>\n", "      <td>Visit</td>\n", "      <td>Visit</td>\n", "      <td>Visit</td>\n", "      <td>S</td>\n", "      <td>IP</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   concept_id                          concept_name    domain_id  \\\n", "0        8507                                  Male       Gender   \n", "1        8532                                Female       Gender   \n", "2      320128                Essential hypertension    Condition   \n", "3    44821244                          Hypertension    Condition   \n", "4     1112807  Ambulatory blood pressure monitoring    Procedure   \n", "5     3004249              Diastolic blood pressure  Measurement   \n", "6     3012888               Systolic blood pressure  Measurement   \n", "7     1545999          Lisinopril 10 MG Oral Tablet         Drug   \n", "8     1308216                            Lisinopril         Drug   \n", "9        9201                       Inpatient Visit        Visit   \n", "\n", "  vocabulary_id      concept_class_id standard_concept concept_code  \\\n", "0        Gender                Gender                S            M   \n", "1        Gender                Gender                S            F   \n", "2        SNOMED      Clinical Finding                S     59621000   \n", "3       ICD10CM   3-char billing code             None          I10   \n", "4        SNOMED             Procedure                S    698452002   \n", "5         LOINC  Clinical Observation                S       8462-4   \n", "6         LOINC  Clinical Observation                S       8480-6   \n", "7        RxNorm         Clinical Drug                S       314076   \n", "8        RxNorm            Ingredient                S        29046   \n", "9         Visit                 Visit                S           IP   \n", "\n", "  valid_start_date valid_end_date invalid_reason  \n", "0       1970-01-01     2099-12-31           None  \n", "1       1970-01-01     2099-12-31           None  \n", "2       1970-01-01     2099-12-31           None  \n", "3       2015-10-01     2099-12-31           None  \n", "4       1970-01-01     2099-12-31           None  \n", "5       1970-01-01     2099-12-31           None  \n", "6       1970-01-01     2099-12-31           None  \n", "7       1970-01-01     2099-12-31           None  \n", "8       1970-01-01     2099-12-31           None  \n", "9       1970-01-01     2099-12-31           None  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create example CONCEPT data\n", "concept_data = [\n", "    {\n", "        \"concept_id\": 8507,\n", "        \"concept_name\": \"Male\",\n", "        \"domain_id\": \"Gender\",\n", "        \"vocabulary_id\": \"Gender\",\n", "        \"concept_class_id\": \"Gender\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"M\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 8532,\n", "        \"concept_name\": \"Female\",\n", "        \"domain_id\": \"Gender\",\n", "        \"vocabulary_id\": \"Gender\",\n", "        \"concept_class_id\": \"Gender\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"F\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 320128,\n", "        \"concept_name\": \"Essential hypertension\",\n", "        \"domain_id\": \"Condition\",\n", "        \"vocabulary_id\": \"SNOMED\",\n", "        \"concept_class_id\": \"Clinical Finding\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"59621000\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 44821244,\n", "        \"concept_name\": \"Hypertension\",\n", "        \"domain_id\": \"Condition\",\n", "        \"vocabulary_id\": \"ICD10CM\",\n", "        \"concept_class_id\": \"3-char billing code\",\n", "        \"standard_concept\": None,\n", "        \"concept_code\": \"I10\",\n", "        \"valid_start_date\": \"2015-10-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 1112807,\n", "        \"concept_name\": \"Ambulatory blood pressure monitoring\",\n", "        \"domain_id\": \"Procedure\",\n", "        \"vocabulary_id\": \"SNOMED\",\n", "        \"concept_class_id\": \"Procedure\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"698452002\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 3004249,\n", "        \"concept_name\": \"Diastolic blood pressure\",\n", "        \"domain_id\": \"Measurement\",\n", "        \"vocabulary_id\": \"LOINC\",\n", "        \"concept_class_id\": \"Clinical Observation\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"8462-4\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 3012888,\n", "        \"concept_name\": \"Systolic blood pressure\",\n", "        \"domain_id\": \"Measurement\",\n", "        \"vocabulary_id\": \"LOINC\",\n", "        \"concept_class_id\": \"Clinical Observation\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"8480-6\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 1545999,\n", "        \"concept_name\": \"Lisinopril 10 MG Oral Tablet\",\n", "        \"domain_id\": \"Drug\",\n", "        \"vocabulary_id\": \"RxNorm\",\n", "        \"concept_class_id\": \"Clinical Drug\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"314076\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 1308216,\n", "        \"concept_name\": \"Lisinopril\",\n", "        \"domain_id\": \"Drug\",\n", "        \"vocabulary_id\": \"RxNorm\",\n", "        \"concept_class_id\": \"Ingredient\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"29046\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id\": 9201,\n", "        \"concept_name\": \"Inpatient Visit\",\n", "        \"domain_id\": \"Visit\",\n", "        \"vocabulary_id\": \"Visit\",\n", "        \"concept_class_id\": \"Visit\",\n", "        \"standard_concept\": \"S\",\n", "        \"concept_code\": \"IP\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    }\n", "]\n", "\n", "# Create a DataFrame from the concept data\n", "concept_df = pd.DataFrame(concept_data)\n", "\n", "# Display the concept data\n", "concept_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM) - Part 4\n", "\n", "## 3. OMOP Vocabulary System (continued)\n", "\n", "Let's continue exploring the OMOP vocabulary system with some example data:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Example CONCEPT_RELATIONSHIP Data:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>concept_id_1</th>\n", "      <th>concept_id_2</th>\n", "      <th>relationship_id</th>\n", "      <th>valid_start_date</th>\n", "      <th>valid_end_date</th>\n", "      <th>invalid_reason</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>44821244</td>\n", "      <td>320128</td>\n", "      <td>Maps to</td>\n", "      <td>2015-10-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1545999</td>\n", "      <td>1308216</td>\n", "      <td>Has ingredient</td>\n", "      <td>1970-01-01</td>\n", "      <td>2099-12-31</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   concept_id_1  concept_id_2 relationship_id valid_start_date valid_end_date  \\\n", "0      44821244        320128         Maps to       2015-10-01     2099-12-31   \n", "1       1545999       1308216  Has ingredient       1970-01-01     2099-12-31   \n", "\n", "  invalid_reason  \n", "0           None  \n", "1           None  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Create example CONCEPT_RELATIONSHIP data\n", "concept_relationship_data = [\n", "    {\n", "        \"concept_id_1\": 44821244,  # ICD-10 Hypertension\n", "        \"concept_id_2\": 320128,    # SNOMED Essential hypertension\n", "        \"relationship_id\": \"Maps to\",\n", "        \"valid_start_date\": \"2015-10-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    },\n", "    {\n", "        \"concept_id_1\": 1545999,   # Lisinopril 10 MG Oral Tablet\n", "        \"concept_id_2\": 1308216,   # Lisinopril\n", "        \"relationship_id\": \"Has ingredient\",\n", "        \"valid_start_date\": \"1970-01-01\",\n", "        \"valid_end_date\": \"2099-12-31\",\n", "        \"invalid_reason\": None\n", "    }\n", "]\n", "\n", "# Create a DataFrame from the concept relationship data\n", "concept_relationship_df = pd.DataFrame(concept_relationship_data)\n", "\n", "# Display the concept relationship data\n", "print(\"\\nExample CONCEPT_RELATIONSHIP Data:\")\n", "display(concept_relationship_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Concept Mapping Process\n", "\n", "When data is transformed into the OMOP CDM, source codes (e.g., ICD-10, CPT) are mapped to standard concepts. This process involves:\n", "\n", "1. **Source value preservation**: Original codes are stored in *_source_value fields\n", "2. **Source concept identification**: Source codes are linked to source concepts\n", "3. **Standard concept mapping**: Source concepts are mapped to standard concepts\n", "\n", "For example, an ICD-10-CM diagnosis code \"I10\" (Hypertension) would be:\n", "\n", "1. Stored as \"I10\" in condition_source_value\n", "2. Linked to concept_id 44821244 (ICD-10-CM Hypertension) in condition_source_concept_id\n", "3. Mapped to concept_id 320128 (SNOMED Essential hypertension) in condition_concept_id\n", "\n", "This mapping process ensures that analyses can be performed using standardized concepts while preserving the original source data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Working with OMOP Data\n", "\n", "Now let's explore how to work with OMOP data using SQL and Python. We'll create a small SQLite database with some example OMOP data to demonstrate."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Create a SQLite database with example OMOP tables\n", "def create_example_omop_database():\n", "    \"\"\"Create a SQLite database with example OMOP tables.\"\"\"\n", "    # Create a connection to a new SQLite database\n", "    conn = sqlite3.connect('example_omop.db')\n", "    cursor = conn.cursor()\n", "    \n", "    # Create PERSON table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS person (\n", "        person_id INTEGER PRIMARY KEY,\n", "        gender_concept_id INTEGER,\n", "        year_of_birth INTEGER,\n", "        month_of_birth INTEGER,\n", "        day_of_birth INTEGER,\n", "        birth_datetime TEXT,\n", "        race_concept_id INTEGER,\n", "        ethnicity_concept_id INTEGER,\n", "        location_id INTEGER,\n", "        provider_id INTEGER,\n", "        care_site_id INTEGER,\n", "        person_source_value TEXT,\n", "        gender_source_value TEXT,\n", "        gender_source_concept_id INTEGER,\n", "        race_source_value TEXT,\n", "        race_source_concept_id INTEGER,\n", "        ethnicity_source_value TEXT,\n", "        ethnicity_source_concept_id INTEGER\n", "    )\n", "    ''')\n", "    \n", "    # Create VISIT_OCCURRENCE table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS visit_occurrence (\n", "        visit_occurrence_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        visit_concept_id INTEGER,\n", "        visit_start_date TEXT,\n", "        visit_start_datetime TEXT,\n", "        visit_end_date TEXT,\n", "        visit_end_datetime TEXT,\n", "        visit_type_concept_id INTEGER,\n", "        provider_id INTEGER,\n", "        care_site_id INTEGER,\n", "        visit_source_value TEXT,\n", "        visit_source_concept_id INTEGER,\n", "        admitted_from_concept_id INTEGER,\n", "        admitted_from_source_value TEXT,\n", "        discharge_to_concept_id INTEGER,\n", "        discharge_to_source_value TEXT,\n", "        preceding_visit_occurrence_id INTEGER,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id)\n", "    )\n", "    ''')\n", "    \n", "    # Create CONDITION_OCCURRENCE table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS condition_occurrence (\n", "        condition_occurrence_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        condition_concept_id INTEGER,\n", "        condition_start_date TEXT,\n", "        condition_start_datetime TEXT,\n", "        condition_end_date TEXT,\n", "        condition_end_datetime TEXT,\n", "        condition_type_concept_id INTEGER,\n", "        condition_status_concept_id INTEGER,\n", "        stop_reason TEXT,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        condition_source_value TEXT,\n", "        condition_source_concept_id INTEGER,\n", "        condition_status_source_value TEXT,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id),\n", "        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)\n", "    )\n", "    ''')\n", "    \n", "    # Create DRUG_EXPOSURE table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS drug_exposure (\n", "        drug_exposure_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        drug_concept_id INTEGER,\n", "        drug_exposure_start_date TEXT,\n", "        drug_exposure_start_datetime TEXT,\n", "        drug_exposure_end_date TEXT,\n", "        drug_exposure_end_datetime TEXT,\n", "        verbatim_end_date TEXT,\n", "        drug_type_concept_id INTEGER,\n", "        stop_reason TEXT,\n", "        refills INTEGER,\n", "        quantity REAL,\n", "        days_supply INTEGER,\n", "        sig TEXT,\n", "        route_concept_id INTEGER,\n", "        lot_number TEXT,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        drug_source_value TEXT,\n", "        drug_source_concept_id INTEGER,\n", "        route_source_value TEXT,\n", "        dose_unit_source_value TEXT,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id),\n", "        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)\n", "    )\n", "    ''')\n", "    \n", "    # Create MEASUREMENT table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS measurement (\n", "        measurement_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        measurement_concept_id INTEGER,\n", "        measurement_date TEXT,\n", "        measurement_datetime TEXT,\n", "        measurement_time TEXT,\n", "        measurement_type_concept_id INTEGER,\n", "        operator_concept_id INTEGER,\n", "        value_as_number REAL,\n", "        value_as_concept_id INTEGER,\n", "        unit_concept_id INTEGER,\n", "        range_low REAL,\n", "        range_high REAL,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        measurement_source_value TEXT,\n", "        measurement_source_concept_id INTEGER,\n", "        unit_source_value TEXT,\n", "        value_source_value TEXT,\n", "        FOREIGN KEY (person_id) REFERENCES person(person_id),\n", "        FOREIGN KEY (visit_occurrence_id) REFERENCES visit_occurrence(visit_occurrence_id)\n", "    )\n", "    ''')\n", "    \n", "    # Create CONCEPT table\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS concept (\n", "        concept_id INTEGER PRIMARY KEY,\n", "        concept_name TEXT,\n", "        domain_id TEXT,\n", "        vocabulary_id TEXT,\n", "        concept_class_id TEXT,\n", "        standard_concept TEXT,\n", "        concept_code TEXT,\n", "        valid_start_date TEXT,\n", "        valid_end_date TEXT,\n", "        invalid_reason TEXT\n", "    )\n", "    ''')\n", "    \n", "    conn.commit()\n", "    return conn"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM) - Part 5\n", "\n", "## 4. Working with OMOP Data (continued)\n", "\n", "Let's continue with our example OMOP database by populating it with sample data:"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating OMOP CDM tables for our tutorial...\n", "\n", "1. Creating PERSON table...\n", "2. Creating VISIT_OCCURRENCE table...\n", "3. Creating CONDITION_OCCURRENCE table...\n", "4. Creating DRUG_EXPOSURE table...\n", "5. Creating MEASUREMENT table...\n", "6. Creating CONCEPT table...\n", "\n", "All OMOP CDM tables have been created successfully!\n"]}], "source": ["# Introduction to OMOP CDM Database Setup\n", "print(\"Creating OMOP CDM tables for our tutorial...\")\n", "\n", "import sqlite3\n", "\n", "def create_omop_tables():\n", "    \"\"\"Create the basic OMOP CDM tables needed for this tutorial\"\"\"\n", "    conn = sqlite3.connect('example_omop.db')\n", "    cursor = conn.cursor()\n", "    \n", "    # Create PERSON table\n", "    print(\"\\n1. Creating PERSON table...\")\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS person (\n", "        person_id INTEGER PRIMARY KEY,\n", "        gender_concept_id INTEGER,\n", "        year_of_birth INTEGER,\n", "        month_of_birth INTEGER,\n", "        day_of_birth INTEGER,\n", "        birth_datetime TEXT,\n", "        race_concept_id INTEGER,\n", "        ethnicity_concept_id INTEGER,\n", "        location_id INTEGER,\n", "        provider_id INTEGER,\n", "        care_site_id INTEGER,\n", "        person_source_value TEXT,\n", "        gender_source_value TEXT,\n", "        gender_source_concept_id INTEGER,\n", "        race_source_value TEXT,\n", "        race_source_concept_id INTEGER,\n", "        ethnicity_source_value TEXT,\n", "        ethnicity_source_concept_id INTEGER\n", "    )\n", "    ''')\n", "    \n", "    # Create VISIT_OCCURRENCE table\n", "    print(\"2. Creating VISIT_OCCURRENCE table...\")\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS visit_occurrence (\n", "        visit_occurrence_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        visit_concept_id INTEGER,\n", "        visit_start_date TEXT,\n", "        visit_start_datetime TEXT,\n", "        visit_end_date TEXT,\n", "        visit_end_datetime TEXT,\n", "        visit_type_concept_id INTEGER,\n", "        provider_id INTEGER,\n", "        care_site_id INTEGER,\n", "        visit_source_value TEXT,\n", "        visit_source_concept_id INTEGER,\n", "        admitted_from_concept_id INTEGER,\n", "        admitted_from_source_value TEXT,\n", "        discharge_to_concept_id INTEGER,\n", "        discharge_to_source_value TEXT,\n", "        preceding_visit_occurrence_id INTEGER\n", "    )\n", "    ''')\n", "    \n", "    # Create CONDITION_OCCURRENCE table\n", "    print(\"3. Creating CONDITION_OCCURRENCE table...\")\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS condition_occurrence (\n", "        condition_occurrence_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        condition_concept_id INTEGER,\n", "        condition_start_date TEXT,\n", "        condition_start_datetime TEXT,\n", "        condition_end_date TEXT,\n", "        condition_end_datetime TEXT,\n", "        condition_type_concept_id INTEGER,\n", "        condition_status_concept_id INTEGER,\n", "        stop_reason TEXT,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        condition_source_value TEXT,\n", "        condition_source_concept_id INTEGER,\n", "        condition_status_source_value TEXT\n", "    )\n", "    ''')\n", "    \n", "    # Create DRUG_EXPOSURE table\n", "    print(\"4. Creating DRUG_EXPOSURE table...\")\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS drug_exposure (\n", "        drug_exposure_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        drug_concept_id INTEGER,\n", "        drug_exposure_start_date TEXT,\n", "        drug_exposure_start_datetime TEXT,\n", "        drug_exposure_end_date TEXT,\n", "        drug_exposure_end_datetime TEXT,\n", "        verbatim_end_date TEXT,\n", "        drug_type_concept_id INTEGER,\n", "        stop_reason TEXT,\n", "        refills INTEGER,\n", "        quantity REAL,\n", "        days_supply INTEGER,\n", "        sig TEXT,\n", "        route_concept_id INTEGER,\n", "        lot_number TEXT,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        drug_source_value TEXT,\n", "        drug_source_concept_id INTEGER,\n", "        route_source_value TEXT,\n", "        dose_unit_source_value TEXT\n", "    )\n", "    ''')\n", "    \n", "    # Create MEASUREMENT table\n", "    print(\"5. Creating MEASUREMENT table...\")\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS measurement (\n", "        measurement_id INTEGER PRIMARY KEY,\n", "        person_id INTEGER,\n", "        measurement_concept_id INTEGER,\n", "        measurement_date TEXT,\n", "        measurement_datetime TEXT,\n", "        measurement_time TEXT,\n", "        measurement_type_concept_id INTEGER,\n", "        operator_concept_id INTEGER,\n", "        value_as_number REAL,\n", "        value_as_concept_id INTEGER,\n", "        unit_concept_id INTEGER,\n", "        range_low REAL,\n", "        range_high REAL,\n", "        provider_id INTEGER,\n", "        visit_occurrence_id INTEGER,\n", "        visit_detail_id INTEGER,\n", "        measurement_source_value TEXT,\n", "        measurement_source_concept_id INTEGER,\n", "        unit_source_value TEXT,\n", "        value_source_value TEXT\n", "    )\n", "    ''')\n", "    \n", "    # Create CONCEPT table\n", "    print(\"6. Creating CONCEPT table...\")\n", "    cursor.execute('''\n", "    CREATE TABLE IF NOT EXISTS concept (\n", "        concept_id INTEGER PRIMARY KEY,\n", "        concept_name TEXT,\n", "        domain_id TEXT,\n", "        vocabulary_id TEXT,\n", "        concept_class_id TEXT,\n", "        standard_concept TEXT,\n", "        concept_code TEXT,\n", "        valid_start_date TEXT,\n", "        valid_end_date TEXT,\n", "        invalid_reason TEXT\n", "    )\n", "    ''')\n", "    \n", "    conn.commit()\n", "    conn.close()\n", "    print(\"\\nAll OMOP CDM tables have been created successfully!\")\n", "\n", "# Execute the function to create all tables\n", "create_omop_tables()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connecting to the OMOP database...\n", "\n", "Populating OMOP tables with example data...\n", "1. Adding person records...\n"]}, {"ename": "OperationalError", "evalue": "database is locked", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mOperationalError\u001b[39m                          <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[12]\u001b[39m\u001b[32m, line 17\u001b[39m\n\u001b[32m      9\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m1. Adding person records...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     10\u001b[39m person_data = [\n\u001b[32m     11\u001b[39m     (\u001b[32m1\u001b[39m, \u001b[32m8507\u001b[39m, \u001b[32m1960\u001b[39m, \u001b[32m1\u001b[39m, \u001b[32m1\u001b[39m, \u001b[33m'\u001b[39m\u001b[33m1960-01-01 00:00:00\u001b[39m\u001b[33m'\u001b[39m, \u001b[32m8516\u001b[39m, \u001b[32m38003564\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mPT1\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mM\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mBlack\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mN<PERSON>\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mNot Hispanic\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[32m     12\u001b[39m     (\u001b[32m2\u001b[39m, \u001b[32m8532\u001b[39m, \u001b[32m1970\u001b[39m, \u001b[32m5\u001b[39m, \u001b[32m15\u001b[39m, \u001b[33m'\u001b[39m\u001b[33m1970-05-15 00:00:00\u001b[39m\u001b[33m'\u001b[39m, \u001b[32m8527\u001b[39m, \u001b[32m38003564\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mPT2\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mF\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mWhite\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mNot Hispanic\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[32m     13\u001b[39m     (\u001b[32m3\u001b[39m, \u001b[32m8507\u001b[39m, \u001b[32m1980\u001b[39m, \u001b[32m10\u001b[39m, \u001b[32m30\u001b[39m, \u001b[33m'\u001b[39m\u001b[33m1980-10-30 00:00:00\u001b[39m\u001b[33m'\u001b[39m, \u001b[32m8527\u001b[39m, \u001b[32m38003563\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mPT3\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mM\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mWhite\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mHispanic\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m),\n\u001b[32m     14\u001b[39m     (\u001b[32m4\u001b[39m, \u001b[32m8532\u001b[39m, \u001b[32m1990\u001b[39m, \u001b[32m12\u001b[39m, \u001b[32m25\u001b[39m, \u001b[33m'\u001b[39m\u001b[33m1990-12-25 00:00:00\u001b[39m\u001b[33m'\u001b[39m, \u001b[32m8516\u001b[39m, \u001b[32m38003564\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mPT4\u001b[39m\u001b[33m'\u001b[39m, \u001b[33m'\u001b[39m\u001b[33mF\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mBlack\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[33m'\u001b[39m\u001b[33mNot Hispanic\u001b[39m\u001b[33m'\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m     15\u001b[39m ]\n\u001b[32m---> \u001b[39m\u001b[32m17\u001b[39m \u001b[43mcursor\u001b[49m\u001b[43m.\u001b[49m\u001b[43mexecutemany\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m'''\u001b[39;49m\n\u001b[32m     18\u001b[39m \u001b[33;43mINSERT INTO person (\u001b[39;49m\n\u001b[32m     19\u001b[39m \u001b[33;43m    person_id, gender_concept_id, year_of_birth, month_of_birth, day_of_birth, \u001b[39;49m\n\u001b[32m     20\u001b[39m \u001b[33;43m    birth_datetime, race_concept_id, ethnicity_concept_id, location_id, \u001b[39;49m\n\u001b[32m     21\u001b[39m \u001b[33;43m    provider_id, care_site_id, person_source_value, gender_source_value, \u001b[39;49m\n\u001b[32m     22\u001b[39m \u001b[33;43m    gender_source_concept_id, race_source_value, race_source_concept_id, \u001b[39;49m\n\u001b[32m     23\u001b[39m \u001b[33;43m    ethnicity_source_value, ethnicity_source_concept_id\u001b[39;49m\n\u001b[32m     24\u001b[39m \u001b[33;43m) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\u001b[39;49m\n\u001b[32m     25\u001b[39m \u001b[33;43m'''\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mperson_data\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     27\u001b[39m \u001b[38;5;66;03m# 2. Insert example data into VISIT_OCCURRENCE table\u001b[39;00m\n\u001b[32m     28\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m2. Adding visit records...\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mOperationalError\u001b[39m: database is locked"]}], "source": ["# Connect to the database\n", "print(\"Connecting to the OMOP database...\")\n", "conn = sqlite3.connect('example_omop.db')\n", "cursor = conn.cursor()\n", "\n", "print(\"\\nPopulating OMOP tables with example data...\")\n", "\n", "# 1. Insert example data into PERSON table\n", "print(\"1. Adding person records...\")\n", "person_data = [\n", "    (1, 8507, 1960, 1, 1, '1960-01-01 00:00:00', 8516, 38003564, None, None, None, 'PT1', 'M', None, 'Black', None, 'Not Hispanic', None),\n", "    (2, 8532, 1970, 5, 15, '1970-05-15 00:00:00', 8527, 38003564, None, None, None, 'PT2', 'F', None, 'White', None, 'Not Hispanic', None),\n", "    (3, 8507, 1980, 10, 30, '1980-10-30 00:00:00', 8527, 38003563, None, None, None, 'PT3', 'M', None, 'White', None, 'Hispanic', None),\n", "    (4, 8532, 1990, 12, 25, '1990-12-25 00:00:00', 8516, 38003564, None, None, None, 'PT4', 'F', None, 'Black', None, 'Not Hispanic', None)\n", "]\n", "\n", "cursor.executemany('''\n", "INSERT INTO person (\n", "    person_id, gender_concept_id, year_of_birth, month_of_birth, day_of_birth, \n", "    birth_datetime, race_concept_id, ethnicity_concept_id, location_id, \n", "    provider_id, care_site_id, person_source_value, gender_source_value, \n", "    gender_source_concept_id, race_source_value, race_source_concept_id, \n", "    ethnicity_source_value, ethnicity_source_concept_id\n", ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n", "''', person_data)\n", "\n", "# 2. Insert example data into VISIT_OCCURRENCE table\n", "print(\"2. Adding visit records...\")\n", "visit_data = [\n", "    (1, 1, 9201, '2020-01-15', '2020-01-15 08:00:00', '2020-01-20', '2020-01-20 16:00:00', 44818518, None, None, 'VISIT1', None, None, None, None, None, None),\n", "    (2, 1, 9202, '2020-03-10', '2020-03-10 10:30:00', '2020-03-10', '2020-03-10 11:45:00', 44818519, None, None, 'VISIT2', None, None, None, None, None, None),\n", "    (3, 2, 9201, '2020-02-20', '2020-02-20 14:00:00', '2020-02-25', '2020-02-25 11:30:00', 44818518, None, None, 'VISIT3', None, None, None, None, None, None),\n", "    (4, 3, 9202, '2020-04-05', '2020-04-05 09:15:00', '2020-04-05', '2020-04-05 10:30:00', 44818519, None, None, 'VISIT4', None, None, None, None, None, None),\n", "    (5, 4, 9202, '2020-05-12', '2020-05-12 13:45:00', '2020-05-12', '2020-05-12 15:00:00', 44818519, None, None, 'VISIT5', None, None, None, None, None, None)\n", "]\n", "\n", "cursor.executemany('''\n", "INSERT INTO visit_occurrence VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n", "''', visit_data)\n", "\n", "# 3. Insert example data into CONDITION_OCCURRENCE table\n", "print(\"3. Adding condition records...\")\n", "condition_data = [\n", "    (1, 1, 320128, '2020-01-15', '2020-01-15 09:30:00', None, None, 32020, None, None, None, 1, None, 'I10', 44821244, None),\n", "    (2, 2, 320128, '2020-02-20', '2020-02-20 15:45:00', None, None, 32020, None, None, None, 3, None, 'I10', 44821244, None),\n", "    (3, 3, 201826, '2020-04-05', '2020-04-05 09:30:00', None, None, 32020, None, None, None, 4, None, 'E11.9', 45542411, None),\n", "    (4, 4, 317009, '2020-05-12', '2020-05-12 14:00:00', None, None, 32020, None, None, None, 5, None, 'J45.909', 45877007, None)\n", "]\n", "\n", "cursor.executemany('''\n", "INSERT INTO condition_occurrence VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n", "''', condition_data)\n", "\n", "# 4. Insert example data into DRUG_EXPOSURE table\n", "print(\"4. Adding drug exposure records...\")\n", "drug_data = [\n", "    (1, 1, 1545999, '2020-01-15', '2020-01-15 10:00:00', '2020-02-15', '2020-02-15 00:00:00', None, 38000177, None, 3, 30, 30, 'Take 1 tablet daily', None, None, None, 1, None, 'Lisinopril 10mg', None, 'Oral', None),\n", "    (2, 2, 1545999, '2020-02-20', '2020-02-20 16:30:00', '2020-03-20', '2020-03-20 00:00:00', None, 38000177, None, 3, 30, 30, 'Take 1 tablet daily', None, None, None, 3, None, 'Lisinopril 10mg', None, 'Oral', None),\n", "    (3, 3, 1503297, '2020-04-05', '2020-04-05 10:00:00', '2020-05-05', '2020-05-05 00:00:00', None, 38000177, None, 3, 30, 30, 'Take 1 tablet daily', None, None, None, 4, None, 'Metformin 500mg', None, 'Oral', None)\n", "]\n", "\n", "cursor.executemany('''\n", "INSERT INTO drug_exposure VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n", "''', drug_data)\n", "\n", "# 5. Insert example data into MEASUREMENT table\n", "print(\"5. Adding measurement records...\")\n", "measurement_data = [\n", "    (1, 1, 3004249, '2020-01-15', '2020-01-15 09:00:00', None, 44818701, 4172703, 90, None, 8876, 60, 90, None, 1, None, 'Diastolic BP', None, 'mmHg', '90'),\n", "    (2, 1, 3012888, '2020-01-15', '2020-01-15 09:00:00', None, 44818701, 4172703, 140, None, 8876, 90, 140, None, 1, None, 'Systolic BP', None, 'mmHg', '140'),\n", "    (3, 2, 3004249, '2020-02-20', '2020-02-20 15:00:00', None, 44818701, 4172703, 85, None, 8876, 60, 90, None, 3, None, 'Diastolic BP', None, 'mmHg', '85'),\n", "    (4, 2, 3012888, '2020-02-20', '2020-02-20 15:00:00', None, 44818701, 4172703, 135, None, 8876, 90, 140, None, 3, None, 'Systolic BP', None, 'mmHg', '135'),\n", "    (5, 3, 3004501, '2020-04-05', '2020-04-05 09:45:00', None, 44818701, 4172703, 7.2, None, 8554, 4.0, 5.7, None, 4, None, 'HbA1c', None, '%', '7.2')\n", "]\n", "\n", "cursor.executemany('''\n", "INSERT INTO measurement VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n", "''', measurement_data)\n", "\n", "# 6. Insert example data into CONCEPT table\n", "print(\"6. Adding concept records...\")\n", "concept_data = [\n", "    {'concept_id': 320128, 'concept_name': 'Essential hypertension', 'domain_id': 'Condition', 'vocabulary_id': 'SNOMED', 'concept_class_id': 'Clinical Finding', 'standard_concept': 'S', 'concept_code': '59621000', 'valid_start_date': '1970-01-01', 'valid_end_date': '2099-12-31', 'invalid_reason': None},\n", "    {'concept_id': 201826, 'concept_name': 'Type 2 diabetes mellitus', 'domain_id': 'Condition', 'vocabulary_id': 'SNOMED', 'concept_class_id': 'Clinical Finding', 'standard_concept': 'S', 'concept_code': '44054006', 'valid_start_date': '1970-01-01', 'valid_end_date': '2099-12-31', 'invalid_reason': None},\n", "    {'concept_id': 317009, 'concept_name': 'Asthma', 'domain_id': 'Condition', 'vocabulary_id': 'SNOMED', 'concept_class_id': 'Clinical Finding', 'standard_concept': 'S', 'concept_code': '195967001', 'valid_start_date': '1970-01-01', 'valid_end_date': '2099-12-31', 'invalid_reason': None}\n", "]\n", "\n", "for concept in concept_data:\n", "    cursor.execute('''\n", "    INSERT INTO concept VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n", "    ''', (\n", "        concept['concept_id'], concept['concept_name'], concept['domain_id'],\n", "        concept['vocabulary_id'], concept['concept_class_id'], concept['standard_concept'],\n", "        concept['concept_code'], concept['valid_start_date'], concept['valid_end_date'],\n", "        concept['invalid_reason']\n", "    ))\n", "\n", "# Commit changes and close connection\n", "conn.commit()\n", "print(\"\\nAll example data has been successfully loaded into the OMOP database!\")\n", "conn.close()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM) - Part 6\n", "\n", "## 4. Working with OMOP Data (continued)\n", "\n", "### Querying OMOP Data with SQL\n", "\n", "Now that we have an example OMOP database, let's explore how to query it using SQL. We'll start with some basic queries and then move on to more complex ones."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to execute SQL queries and return results as a DataFrame\n", "def run_sql_query(query, engine):\n", "    \"\"\"Execute a SQL query and return results as a DataFrame.\"\"\"\n", "    try:\n", "        return pd.read_sql_query(query, engine)\n", "    except Exception as e:\n", "        print(f\"Error executing query: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# Example 1: Basic query to get all patients\n", "query1 = \"\"\"\n", "SELECT \n", "    p.person_id,\n", "    p.gender_concept_id,\n", "    c.concept_name as gender,\n", "    p.year_of_birth,\n", "    p.race_concept_id,\n", "    r.concept_name as race,\n", "    p.ethnicity_concept_id,\n", "    e.concept_name as ethnicity\n", "FROM \n", "    person p\n", "LEFT JOIN \n", "    concept c ON p.gender_concept_id = c.concept_id\n", "LEFT JOIN \n", "    concept r ON p.race_concept_id = r.concept_id\n", "LEFT JOIN \n", "    concept e ON p.ethnicity_concept_id = e.concept_id\n", "\"\"\"\n", "\n", "patients_df = run_sql_query(query1, engine)\n", "print(\"All Patients:\")\n", "display(patients_df)\n", "\n", "# Example 2: Query to get all conditions with concept names\n", "query2 = \"\"\"\n", "SELECT \n", "    co.condition_occurrence_id,\n", "    co.person_id,\n", "    co.condition_concept_id,\n", "    c.concept_name as condition_name,\n", "    co.condition_start_date,\n", "    co.visit_occurrence_id,\n", "    co.condition_source_value\n", "FROM \n", "    condition_occurrence co\n", "LEFT JOIN \n", "    concept c ON co.condition_concept_id = c.concept_id\n", "\"\"\"\n", "\n", "conditions_df = run_sql_query(query2, engine)\n", "print(\"\\nAll Conditions:\")\n", "display(conditions_df)\n", "\n", "# Example 3: Query to get all measurements with concept names\n", "query3 = \"\"\"\n", "SELECT \n", "    m.measurement_id,\n", "    m.person_id,\n", "    m.measurement_concept_id,\n", "    c.concept_name as measurement_name,\n", "    m.measurement_date,\n", "    m.value_as_number,\n", "    u.concept_name as unit,\n", "    m.visit_occurrence_id\n", "FROM \n", "    measurement m\n", "LEFT JOIN \n", "    concept c ON m.measurement_concept_id = c.concept_id\n", "LEFT JOIN \n", "    concept u ON m.unit_concept_id = u.concept_id\n", "\"\"\"\n", "\n", "measurements_df = run_sql_query(query3, engine)\n", "print(\"\\nAll Measurements:\")\n", "display(measurements_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM) - Part 7\n", "\n", "## 4. Working with OMOP Data (continued)\n", "\n", "### More Complex SQL Queries\n", "\n", "Now let's try some more complex queries that demonstrate the power of the OMOP CDM for healthcare analytics."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 4: Find patients with hypertension and their blood pressure measurements\n", "query4 = \"\"\"\n", "SELECT \n", "    p.person_id,\n", "    p.year_of_birth,\n", "    STRFTIME('%Y', 'now') - p.year_of_birth as age,\n", "    g.concept_name as gender,\n", "    co.condition_start_date as hypertension_date,\n", "    m_sys.measurement_date,\n", "    m_sys.value_as_number as systolic_bp,\n", "    m_dia.value_as_number as diastolic_bp\n", "FROM \n", "    person p\n", "JOIN \n", "    condition_occurrence co ON p.person_id = co.person_id\n", "JOIN \n", "    concept c_condition ON co.condition_concept_id = c_condition.concept_id\n", "JOIN \n", "    concept g ON p.gender_concept_id = g.concept_id\n", "JOIN \n", "    measurement m_sys ON p.person_id = m_sys.person_id\n", "JOIN \n", "    measurement m_dia ON m_sys.person_id = m_dia.person_id \n", "                      AND m_sys.measurement_date = m_dia.measurement_date\n", "WHERE \n", "    c_condition.concept_name = 'Essential hypertension'\n", "    AND m_sys.measurement_concept_id = 3012888  -- Systolic BP\n", "    AND m_dia.measurement_concept_id = 3004249  -- Diastolic BP\n", "\"\"\"\n", "\n", "hypertension_bp_df = run_sql_query(query4, engine)\n", "print(\"Patients with Hypertension and their Blood Pressure Measurements:\")\n", "display(hypertension_bp_df)\n", "\n", "# Example 5: Find patients on antihypertensive medications\n", "query5 = \"\"\"\n", "SELECT \n", "    p.person_id,\n", "    p.year_of_birth,\n", "    STRFTIME('%Y', 'now') - p.year_of_birth as age,\n", "    g.concept_name as gender,\n", "    d.drug_exposure_start_date,\n", "    d.drug_exposure_end_date,\n", "    c_drug.concept_name as drug_name,\n", "    d.days_supply,\n", "    d.quantity\n", "FROM \n", "    person p\n", "JOIN \n", "    drug_exposure d ON p.person_id = d.person_id\n", "JOIN \n", "    concept c_drug ON d.drug_concept_id = c_drug.concept_id\n", "JOIN \n", "    concept g ON p.gender_concept_id = g.concept_id\n", "WHERE \n", "    c_drug.concept_name LIKE '%Lisinopril%'\n", "\"\"\"\n", "\n", "antihypertensive_df = run_sql_query(query5, engine)\n", "print(\"\\nPatients on Antihypertensive Medications:\")\n", "display(antihypertensive_df)\n", "\n", "# Example 6: Find patients with both hypertension and diabetes\n", "query6 = \"\"\"\n", "SELECT \n", "    p.person_id,\n", "    p.year_of_birth,\n", "    STRFTIME('%Y', 'now') - p.year_of_birth as age,\n", "    g.concept_name as gender,\n", "    htn.condition_start_date as hypertension_date,\n", "    dm.condition_start_date as diabetes_date\n", "FROM \n", "    person p\n", "JOIN \n", "    condition_occurrence htn ON p.person_id = htn.person_id\n", "JOIN \n", "    concept c_htn ON htn.condition_concept_id = c_htn.concept_id\n", "JOIN \n", "    condition_occurrence dm ON p.person_id = dm.person_id\n", "JOIN \n", "    concept c_dm ON dm.condition_concept_id = c_dm.concept_id\n", "JOIN \n", "    concept g ON p.gender_concept_id = g.concept_id\n", "WHERE \n", "    c_htn.concept_name = 'Essential hypertension'\n", "    AND c_dm.concept_name LIKE '%diabetes%'\n", "\"\"\"\n", "\n", "comorbidity_df = run_sql_query(query6, engine)\n", "print(\"\\nPatients with Both Hypertension and Diabetes:\")\n", "display(comorbidity_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM) - Part 8\n", "\n", "## 4. Working with OMOP Data (continued)\n", "\n", "### Working with OMOP Data in Python\n", "\n", "While SQL is powerful for querying OMOP data, Python provides additional capabilities for data manipulation, analysis, and visualization. Let's explore how to work with OMOP data using Python."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load all tables into DataFrames\n", "person_df = pd.read_sql_table('person', engine)\n", "visit_df = pd.read_sql_table('visit_occurrence', engine)\n", "condition_df = pd.read_sql_table('condition_occurrence', engine)\n", "drug_df = pd.read_sql_table('drug_exposure', engine)\n", "measurement_df = pd.read_sql_table('measurement', engine)\n", "concept_df = pd.read_sql_table('concept', engine)\n", "\n", "# Display the first few rows of each table\n", "print(\"Person Table:\")\n", "display(person_df.head())\n", "\n", "print(\"\\nVisit Occurrence Table:\")\n", "display(visit_df.head())\n", "\n", "print(\"\\nCondition Occurrence Table:\")\n", "display(condition_df.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Analysis with Python\n", "\n", "Now let's perform some data analysis on our OMOP data using Python."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate age for each person\n", "current_year = datetime.now().year\n", "person_df['age'] = current_year - person_df['year_of_birth']\n", "\n", "# Analyze age distribution\n", "print(\"Age Statistics:\")\n", "print(person_df['age'].describe())\n", "\n", "# Create age groups\n", "bins = [0, 30, 40, 50, 60, 70, 100]\n", "labels = ['<30', '30-39', '40-49', '50-59', '60-69', '70+']\n", "person_df['age_group'] = pd.cut(person_df['age'], bins=bins, labels=labels, right=False)\n", "\n", "# Count patients by age group and gender\n", "age_gender_counts = person_df.groupby(['age_group', 'gender_concept_id']).size().unstack()\n", "\n", "# Replace gender concept IDs with names\n", "gender_map = {8507: 'Male', 8532: 'Female'}\n", "age_gender_counts.columns = [gender_map.get(col, col) for col in age_gender_counts.columns]\n", "\n", "# Plot age distribution by gender\n", "plt.figure(figsize=(10, 6))\n", "age_gender_counts.plot(kind='bar', stacked=True)\n", "plt.title('Patient Age Distribution by Gender')\n", "plt.xlabel('Age Group')\n", "plt.ylabel('Number of Patients')\n", "plt.xticks(rotation=0)\n", "plt.legend(title='Gender')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to OMOP Common Data Model (CDM) - Part 9\n", "\n", "## 4. Working with OMOP Data (continued)\n", "\n", "### Analyzing Conditions\n", "\n", "Let's analyze the conditions in our dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Merge condition data with concept names\n", "condition_with_names = pd.merge(\n", "    condition_df,\n", "    concept_df[['concept_id', 'concept_name']],\n", "    left_on='condition_concept_id',\n", "    right_on='concept_id',\n", "    how='left'\n", ")\n", "\n", "# Count conditions by concept name\n", "condition_counts = condition_with_names['concept_name'].value_counts()\n", "\n", "# Plot condition distribution\n", "plt.figure(figsize=(10, 6))\n", "condition_counts.plot(kind='bar')\n", "plt.title('Distribution of Conditions')\n", "plt.xlabel('Condition')\n", "plt.ylabel('Number of Occurrences')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Analyze conditions by gender\n", "condition_by_gender = pd.merge(\n", "    condition_with_names,\n", "    person_df[['person_id', 'gender_concept_id']],\n", "    on='person_id',\n", "    how='left'\n", ")\n", "\n", "# Replace gender concept IDs with names\n", "condition_by_gender['gender'] = condition_by_gender['gender_concept_id'].map(gender_map)\n", "\n", "# Count conditions by gender\n", "condition_gender_counts = condition_by_gender.groupby(['concept_name', 'gender']).size().unstack()\n", "\n", "# Plot conditions by gender\n", "plt.figure(figsize=(10, 6))\n", "condition_gender_counts.plot(kind='bar', stacked=True)\n", "plt.title('Conditions by Gender')\n", "plt.xlabel('Condition')\n", "plt.ylabel('Number of Occurrences')\n", "plt.xticks(rotation=45, ha='right')\n", "plt.legend(title='Gender')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> and Next Steps\n", "\n", "In this tutorial, we've explored the OMOP Common Data Model (CDM) and learned how to work with OMOP data using SQL and Python. Here's a summary of what we've covered:\n", "\n", "1. **OMOP CDM Structure**: We learned about the core tables in the OMOP CDM, including PERSON, VISIT_OCCURRENCE, CONDITION_OCCURRENCE, DRUG_EXPOSURE, and MEASUREMENT.\n", "\n", "2. **OMOP Vocabulary System**: We explored the standardized vocabulary system in OMOP, including the CONCEPT and CONCEPT_RELATIONSHIP tables, and how source codes are mapped to standard concepts.\n", "\n", "3. **Working with OMOP Data**: We learned how to query OMOP data using SQL and how to perform data analysis and visualization using Python.\n", "\n", "### Next Steps\n", "\n", "To continue learning about the OMOP CDM, consider the following next steps:\n", "\n", "1. **Explore the OHDSI Tools**: The Observational Health Data Sciences and Informatics (OHDSI) community has developed a suite of tools for working with OMOP data, including:\n", "   - **Atlas**: A web-based tool for exploring OMOP data and performing cohort definitions, characterizations, and population-level analyses.\n", "   - **Achilles**: A tool for data characterization and quality assessment.\n", "   - **WebAPI**: A RESTful API for interacting with OMOP data.\n", "\n", "2. **Learn about ETL Processes**: Explore how data is extracted, transformed, and loaded (ETL) from source systems into the OMOP CDM.\n", "\n", "3. **Participate in the OHDSI Community**: Join the OHDSI community to learn from others and contribute to the development of the OMOP CDM and related tools.\n", "\n", "4. **Apply OMOP to Your Own Data**: Consider how you might apply the OMOP CDM to your own healthcare data to enable standardized analytics and research.\n", "\n", "In the next tutorial, we'll explore how to map data from FHIR resources to the OMOP CDM, bridging these two important healthcare data standards."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Exercises\n", "\n", "To reinforce your understanding of the OMOP CDM, try the following exercises:\n", "\n", "1. Write a SQL query to find all patients with a specific condition (e.g., hypertension) and their demographics.\n", "\n", "2. Write a SQL query to find all medications prescribed for a specific condition.\n", "\n", "3. Use Python to create a visualization showing the distribution of measurements (e.g., blood pressure) over time for a specific patient.\n", "\n", "4. Use Python to analyze the relationship between a condition (e.g., hypertension) and a measurement (e.g., blood pressure).\n", "\n", "5. Create a cohort of patients with a specific condition and analyze their demographics, medications, and measurements.\n", "\n", "### Exercise Solutions\n", "\n", "Here's a solution for Exercise 1:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exercise 1: Find all patients with hypertension and their demographics\n", "exercise1_query = \"\"\"\n", "SELECT \n", "    p.person_id,\n", "    p.year_of_birth,\n", "    STRFTIME('%Y', 'now') - p.year_of_birth as age,\n", "    g.concept_name as gender,\n", "    r.concept_name as race,\n", "    e.concept_name as ethnicity,\n", "    co.condition_start_date\n", "FROM \n", "    person p\n", "JOIN \n", "    condition_occurrence co ON p.person_id = co.person_id\n", "JOIN \n", "    concept c ON co.condition_concept_id = c.concept_id\n", "LEFT JOIN \n", "    concept g ON p.gender_concept_id = g.concept_id\n", "LEFT JOIN \n", "    concept r ON p.race_concept_id = r.concept_id\n", "LEFT JOIN \n", "    concept e ON p.ethnicity_concept_id = e.concept_id\n", "WHERE \n", "    c.concept_name = 'Essential hypertension'\n", "\"\"\"\n", "\n", "exercise1_df = run_sql_query(exercise1_query, engine)\n", "print(\"Patients with Hypertension and their Demographics:\")\n", "display(exercise1_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Try solving the other exercises on your own to reinforce your understanding of the OMOP CDM and SQL/Python for healthcare data analysis."]}], "metadata": {"kernelspec": {"display_name": "fhir-omop", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}