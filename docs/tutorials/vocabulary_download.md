# OMOP Vocabulary Download Tutorial

This tutorial guides you through the process of downloading OMOP vocabularies from Athena.

## Prerequisites

- Athena account (see [Athena Registration Tutorial](athena_registration.md))
- Accepted license agreements for required vocabularies

## Essential Vocabularies

For a FHIR to OMOP transformation project, the following vocabularies are essential:

1. **SNOMED CT** - Primary vocabulary for conditions and clinical concepts
2. **LOINC** - For observations and laboratory measurements
3. **RxNorm** - For medications
4. **CPT-4** - For procedures
5. **ICD10CM** - For diagnoses
6. **ATC** - For medication classification

## Download Process

### Step 1: Select Vocabularies

1. Log in to [Athena](https://athena.ohdsi.org/)
2. Click on "Vocabularies" in the top menu
3. Use the filter or search to find each essential vocabulary
4. Check the box next to each vocabulary you want to download
5. For initial setup, select at minimum: SNOMED CT, LOINC, RxNorm, CPT-4, ICD10CM, and ATC

### Step 2: Download Bundle

1. Click on "Download" in the top right corner
2. Review your selected vocabularies
3. Click "Download" to confirm
4. You will receive an email when your download is ready (usually within minutes)
5. Follow the link in the email to download the zip file

### Step 3: Extract Files

Extract the downloaded zip file to the appropriate directory in your project:

```bash
# Create directory for downloads
mkdir -p data/vocabulary/downloads/athena_[content]_[date]

# Extract files
unzip downloaded_file.zip -d data/vocabulary/downloads/athena_[content]_[date]
```

Where:
- `[content]` describes the content (e.g., "snomed", "basic")
- `[date]` is the release date in YYYYMMDD format

## Directory Structure

We use the following directory structure for vocabulary files:

```
data/vocabulary/
├── downloads/                      # Raw downloaded files
│   ├── athena_snomed_20250227/     # SNOMED CT from Athena (2025-02-27)
│   └── athena_basic_20250227/      # Basic vocabularies from Athena (2025-02-27)
└── omop_v5_20250227/               # Combined and processed OMOP v5 vocabularies
```

## Special Note on CPT-4

CPT-4 requires an additional step to reconstitute the descriptions:

1. Register for a UMLS API key (see [UMLS Registration Tutorial](umls_registration.md))
2. Run the CPT-4 reconstitution tool:

```bash
cd data/vocabulary/downloads/athena_basic_20250227/
java -Dumls-apikey=YOUR_UMLS_API_KEY -jar cpt4.jar 5
```

## Next Steps

After downloading and extracting the vocabularies:

1. Reconstitute CPT-4 if included
2. Combine vocabularies for use in the project
3. Update the `VOCABULARY_PATH` in your `.env` file

See [Vocabulary Configuration](../../data/vocabulary/README.md) for details on combining and configuring vocabularies.
