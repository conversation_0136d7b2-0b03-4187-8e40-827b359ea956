# UMLS Registration Tutorial

This tutorial guides you through the process of registering for UMLS Terminology Services to obtain an API key for CPT-4 reconstitution.

## What is UMLS?

The Unified Medical Language System (UMLS) is a set of files and software that brings together many health and biomedical vocabularies and standards. For OMOP vocabulary setup, we need a UMLS API key to reconstitute CPT-4 codes.

## Registration Process

### Step 1: Create a UMLS Account

1. Visit the [UMLS Terminology Services (UTS) website](https://uts.nlm.nih.gov/uts/)
2. Click on "Sign Up" in the top right corner
3. Fill out the registration form:
   - Provide your personal information
   - Select "UMLS Terminology Services" as the application
   - For "Reason for using UTS," mention "OMOP CDM vocabulary setup for healthcare data research"
4. Accept the terms of service
5. Submit your application

### Step 2: License Agreement

1. After creating your account, you'll need to request a UMLS license
2. Log in to your UTS account
3. Click on "Request License" in the dashboard
4. Select the appropriate license category (usually "Individual")
5. Complete the license agreement form
6. Submit your license request

### Step 3: Obtain API Key

1. After your license is approved (usually within 1-2 business days):
2. Log in to your UTS account
3. Navigate to "My Profile" from the dropdown menu in the top right
4. Find the "API Keys" section
5. Click "Generate New API Key" if you don't already have one
6. Copy your API key for use in CPT-4 reconstitution

## Using Your API Key

Your UMLS API key is used to reconstitute CPT-4 descriptions:

```bash
cd data/vocabulary/downloads/athena_basic_20250227/
java -Dumls-apikey=YOUR_UMLS_API_KEY -jar cpt4.jar 5
```

Replace `YOUR_UMLS_API_KEY` with the actual API key you obtained.

## Troubleshooting

- **License Approval Delay**: UMLS license approval can take 1-2 business days. Plan accordingly.
- **API Key Not Working**: Ensure you've accepted all required license agreements in your UMLS account.
- **CPT-4 Reconstitution Errors**: Make sure Java is installed and you're running the command from the directory containing the CPT4.jar file.

## Next Steps

After obtaining your UMLS API key and reconstituting CPT-4:

- Proceed to combining vocabularies as described in the [Vocabulary Configuration](../../data/vocabulary/README.md) guide
