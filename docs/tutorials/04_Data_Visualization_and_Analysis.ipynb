{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Visualization and Analysis with FHIR and OMOP", "", "In this tutorial, we'll explore how to visualize and analyze healthcare data after it has been transformed from FHIR to OMOP. Data visualization is a crucial step in understanding patterns, trends, and insights in healthcare data.", "", "## Learning Objectives", "", "By the end of this tutorial, you will be able to:", "", "1. Create meaningful visualizations of healthcare data from both FHIR and OMOP sources", "2. Analyze patient demographics, conditions, and observations", "3. Visualize temporal patterns in healthcare data", "4. Compare data before and after FHIR to OMOP transformation", "5. Create interactive dashboards for healthcare data exploration", "", "## Prerequisites", "", "- Basic understanding of FHIR and OMOP CDM (covered in previous tutorials)", "- Familiarity with Python programming", "- Basic knowledge of data visualization concepts", "", "Let's get started!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Up the Environment", "", "First, let's import the necessary libraries for data manipulation and visualization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd", "import numpy as np", "import matplotlib.pyplot as plt", "import seaborn as sns", "import plotly.express as px", "import plotly.graph_objects as go", "from datetime import datetime, timedelta", "import json", "import requests", "from IPython.display import display, HTML", "", "# Set the style for matplotlib visualizations", "plt.style.use('ggplot')", "sns.set(style=\"whitegrid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Loading Sample Data", "", "For this tutorial, we'll use sample data that represents both FHIR resources and OMOP tables. Let's start by loading some sample patient data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample FHIR Patient data", "fhir_patients = [", "    {", "        \"resourceType\": \"Patient\",", "        \"id\": \"patient1\",", "        \"gender\": \"male\",", "        \"birthDate\": \"1970-01-15\",", "        \"name\": [{\"family\": \"<PERSON>\", \"given\": [\"<PERSON>\"]}],", "        \"address\": [{\"city\": \"Boston\", \"state\": \"MA\"}]", "    },", "    {", "        \"resourceType\": \"Patient\",", "        \"id\": \"patient2\",", "        \"gender\": \"female\",", "        \"birthDate\": \"1985-05-20\",", "        \"name\": [{\"family\": \"<PERSON>\", \"given\": [\"<PERSON>\"]}],", "        \"address\": [{\"city\": \"New York\", \"state\": \"NY\"}]", "    },", "    {", "        \"resourceType\": \"Patient\",", "        \"id\": \"patient3\",", "        \"gender\": \"male\",", "        \"birthDate\": \"1990-11-03\",", "        \"name\": [{\"family\": \"<PERSON>\", \"given\": [\"<PERSON>\"]}],", "        \"address\": [{\"city\": \"Chicago\", \"state\": \"IL\"}]", "    },", "    {", "        \"resourceType\": \"Patient\",", "        \"id\": \"patient4\",", "        \"gender\": \"female\",", "        \"birthDate\": \"1965-08-12\",", "        \"name\": [{\"family\": \"<PERSON>\", \"given\": [\"<PERSON>\"]}],", "        \"address\": [{\"city\": \"Los Angeles\", \"state\": \"CA\"}]", "    },", "    {", "        \"resourceType\": \"Patient\",", "        \"id\": \"patient5\",", "        \"gender\": \"male\",", "        \"birthDate\": \"1982-03-28\",", "        \"name\": [{\"family\": \"<PERSON>\", \"given\": [\"<PERSON>\"]}],", "        \"address\": [{\"city\": \"Houston\", \"state\": \"TX\"}]", "    }", "]", "", "# Convert FHIR Patient data to a pandas DataFrame", "fhir_patients_df = pd.DataFrame([", "    {", "        \"patient_id\": p[\"id\"],", "        \"gender\": p[\"gender\"],", "        \"birth_date\": p[\"birthDate\"],", "        \"family_name\": p[\"name\"][0][\"family\"],", "        \"given_name\": p[\"name\"][0][\"given\"][0],", "        \"city\": p[\"address\"][0][\"city\"],", "        \"state\": p[\"address\"][0][\"state\"]", "    } for p in fhir_patients", "])", "", "# Calculate age from birth date", "fhir_patients_df[\"age\"] = fhir_patients_df[\"birth_date\"].apply(", "    lambda x: datetime.now().year - datetime.strptime(x, \"%Y-%m-%d\").year", ")", "", "# Display the DataFrame", "print(\"FHIR Patients DataFrame:\")", "display(fhir_patients_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's create a sample OMOP person table that would result from transforming the FHIR patient data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample OMOP Person data", "omop_person_df = pd.DataFrame({", "    \"person_id\": range(1, 6),", "    \"gender_concept_id\": [8507, 8532, 8507, 8532, 8507],  # 8507 for male, 8532 for female", "    \"year_of_birth\": [1970, 1985, 1990, 1965, 1982],", "    \"month_of_birth\": [1, 5, 11, 8, 3],", "    \"day_of_birth\": [15, 20, 3, 12, 28],", "    \"location_id\": [1, 2, 3, 4, 5],", "    \"race_concept_id\": [8527, 8527, 8516, 8527, 8516],  # Example concept IDs", "    \"ethnicity_concept_id\": [38003564, 38003564, 38003564, 38003564, 38003564]", "})", "", "# Add gender name for easier visualization", "omop_person_df[\"gender\"] = omop_person_df[\"gender_concept_id\"].map({8507: \"male\", 8532: \"female\"})", "", "# Calculate age", "omop_person_df[\"age\"] = datetime.now().year - omop_person_df[\"year_of_birth\"]", "", "print(\"OMOP Person DataFrame:\")", "display(omop_person_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Basic Demographic Visualizations", "", "Let's start with some basic demographic visualizations to understand our patient population."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Gender Distribution", "", "First, let's visualize the gender distribution in both FHIR and OMOP data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gender distribution in FHIR data", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))", "", "# FHIR gender distribution", "fhir_gender_counts = fhir_patients_df[\"gender\"].value_counts()", "ax1.pie(fhir_gender_counts, labels=fhir_gender_counts.index, autopct='%1.1f%%', startangle=90)", "ax1.set_title('Gender Distribution (FHIR)')", "", "# OMOP gender distribution", "omop_gender_counts = omop_person_df[\"gender\"].value_counts()", "ax2.pie(omop_gender_counts, labels=omop_gender_counts.index, autopct='%1.1f%%', startangle=90)", "ax2.set_title('Gender Distribution (OMOP)')", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Age Distribution", "", "Now, let's look at the age distribution of our patients."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Age distribution visualization", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))", "", "# FHIR age distribution", "sns.histplot(fhir_patients_df[\"age\"], bins=5, kde=True, ax=ax1)", "ax1.set_title('Age Distribution (FHIR)')", "ax1.set_xlabel('Age')", "ax1.set_ylabel('Count')", "", "# OMOP age distribution", "sns.histplot(omop_person_df[\"age\"], bins=5, kde=True, ax=ax2)", "ax2.set_title('Age Distribution (OMOP)')", "ax2.set_xlabel('Age')", "ax2.set_ylabel('Count')", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Age by Gender", "", "Let's examine the age distribution by gender."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Age by gender visualization", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))", "", "# FHIR age by gender", "sns.boxplot(x=\"gender\", y=\"age\", data=fhir_patients_df, ax=ax1)", "ax1.set_title('Age by Gender (FHIR)')", "ax1.set_xlabel('Gender')", "ax1.set_ylabel('Age')", "", "# OMOP age by gender", "sns.boxplot(x=\"gender\", y=\"age\", data=omop_person_df, ax=ax2)", "ax2.set_title('Age by Gender (OMOP)')", "ax2.set_xlabel('Gender')", "ax2.set_ylabel('Age')", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Clinical Data", "", "Now, let's create some sample clinical data (conditions and observations) and visualize them."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample FHIR Condition data", "fhir_conditions = [", "    {\"patient_id\": \"patient1\", \"code\": \"I10\", \"display\": \"Hypertension\", \"onset_date\": \"2020-03-15\"},", "    {\"patient_id\": \"patient1\", \"code\": \"E11\", \"display\": \"Type 2 Diabetes\", \"onset_date\": \"2019-05-20\"},", "    {\"patient_id\": \"patient2\", \"code\": \"J45\", \"display\": \"Asthma\", \"onset_date\": \"2018-11-10\"},", "    {\"patient_id\": \"patient3\", \"code\": \"I10\", \"display\": \"Hypertension\", \"onset_date\": \"2021-01-05\"},", "    {\"patient_id\": \"patient4\", \"code\": \"M19\", \"display\": \"Osteoarthritis\", \"onset_date\": \"2020-07-22\"},", "    {\"patient_id\": \"patient4\", \"code\": \"E78\", \"display\": \"Hyperlipidemia\", \"onset_date\": \"2019-09-14\"},", "    {\"patient_id\": \"patient5\", \"code\": \"F32\", \"display\": \"Depression\", \"onset_date\": \"2021-02-18\"},", "    {\"patient_id\": \"patient5\", \"code\": \"J45\", \"display\": \"Asthma\", \"onset_date\": \"2020-04-30\"}", "]", "", "fhir_conditions_df = pd.DataFrame(fhir_conditions)", "fhir_conditions_df[\"onset_year\"] = pd.to_datetime(fhir_conditions_df[\"onset_date\"]).dt.year", "", "print(\"FHIR Conditions DataFrame:\")", "display(fhir_conditions_df)", "", "# Sample OMOP condition_occurrence data", "omop_condition_occurrence_df = pd.DataFrame({", "    \"condition_occurrence_id\": range(1, 9),", "    \"person_id\": [1, 1, 2, 3, 4, 4, 5, 5],", "    \"condition_concept_id\": [320128, 201826, 317009, 320128, 80180, 432867, 440383, 317009],", "    \"condition_start_date\": [", "        \"2020-03-15\", \"2019-05-20\", \"2018-11-10\", \"2021-01-05\", ", "        \"2020-07-22\", \"2019-09-14\", \"2021-02-18\", \"2020-04-30\"", "    ],", "    \"condition_type_concept_id\": [32020, 32020, 32020, 32020, 32020, 32020, 32020, 32020],", "    \"condition_source_value\": [\"I10\", \"E11\", \"J45\", \"I10\", \"M19\", \"E78\", \"F32\", \"J45\"]", "})", "", "# Add condition name for easier visualization", "condition_concept_map = {", "    320128: \"Hypertension\",", "    201826: \"Type 2 Diabetes\",", "    317009: \"Asthma\",", "    80180: \"Osteoarthritis\",", "    432867: \"Hyperlipidemia\",", "    440383: \"Depression\"", "}", "omop_condition_occurrence_df[\"condition_name\"] = omop_condition_occurrence_df[\"condition_concept_id\"].map(condition_concept_map)", "omop_condition_occurrence_df[\"condition_start_year\"] = pd.to_datetime(omop_condition_occurrence_df[\"condition_start_date\"]).dt.year", "", "print(\"OMOP Condition Occurrence DataFrame:\")", "display(omop_condition_occurrence_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Condition Distribution", "", "Let's visualize the distribution of conditions in our data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Condition distribution visualization", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))", "", "# FHIR condition distribution", "fhir_condition_counts = fhir_conditions_df[\"display\"].value_counts()", "sns.barplot(x=fhir_condition_counts.index, y=fhir_condition_counts.values, ax=ax1)", "ax1.set_title('Condition Distribution (FHIR)')", "ax1.set_xlabel('Condition')", "ax1.set_ylabel('Count')", "ax1.set_xticklabels(ax1.get_xticklabels(), rotation=45, ha='right')", "", "# OMOP condition distribution", "omop_condition_counts = omop_condition_occurrence_df[\"condition_name\"].value_counts()", "sns.barplot(x=omop_condition_counts.index, y=omop_condition_counts.values, ax=ax2)", "ax2.set_title('Condition Distribution (OMOP)')", "ax2.set_xlabel('Condition')", "ax2.set_ylabel('Count')", "ax2.set_xticklabels(ax2.get_xticklabels(), rotation=45, ha='right')", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Conditions Over Time", "", "Let's visualize how conditions have been diagnosed over time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Conditions over time visualization", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))", "", "# FHIR conditions over time", "fhir_conditions_by_year = fhir_conditions_df.groupby(['onset_year', 'display']).size().unstack().fillna(0)", "fhir_conditions_by_year.plot(kind='bar', stacked=True, ax=ax1)", "ax1.set_title('Conditions by Year (FHIR)')", "ax1.set_xlabel('Year')", "ax1.set_ylabel('Number of Diagnoses')", "ax1.legend(title='Condition')", "", "# OMOP conditions over time", "omop_conditions_by_year = omop_condition_occurrence_df.groupby(['condition_start_year', 'condition_name']).size().unstack().fillna(0)", "omop_conditions_by_year.plot(kind='bar', stacked=True, ax=ax2)", "ax2.set_title('Conditions by Year (OMOP)')", "ax2.set_xlabel('Year')", "ax2.set_ylabel('Number of Diagnoses')", "ax2.legend(title='Condition')", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualizing Observations and Measurements", "", "Now, let's create and visualize some sample observation/measurement data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample FHIR Observation data (focusing on blood pressure and blood glucose)", "fhir_observations = []", "", "# Generate blood pressure observations", "for patient_id in [\"patient1\", \"patient2\", \"patient3\", \"patient4\", \"patient5\"]:", "    # Generate 3 blood pressure readings per patient over different dates", "    for i, month in enumerate([3, 6, 9]):", "        # Systolic BP (normal range: 90-120)", "        systolic_value = np.random.normal(120, 15)", "        # Diastolic BP (normal range: 60-80)", "        diastolic_value = np.random.normal(80, 10)", "        ", "        date = f\"2021-{month:02d}-{np.random.randint(1, 28):02d}\"", "        ", "        fhir_observations.append({", "            \"patient_id\": patient_id,", "            \"code\": \"8480-6\",", "            \"display\": \"Systolic Blood Pressure\",", "            \"value\": systolic_value,", "            \"unit\": \"mmHg\",", "            \"date\": date", "        })", "        ", "        fhir_observations.append({", "            \"patient_id\": patient_id,", "            \"code\": \"8462-4\",", "            \"display\": \"Diastolic Blood Pressure\",", "            \"value\": diastolic_value,", "            \"unit\": \"mmHg\",", "            \"date\": date", "        })", "    ", "    # Generate 3 blood glucose readings per patient", "    for i, month in enumerate([2, 5, 8]):", "        # Blood glucose (normal fasting: 70-100 mg/dL)", "        glucose_value = np.random.normal(100, 20)", "        ", "        date = f\"2021-{month:02d}-{np.random.randint(1, 28):02d}\"", "        ", "        fhir_observations.append({", "            \"patient_id\": patient_id,", "            \"code\": \"2339-0\",", "            \"display\": \"Blood Glucose\",", "            \"value\": glucose_value,", "            \"unit\": \"mg/dL\",", "            \"date\": date", "        })", "", "fhir_observations_df = pd.DataFrame(fhir_observations)", "fhir_observations_df[\"date\"] = pd.to_datetime(fhir_observations_df[\"date\"])", "fhir_observations_df[\"month\"] = fhir_observations_df[\"date\"].dt.month", "fhir_observations_df[\"month_name\"] = fhir_observations_df[\"date\"].dt.strftime('%b')", "", "print(\"FHIR Observations DataFrame (sample):\")", "display(fhir_observations_df.head(10))", "", "# Sample OMOP measurement data", "# Create a mapping from FHIR patient_id to OMOP person_id", "patient_to_person = {", "    \"patient1\": 1,", "    \"patient2\": 2,", "    \"patient3\": 3,", "    \"patient4\": 4,", "    \"patient5\": 5", "}", "", "# Create OMOP measurement data from FHIR observations", "omop_measurement_records = []", "for idx, obs in fhir_observations_df.iterrows():", "    measurement_concept_id = None", "    if obs[\"code\"] == \"8480-6\":", "        measurement_concept_id = 3004249  # Systolic BP", "    elif obs[\"code\"] == \"8462-4\":", "        measurement_concept_id = 3012888  # Diastolic BP", "    elif obs[\"code\"] == \"2339-0\":", "        measurement_concept_id = 3004501  # Blood Glucose", "    ", "    if measurement_concept_id:", "        omop_measurement_records.append({", "            \"measurement_id\": idx + 1,", "            \"person_id\": patient_to_person[obs[\"patient_id\"]],", "            \"measurement_concept_id\": measurement_concept_id,", "            \"measurement_date\": obs[\"date\"],", "            \"measurement_type_concept_id\": 44818701,  # Patient reported", "            \"value_as_number\": obs[\"value\"],", "            \"unit_concept_id\": 8876 if \"mmHg\" in obs[\"unit\"] else 8840,  # mmHg or mg/dL", "            \"measurement_source_value\": obs[\"code\"]", "        })", "", "omop_measurement_df = pd.DataFrame(omop_measurement_records)", "", "# Add measurement name for easier visualization", "measurement_concept_map = {", "    3004249: \"Systolic Blood Pressure\",", "    3012888: \"Diastolic Blood Pressure\",", "    3004501: \"Blood Glucose\"", "}", "omop_measurement_df[\"measurement_name\"] = omop_measurement_df[\"measurement_concept_id\"].map(measurement_concept_map)", "omop_measurement_df[\"month\"] = pd.to_datetime(omop_measurement_df[\"measurement_date\"]).dt.month", "omop_measurement_df[\"month_name\"] = pd.to_datetime(omop_measurement_df[\"measurement_date\"]).dt.strftime('%b')", "", "print(\"OMOP Measurement DataFrame (sample):\")", "display(omop_measurement_df.head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Blood Pressure Trends", "", "Let's visualize blood pressure trends over time."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Blood pressure trends visualization", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))", "", "# FHIR blood pressure trends", "fhir_bp_data = fhir_observations_df[fhir_observations_df[\"display\"].isin([\"Systolic Blood Pressure\", \"Diastolic Blood Pressure\"])]", "fhir_bp_pivot = fhir_bp_data.pivot_table(", "    index=\"month\", ", "    columns=\"display\", ", "    values=\"value\", ", "    aggfunc=\"mean\"", ").reindex(range(1, 13))", "", "fhir_bp_pivot.plot(ax=ax1, marker='o')", "ax1.set_title('Blood Pressure Trends (FHIR)')", "ax1.set_xlabel('Month')", "ax1.set_ylabel('Blood Pressure (mmHg)')", "ax1.set_xticks(range(1, 13))", "ax1.set_xticklabels(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])", "ax1.grid(True)", "", "# OMOP blood pressure trends", "omop_bp_data = omop_measurement_df[omop_measurement_df[\"measurement_name\"].isin([\"Systolic Blood Pressure\", \"Diastolic Blood Pressure\"])]", "omop_bp_pivot = omop_bp_data.pivot_table(", "    index=\"month\", ", "    columns=\"measurement_name\", ", "    values=\"value_as_number\", ", "    aggfunc=\"mean\"", ").reindex(range(1, 13))", "", "omop_bp_pivot.plot(ax=ax2, marker='o')", "ax2.set_title('Blood Pressure Trends (OMOP)')", "ax2.set_xlabel('Month')", "ax2.set_ylabel('Blood Pressure (mmHg)')", "ax2.set_xticks(range(1, 13))", "ax2.set_xticklabels(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'])", "ax2.grid(True)", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Blood Glucose Distribution", "", "Let's visualize the distribution of blood glucose measurements."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Blood glucose distribution visualization", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))", "", "# FHIR blood glucose distribution", "fhir_glucose_data = fhir_observations_df[fhir_observations_df[\"display\"] == \"Blood Glucose\"]", "sns.histplot(fhir_glucose_data[\"value\"], bins=10, kde=True, ax=ax1)", "ax1.axvline(x=70, color='g', linestyle='--', label='Lower Normal (70 mg/dL)')", "ax1.axvline(x=100, color='g', linestyle='--', label='Upper Normal (100 mg/dL)')", "ax1.axvline(x=126, color='r', linestyle='--', label='Diabetes Threshold (126 mg/dL)')", "ax1.set_title('Blood Glucose Distribution (FHIR)')", "ax1.set_xlabel('Blood Glucose (mg/dL)')", "ax1.set_ylabel('Count')", "ax1.legend()", "", "# OMOP blood glucose distribution", "omop_glucose_data = omop_measurement_df[omop_measurement_df[\"measurement_name\"] == \"Blood Glucose\"]", "sns.histplot(omop_glucose_data[\"value_as_number\"], bins=10, kde=True, ax=ax2)", "ax2.axvline(x=70, color='g', linestyle='--', label='Lower Normal (70 mg/dL)')", "ax2.axvline(x=100, color='g', linestyle='--', label='Upper Normal (100 mg/dL)')", "ax2.axvline(x=126, color='r', linestyle='--', label='Diabetes Threshold (126 mg/dL)')", "ax2.set_title('Blood Glucose Distribution (OMOP)')", "ax2.set_xlabel('Blood Glucose (mg/dL)')", "ax2.set_ylabel('Count')", "ax2.legend()", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Blood Glucose by <PERSON><PERSON>", "", "Let's visualize blood glucose measurements for each patient."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Blood glucose by patient visualization", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))", "", "# FHIR blood glucose by patient", "fhir_glucose_by_patient = fhir_glucose_data.pivot_table(", "    index=\"patient_id\", ", "    columns=\"month_name\", ", "    values=\"value\", ", "    aggfunc=\"mean\"", ")", "fhir_glucose_by_patient = fhir_glucose_by_patient.reindex(columns=['Feb', 'May', 'Aug'])", "fhir_glucose_by_patient.plot(kind='bar', ax=ax1)", "ax1.set_title('Blood Glucose by Patient (FHIR)')", "ax1.set_xlabel('Patient ID')", "ax1.set_ylabel('Blood Glucose (mg/dL)')", "ax1.axhline(y=100, color='g', linestyle='--', label='Upper Normal (100 mg/dL)')", "ax1.axhline(y=126, color='r', linestyle='--', label='Diabetes Threshold (126 mg/dL)')", "ax1.legend()", "", "# OMOP blood glucose by patient", "omop_glucose_by_patient = omop_glucose_data.pivot_table(", "    index=\"person_id\", ", "    columns=\"month_name\", ", "    values=\"value_as_number\", ", "    aggfunc=\"mean\"", ")", "omop_glucose_by_patient = omop_glucose_by_patient.reindex(columns=['Feb', 'May', 'Aug'])", "omop_glucose_by_patient.plot(kind='bar', ax=ax2)", "ax2.set_title('Blood Glucose by <PERSON><PERSON> (OMOP)')", "ax2.set_xlabel('Person ID')", "ax2.set_ylabel('Blood Glucose (mg/dL)')", "ax2.axhline(y=100, color='g', linestyle='--', label='Upper Normal (100 mg/dL)')", "ax2.axhline(y=126, color='r', linestyle='--', label='Diabetes Threshold (126 mg/dL)')", "ax2.legend()", "", "plt.tight_layout()", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Visualizations with <PERSON><PERSON><PERSON>", "", "Now, let's create some interactive visualizations using Plotly."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interactive Patient Demographics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive patient demographics visualization", "# Age distribution by gender", "fig = px.histogram(", "    fhir_patients_df, ", "    x=\"age\", ", "    color=\"gender\", ", "    marginal=\"box\", ", "    hover_data=fhir_patients_df.columns,", "    title=\"Age Distribution by Gender (FHIR)\",", "    labels={\"age\": \"Age\", \"gender\": \"Gender\"},", "    color_discrete_map={\"male\": \"blue\", \"female\": \"pink\"}", ")", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interactive Condition Timeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive condition timeline", "# Prepare data for timeline", "timeline_data = []", "for _, row in fhir_conditions_df.iterrows():", "    patient_name = fhir_patients_df[fhir_patients_df[\"patient_id\"] == row[\"patient_id\"]][\"given_name\"].values[0]", "    timeline_data.append({", "        \"Patient\": f\"{patient_name} ({row['patient_id']})\",", "        \"Condition\": row[\"display\"],", "        \"Start Date\": row[\"onset_date\"],", "        \"Code\": row[\"code\"]", "    })", "", "timeline_df = pd.DataFrame(timeline_data)", "timeline_df[\"Start Date\"] = pd.to_datetime(timeline_df[\"Start Date\"])", "timeline_df = timeline_df.sort_values([\"Patient\", \"Start Date\"])", "", "fig = px.timeline(", "    timeline_df, ", "    x_start=\"Start Date\", ", "    x_end=\"Start Date\", ", "    y=\"Patient\", ", "    color=\"Condition\",", "    hover_data=[\"Code\"],", "    title=\"Condition Timeline by Patient\",", "    labels={\"Patient\": \"Patient\", \"Condition\": \"Condition\"}", ")", "fig.update_yaxes(autorange=\"reversed\")", "fig.update_layout(xaxis_title=\"Date\")", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Interactive Blood Pressure Dashboard"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive blood pressure dashboard", "# Prepare data for dashboard", "bp_data = fhir_observations_df[fhir_observations_df[\"display\"].isin([\"Systolic Blood Pressure\", \"Diastolic Blood Pressure\"])]", "bp_data = bp_data.merge(fhir_patients_df[[\"patient_id\", \"given_name\", \"family_name\"]], on=\"patient_id\")", "bp_data[\"Patient Name\"] = bp_data[\"given_name\"] + \" \" + bp_data[\"family_name\"]", "", "# Create figure with subplots", "fig = go.Figure()", "", "# Add traces for each patient", "for patient_id in bp_data[\"patient_id\"].unique():", "    patient_data = bp_data[bp_data[\"patient_id\"] == patient_id]", "    patient_name = patient_data[\"Patient Name\"].iloc[0]", "    ", "    systolic_data = patient_data[patient_data[\"display\"] == \"Systolic Blood Pressure\"]", "    diastolic_data = patient_data[patient_data[\"display\"] == \"Diastolic Blood Pressure\"]", "    ", "    fig.add_trace(go.<PERSON>(", "        x=systolic_data[\"date\"],", "        y=systolic_data[\"value\"],", "        mode='lines+markers',", "        name=f\"{patient_name} - Systolic\",", "        line=dict(dash='solid')", "    ))", "    ", "    fig.add_trace(go.<PERSON>(", "        x=diastolic_data[\"date\"],", "        y=diastolic_data[\"value\"],", "        mode='lines+markers',", "        name=f\"{patient_name} - Diast<PERSON>\",", "        line=dict(dash='dot')", "    ))", "", "# Add reference lines", "fig.add_shape(", "    type=\"line\",", "    x0=bp_data[\"date\"].min(),", "    y0=120,", "    x1=bp_data[\"date\"].max(),", "    y1=120,", "    line=dict(color=\"red\", width=2, dash=\"dash\"),", "    name=\"Systolic Threshold\"", ")", "", "fig.add_shape(", "    type=\"line\",", "    x0=bp_data[\"date\"].min(),", "    y0=80,", "    x1=bp_data[\"date\"].max(),", "    y1=80,", "    line=dict(color=\"orange\", width=2, dash=\"dash\"),", "    name=\"Diastolic Threshold\"", ")", "", "# Update layout", "fig.update_layout(", "    title=\"Blood Pressure Trends by Patient\",", "    xaxis_title=\"Date\",", "    yaxis_title=\"Blood Pressure (mmHg)\",", "    legend_title=\"Patient - Measurement\",", "    hovermode=\"closest\"", ")", "", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Comparing FHIR and OMOP Data", "", "Let's create visualizations that directly compare data before and after transformation from FHIR to OMOP."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comparing condition counts between FHIR and OMOP", "fhir_condition_counts = fhir_conditions_df[\"display\"].value_counts().reset_index()", "fhir_condition_counts.columns = [\"Condition\", \"Count\"]", "fhir_condition_counts[\"Source\"] = \"FHIR\"", "", "omop_condition_counts = omop_condition_occurrence_df[\"condition_name\"].value_counts().reset_index()", "omop_condition_counts.columns = [\"Condition\", \"Count\"]", "omop_condition_counts[\"Source\"] = \"OMOP\"", "", "combined_condition_counts = pd.concat([fhir_condition_counts, omop_condition_counts])", "", "fig = px.bar(", "    combined_condition_counts,", "    x=\"Condition\",", "    y=\"Count\",", "    color=\"Source\",", "    barmode=\"group\",", "    title=\"Condition Counts: FHIR vs OMOP\",", "    labels={\"Condition\": \"Condition\", \"Count\": \"Number of Records\", \"Source\": \"Data Source\"}", ")", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advanced Analysis: Comorbidity Network", "", "Let's create a network visualization to show comorbidities (conditions that occur together in patients)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create comorbidity network", "# First, identify which conditions occur in the same patients", "comorbidity_matrix = pd.crosstab(", "    fhir_conditions_df[\"patient_id\"],", "    fhir_conditions_df[\"display\"]", ")", "", "# Calculate co-occurrence (how many patients have both conditions)", "condition_co_occurrence = {}", "conditions = comorbidity_matrix.columns", "for i, cond1 in enumerate(conditions):", "    for j, cond2 in enumerate(conditions):", "        if i < j:  # Avoid duplicates and self-comparisons", "            # Count patients who have both conditions", "            both_conditions = (comorbidity_matrix[cond1] > 0) & (comorbidity_matrix[cond2] > 0)", "            count = both_conditions.sum()", "            if count > 0:", "                condition_co_occurrence[(cond1, cond2)] = count", "", "# Create network data", "network_nodes = []", "for condition in conditions:", "    # Count patients with this condition", "    count = (comorbidity_matrix[condition] > 0).sum()", "    network_nodes.append({", "        \"id\": condition,", "        \"label\": condition,", "        \"size\": count * 10  # Scale node size based on prevalence", "    })", "", "network_edges = []", "for (cond1, cond2), count in condition_co_occurrence.items():", "    network_edges.append({", "        \"source\": cond1,", "        \"target\": cond2,", "        \"value\": count,", "        \"width\": count * 2  # Scale edge width based on co-occurrence", "    })", "", "# Convert to DataFrames for Plotly", "nodes_df = pd.DataFrame(network_nodes)", "edges_df = pd.DataFrame(network_edges)", "", "# Create network visualization", "fig = go.Figure()", "", "# Add edges (links)", "for _, edge in edges_df.iterrows():", "    fig.add_trace(", "        <PERSON><PERSON>(", "            x=[nodes_df[nodes_df[\"id\"] == edge[\"source\"]][\"id\"].index[0], ", "               nodes_df[nodes_df[\"id\"] == edge[\"target\"]][\"id\"].index[0]],", "            y=[0, 0],", "            mode=\"lines\",", "            line=dict(width=edge[\"width\"], color=\"rgba(150,150,150,0.5)\"),", "            hoverinfo=\"text\",", "            text=f\"{edge['source']} - {edge['target']}: {edge['value']} patients\",", "            showlegend=False", "        )", "    )", "", "# Add nodes", "for i, node in nodes_df.iterrows():", "    fig.add_trace(", "        <PERSON><PERSON>(", "            x=[i],", "            y=[0],", "            mode=\"markers+text\",", "            marker=dict(size=node[\"size\"], color=\"skyblue\"),", "            text=node[\"label\"],", "            textposition=\"top center\",", "            hoverinfo=\"text\",", "            hovertext=f\"{node['label']}: {node['size']/10} patients\",", "            showlegend=False", "        )", "    )", "", "# Update layout", "fig.update_layout(", "    title=\"Condition Comorbidity Network\",", "    xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),", "    yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),", "    plot_bgcolor=\"white\"", ")", "", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exercises", "", "Now it's your turn to practice creating visualizations with healthcare data. Here are some exercises to try:", "", "1. **Basic Exercise**: Create a pie chart showing the distribution of patients by state using the FHIR patient data.", "", "2. **Intermediate Exercise**: Create a line chart showing the trend of blood glucose measurements over time for a specific patient.", "", "3. **Advanced Exercise**: Create a heatmap showing the correlation between different measurements (systolic BP, diastolic BP, and blood glucose) across all patients.", "", "### Exercise 1 Solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exercise 1: Pie chart of patients by state", "state_counts = fhir_patients_df[\"state\"].value_counts()", "", "fig = px.pie(", "    values=state_counts.values,", "    names=state_counts.index,", "    title=\"Distribution of Patients by State\",", "    labels={\"names\": \"State\", \"values\": \"Number of Patients\"}", ")", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise 2 Solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exercise 2: Blood glucose trend for a specific patient", "patient_id = \"patient1\"  # Choose a specific patient", "patient_glucose = fhir_observations_df[", "    (fhir_observations_df[\"patient_id\"] == patient_id) & ", "    (fhir_observations_df[\"display\"] == \"Blood Glucose\")", "]", "", "fig = px.line(", "    patient_glucose,", "    x=\"date\",", "    y=\"value\",", "    markers=True,", "    title=f\"Blood Glucose Trend for {patient_id}\",", "    labels={\"date\": \"Date\", \"value\": \"Blood Glucose (mg/dL)\"}", ")", "", "# Add reference lines", "fig.add_shape(", "    type=\"line\",", "    x0=patient_glucose[\"date\"].min(),", "    y0=100,", "    x1=patient_glucose[\"date\"].max(),", "    y1=100,", "    line=dict(color=\"green\", width=2, dash=\"dash\")", ")", "", "fig.add_shape(", "    type=\"line\",", "    x0=patient_glucose[\"date\"].min(),", "    y0=126,", "    x1=patient_glucose[\"date\"].max(),", "    y1=126,", "    line=dict(color=\"red\", width=2, dash=\"dash\")", ")", "", "fig.add_annotation(", "    x=patient_glucose[\"date\"].min(),", "    y=100,", "    text=\"Normal\",", "    showarrow=False,", "    yshift=10", ")", "", "fig.add_annotation(", "    x=patient_glucose[\"date\"].min(),", "    y=126,", "    text=\"Diabetes Threshold\",", "    showarrow=False,", "    yshift=10", ")", "", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise 3 Solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Exercise 3: Correlation heatmap between different measurements", "# First, pivot the data to get measurements by patient", "measurements_pivot = pd.pivot_table(", "    fhir_observations_df,", "    values=\"value\",", "    index=\"patient_id\",", "    columns=\"display\",", "    aggfunc=\"mean\"", ")", "", "# Calculate correlation matrix", "correlation_matrix = measurements_pivot.corr()", "", "# Create heatmap", "fig = px.imshow(", "    correlation_matrix,", "    text_auto=True,", "    color_continuous_scale=\"RdBu_r\",", "    title=\"Correlation Between Different Measurements\",", "    labels={\"x\": \"Measurement\", \"y\": \"Measurement\", \"color\": \"Correlation\"}", ")", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion", "", "In this tutorial, we've explored various techniques for visualizing and analyzing healthcare data from both FHIR and OMOP sources. We've covered:", "", "1. Basic demographic visualizations", "2. Clinical data visualization (conditions and observations)", "3. Interactive dashboards with <PERSON>lotly", "4. Comparative analysis between FHIR and OMOP data", "5. Advanced network analysis for comorbidities", "", "These visualization techniques can help you gain insights from healthcare data and communicate findings effectively. As you continue to work with FHIR and OMOP data, you can adapt and extend these techniques to suit your specific analysis needs.", "", "## Next Steps", "", "- Explore more advanced visualization techniques such as geospatial analysis", "- Integrate statistical analysis with visualizations", "- Build interactive dashboards for real-time monitoring", "- Apply machine learning techniques to predict health outcomes", "", "## References", "", "1. FHIR Documentation: https://www.hl7.org/fhir/", "2. OMOP CDM Documentation: https://ohdsi.github.io/CommonDataModel/", "3. Matplotlib Documentation: https://matplotlib.org/", "4. Seaborn Documentation: https://seaborn.pydata.org/", "5. Plotly Documentation: https://plotly.com/python/"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}