# FHIR to OMOP Tutorials

This directory contains tutorials and learning materials for understanding FHIR, OMOP CDM, and the transformation process between them.

## Learning Path

The recommended learning path is outlined in the [Learning Path](learning_path.md) document. This provides a structured approach to learning the concepts and implementation details.

## Jupyter Notebooks

The following Jupyter notebooks provide interactive tutorials:

1. [Introduction to FHIR](01_Introduction_to_FHIR.ipynb) - Learn the basics of FHIR standard
2. [Introduction to OMOP CDM](02_Introduction_to_OMOP_CDM.ipynb) - Learn the basics of OMOP Common Data Model
3. [Mapping FHIR to OMOP](03_Mapping_FHIR_to_OMOP.ipynb) - Learn how to map FHIR resources to OMOP tables
4. [Data Visualization and Analysis](04_Data_Visualization_and_Analysis.ipynb) - Visualize and analyze transformed data

## Setup Tutorials

The following tutorials guide you through the setup process:

- [Athena Registration](athena_registration.md) - How to register on OHDSI Athena
- [Vocabulary Download](vocabulary_download.md) - How to download OMOP vocabularies
- [UMLS Registration](umls_registration.md) - How to register for UMLS API access

## Tutorial Plan

For educators and developers looking to extend these tutorials, the [Tutorial Plan](tutorial_plan.md) document provides a detailed outline of the learning objectives and content structure.

## References

All tutorials and code examples include references to original sources:

- [OHDSI Documentation](https://ohdsi.github.io/TheBookOfOhdsi/)
- [HL7 FHIR Documentation](https://hl7.org/fhir/)
- [Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
