// ObservationParser.java from NACHC-CAD/fhir-to-omop repository
// Source: https://github.com/NACHC-CAD/fhir-to-omop/blob/main/src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/observation/ObservationParser.java

package org.nachc.tools.fhirtoomop.fhir.parser.observation;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.hl7.fhir.dstu3.model.Coding;
import org.hl7.fhir.dstu3.model.Observation;
import org.hl7.fhir.dstu3.model.Observation.ObservationComponentComponent;
import org.hl7.fhir.dstu3.model.Quantity;
import org.nachc.tools.fhirtoomop.fhir.parser.encounter.EncounterParser;
import org.nachc.tools.fhirtoomop.fhir.parser.observation.component.ObservationComponentParser;
import org.nachc.tools.fhirtoomop.fhir.parser.observation.type.ObservationType;
import org.nachc.tools.fhirtoomop.fhir.patient.FhirPatient;
import org.nachc.tools.fhirtoomop.fhir.util.id.FhirUtil;

public class ObservationParser {

    // instance variables
    //
    
    private Observation obs;
    
    private FhirPatient patient;
    
    // constructor
    //
    
    public ObservationParser(Observation obs, FhirPatient patient) {
        this.obs = obs;
        this.patient = patient;
    }
    
    /**
     * 
     * A single FHIR observation can represent multiple measurements.
     * 
     */
    public boolean isMultipart() {
        if (this.getComponents().size() > 0) {
            return true;
        } else {
            return false;
        }
    }
    
    // component
    //
    
    public List<ObservationComponentParser> getComponents() {
        try {
            ArrayList<ObservationComponentParser> rtn = new ArrayList<ObservationComponentParser>();
            List<ObservationComponentComponent> componentList = this.obs.getComponent();
            for (ObservationComponentComponent comp : componentList) {
                ObservationComponentParser parser = new ObservationComponentParser(comp, this);
                rtn.add(parser);
            }
            return rtn;
        } catch (Exception exp) {
            return new ArrayList<ObservationComponentParser>();
        }
    }
    
    // getters
    //
    
    public String getId() {
        try {
            String rtn = this.obs.getIdElement().getIdPart();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public String getPatientId() {
        try {
            String rtn = this.obs.getSubject().getReference();
            rtn = FhirUtil.getIdFromReference(rtn);
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public String getEncounterId() {
        try {
            String rtn = this.obs.getContext().getReference();
            rtn = FhirUtil.getIdFromReference(rtn);
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public Date getEffectiveDate() {
        try {
            Date rtn = this.obs.getEffectiveDateTimeType().getValue();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public ObservationType getObservationType() {
        try {
            ObservationType rtn = new ObservationType(this.obs.getCode());
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public BigDecimal getValueQuantity() {
        try {
            Quantity quantity = this.obs.getValueQuantity();
            BigDecimal rtn = quantity.getValue();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public String getValueString() {
        try {
            String rtn = this.obs.getValueStringType().getValue();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public Coding getValueCoding() {
        try {
            Coding rtn = this.obs.getValueCodeableConcept().getCodingFirstRep();
            return rtn;
        } catch (Exception exp) {
            return null;
        }
    }
    
    public Observation getObservation() {
        return this.obs;
    }
    
    public FhirPatient getPatient() {
        return this.patient;
    }
    
}
