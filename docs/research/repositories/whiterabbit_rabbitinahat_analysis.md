# WhiteRabbit and Rabbit-in-a-Hat Analysis

## Overview
WhiteRabbit and Rabbit-in-a-Hat are tools developed by the OHDSI community to assist in the ETL (Extract, Transform, Load) process for converting healthcare data to the OMOP Common Data Model (CDM). While not specifically designed for FHIR-to-OMOP transformation, these tools provide valuable functionality for analyzing source data structures and designing ETL specifications.

## WhiteRabbit
### Repository
https://github.com/OHDSI/WhiteRabbit

### Purpose
WhiteRabbit is a small application used to analyze the structure and contents of a database as preparation for designing an ETL. It scans source databases and generates detailed reports about tables, fields, and value distributions.

### Key Features
- Can scan databases in SQL Server, Oracle, PostgreSQL, MySQL, MS Access, Amazon RedShift, Google BigQuery, SAS files, and CSV files
- Generates scan reports containing information on tables, fields, and frequency distributions of values
- Includes privacy protection with cutoff on the minimum frequency of values
- Can be run with a graphical user interface or from the command prompt
- Pure Java application with JDBC connections to databases

### Relevance to FHIR-to-OMOP
While WhiteRabbit doesn't have specific FHIR connectors, it could potentially be used to:
- Analyze FHIR data stored in relational databases
- Scan CSV exports of FHIR resources
- Understand the structure and content of FHIR data before mapping to OMOP

## Rabbit-in-a-Hat

### Purpose
Rabbit-in-a-Hat is an interactive tool for designing ETL specifications using the scan report generated by WhiteRabbit. It provides a graphical interface to map source data to OMOP CDM tables and fields.

### Key Features
- Reads WhiteRabbit scan documents to display source data structure
- Provides a graphical user interface for connecting source data to OMOP CDM tables and columns
- Supports multiple CDM versions (v4, v5, or v6)
- Allows loading custom CDM schemas
- Includes a "stem table" concept for mapping source domains to multiple OMOP CDM target domains
- Provides concept ID hints for fields with limited standard concept options
- Generates ETL documentation in Word, Markdown, or HTML formats
- Supports detailed mapping at both table and field levels
- Allows documentation of transformation logic and comments

### Process Overview
1. Scanned results from WhiteRabbit are loaded
2. Interface displays source tables and CDM tables
3. User connects source tables to CDM tables where the source table provides information
4. For each table connection, user defines field-level mappings between source columns and CDM columns
5. User documents transformation logic and special considerations
6. ETL specification document is generated

### Relevance to FHIR-to-OMOP
For FHIR-to-OMOP transformation, Rabbit-in-a-Hat could be used to:
- Design and document the mapping between FHIR resources and OMOP tables
- Visualize the relationships between FHIR elements and OMOP fields
- Generate comprehensive ETL documentation for implementation teams
- Provide a structured approach to the complex mapping process

## Limitations for FHIR-to-OMOP Use Case
- No direct FHIR API connectivity in WhiteRabbit
- Would require FHIR data to be in a relational database or CSV format for scanning
- Not specifically designed for hierarchical data structures like FHIR
- Generates documentation but not actual transformation code

## Potential Integration in FHIR-to-OMOP Pipeline
1. **Analysis Phase**: Use WhiteRabbit to scan FHIR data stored in relational format
2. **Design Phase**: Use Rabbit-in-a-Hat to create detailed mapping specifications
3. **Documentation Phase**: Generate comprehensive ETL documentation
4. **Implementation Phase**: Developers would use the specifications to write actual transformation code

## References
- [OHDSI WhiteRabbit GitHub Repository](https://github.com/OHDSI/WhiteRabbit)
- [Rabbit-in-a-Hat Documentation](https://ohdsi.github.io/WhiteRabbit/RabbitInAHat.html)
- [OHDSI Forums - OMOP and FHIR resources mapping](https://forums.ohdsi.org/t/omop-and-fhir-resources-mapping/16809)
