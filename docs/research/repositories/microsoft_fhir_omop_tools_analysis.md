# Microsoft FHIR and OMOP Tools Analysis

## Overview
Microsoft offers several tools and services related to FHIR and OMOP in their healthcare data solutions ecosystem. Unlike the previously examined repositories (NACHC-CAD/fhir-to-omop and OHDSI/ETL-German-FHIR-Core), Microsoft's approach is more service-oriented rather than providing standalone open-source transformation code.

## Microsoft OMOP Transformations Service

Microsoft provides OMOP transformations as a capability within their healthcare data solutions in Microsoft Fabric. This service:

- Transforms flattened FHIR data and FHIR DocumentReference (unstructured clinical notes) to OMOP CDM
- Stores OMOP data in open delta format, maintaining update history and enabling time travel
- Transforms reference data from FHIR code systems, codes, and codeable concepts to OMOP concepts using OMOP vocabulary tables
- Supports OMOP CDM version 5.4

### FHIR to OMOP Mapping
Microsoft implements mappings based on HL7 International guidance:

| FHIR Domain Resource | OMOP Table | Notes |
| --- | --- | --- |
| Patient | PERSON | |
| Organization | CARE_SITE | |
| Condition | CONDITION_OCCURRENCE | |
| Patient | DEATH | If `patient.deceased` is populated |
| Procedure | DEVICE_EXPOSURE | If `procedure.focaldevice` isn't null |
| MedicationRequest | DRUG_EXPOSURE | |
| Address | LOCATION | `patient.address` and `organization.address` |
| Observation | MEASUREMENT | If `observation.category` is laboratory |
| DocumentReference | NOTE | |
| DocumentReference | NOTE_NLP | The Text Analytics for health output from documentreference unstructured note |
| Observation | OBSERVATION | If `observation.category` isn't laboratory |
| Procedure | PROCEDURE_OCCURRENCE | If `procedure.focaldevice` is null |
| Practitioner | PROVIDER | |
| Encounter | VISIT_OCCURRENCE | |

## Microsoft FHIR-Converter

Microsoft also offers the FHIR-Converter, an open-source project that enables conversion of health data from legacy formats to and from FHIR. While not specifically targeting OMOP, it's a relevant tool in the FHIR ecosystem:

- Uses Liquid template language and .NET runtime
- Supports conversions: HL7v2 to FHIR, C-CDA to FHIR, JSON to FHIR, FHIR STU3 to R4, and FHIR to HL7v2 (Preview)
- Provides containerized API
- Supports Azure Storage for custom templates
- Offers pre-built templates that can be customized

## Integration Approach

For organizations using Microsoft's cloud ecosystem, the recommended approach for FHIR to OMOP transformation would be:

1. Use Azure Health Data Services to ingest and store FHIR data
2. Implement the OMOP transformations capability in Microsoft Fabric
3. Query and analyze the resulting OMOP data using SQL endpoints

## Limitations and Considerations

- Microsoft's OMOP transformation tools are tightly integrated with their cloud services
- Not available as standalone open-source code that can be deployed independently
- Requires Microsoft Fabric and potentially other Microsoft services
- Limited customization compared to fully open-source solutions

## Relevance to FHIR-to-OMOP Pipeline Development

While Microsoft's tools don't provide directly reusable code for a custom FHIR-to-OMOP pipeline, they offer:

1. A validated mapping approach based on HL7 International guidance
2. A reference architecture for implementing FHIR-to-OMOP transformations
3. Insights into handling specific FHIR resource types and their mapping to OMOP tables

## References

- [Microsoft Cloud for Healthcare - OMOP Transformations](https://learn.microsoft.com/en-us/industry/healthcare/healthcare-data-solutions/omop-transformations)
- [Microsoft FHIR-Converter GitHub Repository](https://github.com/microsoft/FHIR-Converter)
