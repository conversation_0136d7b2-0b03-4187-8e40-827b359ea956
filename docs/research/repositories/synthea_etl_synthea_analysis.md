# Synthea and ETL-Synthea Analysis

## Overview
Synthea and ETL-Synthea are complementary tools that together provide a complete pipeline for generating synthetic patient data and converting it to the OMOP Common Data Model (CDM). This analysis covers both tools and their relevance to FHIR-to-OMOP transformation.

## Synthea

### Repository
[synthetichealth/synthea](https://github.com/synthetichealth/synthea)

### Description
Synthea is a Synthetic Patient Population Simulator that generates realistic (but not real) patient data and associated health records in various formats, including FHIR. It's a Java-based application that simulates the medical history of synthetic patients from birth to death.

### Key Features
- **Birth to Death Lifecycle**: Generates complete patient histories
- **Configuration-based statistics and demographics**: Defaults with Massachusetts Census data
- **Modular Rule System**: Generic modules and custom Java rules
- **Multiple Output Formats**:
  - HL7 FHIR (R4, STU3 v3.0.1, and DSTU2 v1.0.2)
  - Bulk FHIR in ndjson format
  - C-CDA
  - CSV
  - CPCDS
- **Comprehensive Clinical Data**: Includes conditions, allergies, medications, vaccinations, observations/vitals, labs, procedures, and care plans
- **Encounter Types**: Primary care, emergency room, and symptom-driven encounters

### Usage for FHIR Data Generation
Synthea can be configured to generate FHIR data by setting appropriate properties in the configuration file:
```
# Enable FHIR export
exporter.fhir.export = true

# Specify FHIR version (R4, STU3, DSTU2)
exporter.fhir.transaction_bundle = true

# Enable bulk FHIR in ndjson format
exporter.fhir.bulk_data = true
```

## ETL-Synthea

### Repository
[OHDSI/ETL-Synthea](https://github.com/OHDSI/ETL-Synthea)

### Description
ETL-Synthea is an R package that supports the conversion of Synthea CSV data to the OMOP Common Data Model. It provides a complete ETL pipeline for transforming synthetic patient data into the OMOP CDM format.

### Key Features
- Supports OMOP CDM versions 5.3 and 5.4
- Compatible with multiple Synthea versions (2.7.0, 3.0.0, 3.1.0, 3.2.0, 3.3.0)
- Provides functions for:
  - Creating CDM tables
  - Creating Synthea tables
  - Loading Synthea data from CSV files
  - Loading vocabulary data
  - Creating mapping and rollup tables
  - Creating extra indices for performance
  - Loading event tables

### ETL Process
The ETL-Synthea package follows this general process:
1. Create CDM tables in the target database
2. Create Synthea tables in the source schema
3. Load Synthea CSV data into the source tables
4. Load vocabulary data into the CDM schema
5. Create mapping and rollup tables
6. Create additional indices for performance
7. Transform and load event data into the CDM tables

### Example Code
```R
# Create connection to database
cd <- DatabaseConnector::createConnectionDetails(
  dbms = "postgresql",
  server = "localhost/synthea10",
  user = "postgres",
  password = "password",
  port = 5432,
  pathToDriver = "d:/drivers"
)

# Define schemas and locations
cdmSchema <- "cdm_synthea10"
cdmVersion <- "5.4"
syntheaVersion <- "2.7.0"
syntheaSchema <- "native"
syntheaFileLoc <- "/tmp/synthea/output/csv"
vocabFileLoc <- "/tmp/Vocabulary_20181119"

# Execute ETL process
ETLSyntheaBuilder::CreateCDMTables(connectionDetails = cd, cdmSchema = cdmSchema, cdmVersion = cdmVersion)
ETLSyntheaBuilder::CreateSyntheaTables(connectionDetails = cd, syntheaSchema = syntheaSchema, syntheaVersion = syntheaVersion)
ETLSyntheaBuilder::LoadSyntheaTables(connectionDetails = cd, syntheaSchema = syntheaSchema, syntheaFileLoc = syntheaFileLoc)
ETLSyntheaBuilder::LoadVocabFromCsv(connectionDetails = cd, cdmSchema = cdmSchema, vocabFileLoc = vocabFileLoc)
ETLSyntheaBuilder::CreateMapAndRollupTables(connectionDetails = cd, cdmSchema = cdmSchema, syntheaSchema = syntheaSchema, cdmVersion = cdmVersion, syntheaVersion = syntheaVersion)
ETLSyntheaBuilder::CreateExtraIndices(connectionDetails = cd, cdmSchema = cdmSchema, syntheaSchema = syntheaSchema, syntheaVersion = syntheaVersion)
ETLSyntheaBuilder::LoadEventTables(connectionDetails = cd, cdmSchema = cdmSchema, syntheaSchema = syntheaSchema, cdmVersion = cdmVersion, syntheaVersion = syntheaVersion)
```

## Combined FHIR-to-OMOP Workflow Using Synthea and ETL-Synthea

While ETL-Synthea is designed to work with Synthea's CSV output format rather than FHIR, the two tools can be used together to create a complete pipeline for testing FHIR-to-OMOP transformations:

1. **Generate Synthetic Data in FHIR Format**:
   - Use Synthea to generate synthetic patient data in FHIR format
   - Configure Synthea to output both FHIR and CSV formats simultaneously

2. **Direct CSV-to-OMOP Conversion**:
   - Use ETL-Synthea to convert the CSV output directly to OMOP CDM
   - This provides a "ground truth" OMOP dataset derived from the same source data

3. **FHIR-to-OMOP Conversion**:
   - Apply your custom FHIR-to-OMOP transformation pipeline to the FHIR output
   - Compare the results with the "ground truth" OMOP dataset from ETL-Synthea

4. **Validation and Testing**:
   - Validate the FHIR-to-OMOP transformation by comparing with the ETL-Synthea results
   - Identify and fix discrepancies in the FHIR-to-OMOP mapping

## Relevance to FHIR-to-OMOP Pipeline Development

The combination of Synthea and ETL-Synthea provides several benefits for FHIR-to-OMOP pipeline development:

1. **Synthetic Test Data**: Generate realistic test data without privacy concerns
2. **Ground Truth**: Create a reference OMOP dataset for validation
3. **Controlled Environment**: Test with known data patterns and edge cases
4. **Scalability Testing**: Generate datasets of various sizes to test performance
5. **Mapping Validation**: Compare FHIR-to-OMOP mappings with established ETL-Synthea mappings

## Limitations

1. **Indirect FHIR-to-OMOP Conversion**: ETL-Synthea works with CSV, not directly with FHIR
2. **Limited to Synthetic Data**: May not capture all real-world data complexities
3. **R-based Implementation**: Requires R knowledge to customize or extend
4. **Fixed Mapping Logic**: ETL-Synthea has predefined mappings that may differ from custom FHIR-to-OMOP mappings

## References

- [synthetichealth/synthea GitHub Repository](https://github.com/synthetichealth/synthea)
- [OHDSI/ETL-Synthea GitHub Repository](https://github.com/OHDSI/ETL-Synthea)
- [Synthea Wiki](https://github.com/synthetichealth/synthea/wiki)
- [OHDSI ETL-Synthea Documentation](https://ohdsi.github.io/ETL-Synthea/)
