# NACHC-CAD/fhir-to-omop Repository Analysis

## Overview
The NACHC-CAD/fhir-to-omop repository is a Java-based solution for transforming FHIR data to OMOP CDM. It uses a file-based approach where FHIR resources are downloaded from a FHIR Patient Server and then transformed to write the data to an OMOP CDM database instance.

## Repository URL
https://github.com/NACHC-CAD/fhir-to-omop

## Repository Structure
The repository is organized with the following key components:

- **src/main/java/org/nachc/tools/fhirtoomop/fhir/parser/**: Contains parsers for different FHIR resources
  - **patient/**: Patient resource parser
  - **observation/**: Observation resource parser
  - **condition/**: Condition resource parser
  - And other FHIR resource parsers (encounter, medicationrequest, etc.)

## Architecture
Based on the documentation and code examination, the tool follows a pipeline approach:
1. Create an OMOP CDM Database
2. Get the IDs of the Patients on the FHIR Server
3. Download Patients from the FHIR Server
4. Upload FHIR Patients into the OMOP CDM Database

The tool can be used as individual components or as a single downloadable standalone application.

## Key FHIR Resource Parsers

### PatientParser
- Handles parsing of FHIR Patient resources
- Extracts patient demographics including:
  - ID
  - First and last name
  - Race and ethnicity (from extensions)
  - Gender
  - Birth date
- Maps these to corresponding OMOP Person table fields

### ObservationParser
- Handles parsing of FHIR Observation resources
- Supports both single and multi-part observations
- Extracts:
  - Observation ID
  - Patient ID
  - Encounter ID
  - Effective date
  - Observation type
  - Quantitative values
  - String values
  - Coded values
- Maps to OMOP Measurement or Observation tables depending on the observation type

### ConditionParser
- Handles parsing of FHIR Condition resources
- Extracts:
  - Condition ID
  - Coding (diagnosis code)
  - Patient ID
  - Encounter ID
  - Onset and abatement dates
  - Clinical status
  - Verification status
- Maps to OMOP Condition_Occurrence table

## Maturity and Status
- The repository appears to be actively maintained with recent commits
- Last release: v1.7.053 (July 26, 2024)
- The tool was developed and tested in a Windows environment using MS SQL Server, but should be runnable anywhere as it's written in Java
- Recent work includes updates for PostgreSQL support

