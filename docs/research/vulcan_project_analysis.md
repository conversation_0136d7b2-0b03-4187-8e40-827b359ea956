# Vulcan Project FHIR to OMOP Mapping Analysis

## Overview
The Vulcan Project is a collaborative effort between HL7 and OHDSI to develop standardized mappings between FHIR resources and the OMOP Common Data Model. This analysis examines the Vulcan Project's FHIR to OMOP Implementation Guide and associated mapping files to understand the mapping strategies and reference resources available for FHIR to OMOP transformation.

## FHIR to OMOP Implementation Guide

### Background
The FHIR to OMOP Implementation Guide (IG) is being developed by the HL7 Vulcan Accelerator in collaboration with the OHDSI OMOP + FHIR Working Group. The IG aims to provide standardized transformation specifications for converting data between FHIR and OMOP formats.

Key points about the collaboration:
- OMOP has widespread deployment internationally for research analytics
- FHIR is the world's most important interoperability platform for health data
- Establishing standard transformations allows organizations to leverage both FHIR's API capabilities and OHDSI's analytic methods

### Current Status
- The Implementation Guide is currently in draft status (v0.1.0)
- It is being developed through the HL7 Vulcan Accelerator
- The project is actively ongoing with regular meetings and updates

### Project Goals
1. Support the development of FHIR to OMOP data transfer for better analysis of clinical data for research
2. Develop an Implementation Guide for OMOP + FHIR transformations
3. Create canonical mappings from FHIR to OMOP for core EMR data

## Mapping Structure and Format

The Vulcan Project uses a specialized mapping language called FML (FHIR Mapping Language) to define transformations between FHIR resources and OMOP tables. These mapping files follow a specific structure:

1. **Header Information**:
   - URL of the mapping
   - Name of the mapping
   - Title describing the mapping purpose
   - Status (currently "draft")

2. **Resource Definitions**:
   - Source FHIR resource with alias
   - Target OMOP table with alias

3. **Mapping Groups**:
   - Structured mappings between source and target fields
   - Transformation rules and data type conversions

### Example: Patient to Person Mapping

```
/// url = 'http://hl7.org/fhir/uv/omop/StructureMap/PersonMap'
/// name = 'PersonMap'
/// title = 'Mapping Patient resource to Person OMOP Domain'
/// status = 'draft'

uses "http://hl7.org/fhir/StructureDefinition/Patient" alias Patient as source
uses "http://hl7.org/fhir/uv/omop/StructureDefinition/Person" alias PersonTable as target

group Person(source src: Patient, target tgt : PersonTable) {
  src.id as id -> tgt.person_source_value = cast(id, "string");
  src.gender as gender -> tgt.gender_source_value = cast(gender, "string");
  src.birthDate as bdSrc -> tgt.birth_datetime = bdSrc,
                            tgt.year_of_birth = evaluate(bdSrc, substring(0,4)),
                            tgt.month_of_birth = evaluate(bdSrc, substring(5,2)),
                            tgt.day_of_birth = evaluate(bdSrc, substring(8,2));
}
```

This mapping shows how:
- Patient.id maps to Person.person_source_value
- Patient.gender maps to Person.gender_source_value
- Patient.birthDate maps to multiple Person fields (birth_datetime, year_of_birth, month_of_birth, day_of_birth)

### Example: Immunization to Drug Exposure Mapping

```
/// url = 'http://hl7.org/fhir/uv/omop/StructureMap/ImmunizationMap'
/// name = 'ImmunizationMap'
/// title = 'Mapping Immunization resource to Drug Exposure OMOP Domain'
/// status = 'draft'

uses "http://hl7.org/fhir/StructureDefinition/Immunization" alias Immunization as source
uses "http://hl7.org/fhir/uv/omop/StructureDefinition/DrugExposure" alias DrugExposureTable as target

group DrugExposure(source src: Immunization, target tgt : DrugExposureTable) {
  src.vaccineCode as s -> tgt then {
    s.coding as sc -> tgt then {
      sc.code -> tgt.drug_concept_id, tgt.drug_source_value, tgt.drug_source_concept_id;
    };
  };
  
  src.patient as s -> tgt then {
    s.reference -> tgt.person_id;
    s.identifier as sid -> tgt then {
      sid.value -> tgt.person_id;
    };
  };
  
  src.doseQuantity as s -> tgt then {
    s.value as s -> tgt.quantity = cast(s, "decimal");
    s.code as s -> tgt.dose_unit_source_value = cast(s, "string");
  };
  
  src.route as s -> tgt then {
    s.text as s -> tgt.route_source_value = cast(s, "string");
    s.coding as sc -> tgt then {
      sc.code -> tgt.route_concept_id, tgt.route_source_value;
    };
  };
  
  src.occurrence : dateTime -> tgt.drug_exposure_start_date, tgt.drug_exposure_start_datetime, tgt.drug_exposure_end_date, tgt.drug_exposure_end_datetime;
  
  src.encounter as s -> tgt then {
    s.reference -> tgt.visit_occurrence_id;
    s.identifier as sid -> tgt then {
      sid.value -> tgt.visit_occurrence_id;
    };
  };
  
  src.lotNumber as s -> tgt.lot_number = cast(s, "string");
}
```

This mapping shows how:
- Immunization.vaccineCode maps to DrugExposure.drug_concept_id, drug_source_value, and drug_source_concept_id
- Immunization.patient maps to DrugExposure.person_id
- Immunization.doseQuantity maps to DrugExposure.quantity and dose_unit_source_value
- Immunization.route maps to DrugExposure.route_concept_id and route_source_value
- Immunization.occurrence maps to multiple date fields in DrugExposure
- Immunization.encounter maps to DrugExposure.visit_occurrence_id
- Immunization.lotNumber maps to DrugExposure.lot_number

## OMOP Table Definitions

The Implementation Guide includes detailed definitions of OMOP tables as logical models, including:

1. **Person Table**: Contains demographic information about patients
2. **Condition Occurrence Table**: Contains records of disease or medical conditions
3. **Drug Exposure Table**: Contains records about exposure to drugs
4. **Measurement Table**: Contains records of measurements and lab tests
5. **Observation Table**: Contains clinical facts about patients
6. **Procedure Occurrence Table**: Contains records of procedures performed
7. **Visit Occurrence Table**: Contains records of encounters between patients and providers

Each table definition includes detailed descriptions of fields, data types, and relationships to other tables.

## Mapping Strategies

Based on the examination of the Vulcan Project's mapping files, several key mapping strategies can be identified:

1. **Direct Field Mapping**: Simple one-to-one mappings between FHIR resource elements and OMOP table fields
   - Example: Patient.id → Person.person_source_value

2. **Data Type Conversion**: Explicit casting of data types to match OMOP requirements
   - Example: cast(id, "string") for string fields

3. **One-to-Many Mapping**: Mapping a single FHIR element to multiple OMOP fields
   - Example: Patient.birthDate → Person.birth_datetime, year_of_birth, month_of_birth, day_of_birth

4. **Nested Element Mapping**: Handling nested FHIR elements through chained mapping expressions
   - Example: Immunization.vaccineCode.coding.code → DrugExposure.drug_concept_id

5. **Reference Resolution**: Mapping FHIR references to appropriate OMOP foreign keys
   - Example: Immunization.patient.reference → DrugExposure.person_id

6. **String Manipulation**: Using functions like substring() to extract parts of values
   - Example: evaluate(bdSrc, substring(0,4)) to extract year from birthDate

7. **Terminology Mapping**: Mapping coded values between FHIR and OMOP vocabularies
   - Example: Mapping vaccine codes to drug_concept_id

## Limitations and Considerations

1. **Draft Status**: The Implementation Guide is still in draft form and subject to changes
2. **Limited Resource Coverage**: Currently only a few FHIR resources have defined mappings
3. **Vocabulary Mapping Complexity**: The mapping of terminology systems between FHIR and OMOP is complex and not fully addressed in the current mappings
4. **Implementation Challenges**: The mappings define the transformation rules but don't provide actual implementation code

## Relevance to FHIR-to-OMOP Pipeline Development

The Vulcan Project's FHIR to OMOP Implementation Guide provides:

1. A standardized approach to mapping FHIR resources to OMOP tables
2. Detailed mapping specifications that can be used as reference for implementation
3. A formal language (FML) for expressing transformations
4. Community-validated mapping patterns endorsed by both HL7 and OHDSI

These resources are invaluable for developing a FHIR-to-OMOP transformation pipeline as they provide authoritative guidance on how to map between the two standards.

## References

1. [FHIR to OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
2. [Vulcan Project FHIR to OMOP (2025)](https://confluence.hl7.org/spaces/VA/pages/325451879/FHIR+to+OMOP+2025)
3. [HL7/fhir-omop-ig GitHub Repository](https://github.com/HL7/fhir-omop-ig)
4. [OHDSI OMOP + FHIR Working Group](https://www.ohdsi.org/wp-content/uploads/2024/02/FHIR-24.pdf)
