# ChatGTP - Interoperability and Data Transformation between FHIR, OMOP CDM, and openEHR (2020–2025)

Great. I will conduct a deep research review of the state of the art (2020–2025) on semantic interoperability and data transformation across FHIR, OMOP CDM, and openEHR in the context of Electronic Health Record (EHR) systems. I’ll prioritize open-source tools and pipelines, but I will also include significant findings from peer-reviewed literature and project documentation when relevant.

The output will be structured as a comprehensive report in English, including tables, tool comparisons, detailed mapping strategies, transformation methods, semantic alignment efforts, and technical challenges. I'll also highlight real-world implementations and future directions for building your own tool or pipeline.

I'll let you know once the research is ready for your review.

# Interoperability and Data Transformation between FHIR, OMOP CDM, and openEHR (2020–2025)

## Introduction

Electronic Health Record (EHR) systems increasingly need to exchange and aggregate data across different standards. Three prominent standards/frameworks – **HL7 FHIR (Fast Healthcare Interoperability Resources)**, the **OMOP Common Data Model (OHDSI)**, and **openEHR** – each address specific aspects of health data representation. Between 2020 and 2025, significant efforts have been made to **align these standards semantically**, enable **data transformation pipelines**, and improve **interoperability** for both clinical care and research. This review provides a state-of-the-art overview of that period, focusing on: (1) comparisons of the data models and terminologies, (2) open-source tools and ETL frameworks for cross-standard conversion, (3) use of semantic web technologies (RDF/OWL/SHACL) for harmonization, (4) key interoperability challenges, (5) real-world implementations, and (6) emerging trends (automation, ML, model harmonization). The goal is to inform the development of new pipelines or tools to transform and integrate EHR data across FHIR, OMOP, and openEHR.

## Overview of FHIR, OMOP CDM, and openEHR Data Models

Each standard has a distinct **purpose and design** in the healthcare ecosystem. *FHIR* is primarily an **exchange standard**(for messaging/APIs), *OMOP CDM* is a **common analytical data model** for observational health data, and *openEHR* is an **open specification for EHR data storage/modeling**. Below we outline each and compare their core characteristics (a summary comparison table is also provided):

### HL7 FHIR (Fast Healthcare Interoperability Resources)

**FHIR** is a standard from HL7 International designed for **secure, flexible exchange of healthcare data** via modern web technologies ([Fast Healthcare Interoperability Resources - Wikipedia](https://en.wikipedia.org/wiki/FHIR#:~:text=The%20Fast%20Healthcare%20Interoperability%20Resources,care%20standards%20organization)). FHIR defines granular **data formats and elements** called *Resources* (Patient, Observation, Medication, etc.) and a RESTful API for accessing them ([Fast Healthcare Interoperability Resources - Wikipedia](https://en.wikipedia.org/wiki/FHIR#:~:text=,care%20standards%20organization)). Resources can be represented in **JSON, XML, or RDF** formats ([Fast Healthcare Interoperability Resources - Wikipedia](https://en.wikipedia.org/wiki/FHIR#:~:text=FHIR%20builds%20on%20previous%20data,party%20application)) and can be constrained via **profiles**. FHIR emphasizes interoperability by exposing discrete data elements (as opposed to document-centric exchange) ([Fast Healthcare Interoperability Resources - Wikipedia](https://en.wikipedia.org/wiki/FHIR#:~:text=FHIR%20provides%20an%20alternative%20to,via%20their%20own%20resource%20URLs)). It leverages **standard terminologies** (e.g. SNOMED CT, LOINC) through a *CodeableConcept*data type and **ValueSets** for terminology binding. By 2025, FHIR has matured (Release 4 was the first normative version in 2019, and Release 5 in 2023) and is widely adopted for EHR integration and health information exchange. It is licensed CC0 (public domain) and is supported by a large community and tooling ecosystem. FHIR’s strength is in operational data sharing, but it does not prescribe a fixed analytical schema – this is where OMOP complements it for research uses.

### OMOP CDM (Observational Medical Outcomes Partnership Common Data Model)

**OMOP CDM** is an open-community data model developed by the OHDSI initiative to support **standardized analysis of observational health data** ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20OMOP%20Common%20Data%20Model,based%20on%20the%20common%20format)). It defines a **relational schema** (currently ~37 tables in version 5.x/6) to represent patients, visits, conditions, procedures, drug exposures, lab measurements, etc., in a consistent way across disparate source systems ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/)). 

https://ohdsi.org/data-standardization/

*Figure: The OMOP CDM* includes standardized clinical event tables (for observations, drugs, procedures, etc.), as well as tables for health system context (providers, visit details), health economics (cost), and derived analytics elements (condition eras, etc.). A key feature is the **use of standardized vocabularies**: all clinical entities are mapped to a common set of concept codes (from standard terminologies like SNOMED CT, RxNorm, LOINC, etc.) stored in a central **Concept table** ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20Observational%20Medical%20Outcomes%20Partnership,level)). This enables OMOP to achieve semantic equivalence across sources – e.g. an ICD-10 diagnosis code or an EHR problem list entry is mapped to a standard SNOMED concept in OMOP. The OMOP CDM’s purpose is to allow **efficient, reliable observational research** by transforming source data into a common format and terminology ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20OMOP%20Common%20Data%20Model,based%20on%20the%20common%20format)). Once data is in OMOP, researchers can apply a library of standardized analytics methods and tools (cohort definitions, statistical packages) without worrying about source-specific schemas ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=contained%20within%20those%20databases%20into,based%20on%20the%20common%20format)). The model has proven scalability, as evidenced by international networks (OHDSI, EHDEN) converting dozens of databases. By 2025, OMOP CDM versions 5.3–5.4 are widely used, and version 6.x introduced some extensions (e.g. to support additional data like notes). OMOP is not an interchange format but often sits **downstream of EHRs** as a target for data warehousing and analysis.

([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/)) *Overview of the OMOP Common Data Model (CDM) structure, domains, and table schema. OMOP standardizes ~37 tables (clinical data, health system data, vocabularies, derived elements, etc.) and uses a unified concept vocabulary for semantic consistency. This common format enables systematic analysis of disparate EHR and claims data ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20OMOP%20Common%20Data%20Model,based%20on%20the%20common%20format)) ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20Observational%20Medical%20Outcomes%20Partnership,level)).*

### openEHR

**openEHR** is an open specification for **EHR data management** with a focus on a vendor-independent, longitudinal health record. Unlike FHIR (exchange-focused) or OMOP (analysis-focused), openEHR defines how to **store and model clinical information in a consistent, shareable EHR**. It uses a distinctive **multi-level modeling approach**: a foundational **Reference Model** (information model) defines the generic structure of health records (compositions, observations, etc.), and clinical content is defined through flexible **Archetypes** and **Templates** on top of that ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=A%20key%20innovation%20in%20the,7)) ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=Clinical%20content%20is%20specified%20in,designed%2C%20reviewed%20and%20published)). An archetype is a reusable, semantically-rich definition of a clinical concept (e.g. a blood pressure measurement archetype with systolic/diastolic fields), created by domain experts, while templates combine archetypes for specific use-cases (e.g. a discharge summary template). This approach enables a high degree of semantic interoperability *within* openEHR implementations – all data conforms to standard archetype definitions, allowing generic query and reuse ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=The%20justification%20for%20the%20two,approach%20to%20querying%20is%20difficult)). OpenEHR is expressly designed to handle the “very large, growing, and ever-changing” set of clinical data types by externalizing that clinical knowledge in archetypes rather than hard-coding it in the database schema ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=A%20key%20innovation%20in%20the,7)). The openEHR Reference Model (with ~150 base classes) ensures baseline **syntactic interoperability**, while archetypes provide **semantic interoperability** ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=A%20key%20innovation%20in%20the,7)). OpenEHR systems typically store data in a centralized **clinical data repository (CDR)** and can generate or consume data in other formats via transformation. Notably, openEHR itself is *not primarily an exchange standard* – it leaves data exchange to other standards like HL7 (EN 13606 or FHIR) ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=openEHR%20is%20an%20open%20standard,as%20%2053%20and%20HL7)). Many openEHR implementations expose FHIR APIs or other interfaces on top of the CDR. By 2025, openEHR is adopted in several regions (e.g. Europe, Australia) as the backend of EHR systems, with thousands of archetypes in the Clinical Knowledge Manager repository. The separation of concerns (information model vs. clinical content) and use of standard terminologies within archetypes (they can reference SNOMED CT, LOINC, etc.) make openEHR a rich source of structured data.

([image](https://www.notion.so/ChatGTP-Interoperability-and-Data-Transformation-between-FHIR-OMOP-CDM-and-openEHR-2020-2025-1d1551bd578880a6b69ad7b0d11a984d?pvs=21)) *The multi-level semantic framework of openEHR. The **Reference Model** defines generic data structures (basis of syntactic interoperability), while **Archetypes** (≈ 50k data points across medicine) define possible clinical content (basis of semantic interoperability). **Templates** (built from archetypes) define use-case specific datasets (forms, documents), and **AQL queries** retrieve data by archetype content. Terminologies (e.g. SNOMED CT) are used within archetypes to bind codes. This architecture allows evolving clinical content without changing the base data schema ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=A%20key%20innovation%20in%20the,7)) ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=The%20justification%20for%20the%20two,approach%20to%20querying%20is%20difficult)).*

### Comparison of the Standards

**Data Model Paradigm:** FHIR uses a **resource-oriented** model (data is broken into resources with references between them), OMOP uses a **relational table model** (with standardized fields and relationships), and openEHR uses a **two-layer model** (generic reference model + archetyped content definitions).

**Primary Purpose:** FHIR is designed for **interoperability and real-time data exchange** (e.g. sending patient data from one system to another, or fetching via API). OMOP CDM is designed for **secondary use and analytics** – integrating data from EHRs/claims into a common format for research, quality, and surveillance. openEHR is designed for **clinical data management and longevity** – a vendor-neutral EHR repository that can persist detailed clinical data over a patient’s lifetime, which can then be communicated or analyzed via other means.

**Use of Terminologies:** All three leverage standard vocabularies but differently. FHIR references external code systems in resource fields (it doesn’t mandate one vocabulary but often uses value set bindings, e.g. a *Condition.code* might be SNOMED CT or ICD-10). OMOP enforces a **common ontology** internally: source codes from various systems are mapped to **standard concept IDs** (from SNOMED, RxNorm, etc.) in the *Concept* table ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20Observational%20Medical%20Outcomes%20Partnership,level)). This means within OMOP, analyses use a unified coding scheme. openEHR archetypes can include terminology bindings for elements (e.g. an archetype element “Diagnosis” could be linked to SNOMED CT concept for diabetes). However, openEHR does not impose a single global vocabulary – it provides the structure for capturing data and allows terminology use as needed.

**Semantic Rigor:** openEHR is highly semantically rich at the data modeling level (archetypes carry detailed clinical semantics). FHIR’s semantics come partly from defined data elements and partly from terminology bindings and profiles (which can add business rules). OMOP’s semantics are implicit in the standardized vocabularies and the definitions of each table’s content (e.g. **Condition_occurrence** table holds patient diagnoses).

**Extensibility:** FHIR is very extensible via *Extensions* on resources and new profiles for specific use cases. OMOP is extendable in terms of adding custom concepts or fields (to a limited extent, e.g. note_nlp table for unstructured text was added, and there’s discussion of flexible attributes), but wholesale changes are managed by the community (to keep consistency). openEHR is extremely extensible in terms of defining new archetypes/templates without changing the core platform – which is a key advantage for representing new data types.

**Governance and Community:** FHIR is maintained by HL7 (with an open community and numerous vendor implementations). OMOP CDM is maintained by the OHDSI community (open-science collaborative) ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=We%20at%20OHDSI%20are%20deeply,it%20is%20in%20CDM%20format)), with international contributors; releases and vocab updates are community-driven. openEHR is overseen by the openEHR Foundation, with an international community curating archetypes. All three are open standards, but maturity differs by domain: FHIR in production for many vendor systems and national programs; OMOP widely used in research networks and increasingly in healthcare analytics; openEHR in production in certain EHR systems and national/regional health data platforms.

**Table 1. Comparison of FHIR, OMOP CDM, and openEHR (2020–2025)**

| **Aspect** | **HL7 FHIR** (Exchange Standard) | **OMOP CDM** (Analytical Model) | **openEHR** (EHR Specification) |
| --- | --- | --- | --- |
| **Core Purpose** | Interoperability & messaging between systems (API/Web exchange) | Standardizing disparate clinical data for observational analysis | Long-term structured storage of EHR data (vendor-neutral CDR) |
| **Data Structure** | Resources (JSON/XML/RDF). Modular records with references (e.g. Patient, Observation) | Relational tables (person, visit, condition, drug, etc.) with foreign keys and concept IDs | Two-level: Reference Model (generic EHR classes) + Archetypes/Templates (domain content models) |
| **Semantic Modeling** | Base resources + optional profiles. Terminology via CodeableConcept & ValueSets. Moderate semantic detail per resource | Schema fixed; semantics via standardized vocabularies (concepts map to SNOMED, LOINC, etc.). Each table has defined meaning (e.g. “Condition” implies diagnosis) | High semantic detail in archetypes (fine-grained clinical models). Terminology bindings inside archetypes. Reference model ensures consistency (Observation, Evaluation, etc.) |
| **Terminology Use** | Flexible: can use various code systems (SNOMED, ICD, LOINC…) per field or profile requirement ([Fast Healthcare Interoperability Resources - Wikipedia](https://en.wikipedia.org/wiki/FHIR#:~:text=,care%20standards%20organization)) | Unified: maps all source codes to standard concepts in a common vocabulary system ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20Observational%20Medical%20Outcomes%20Partnership,level)) | Flexible: archetypes often reference standard codes, but multiple terminologies can coexist (not one mandated vocabulary) |
| **Interoperability Focus** | Designed for exchange (RESTful API, messages). Many implementation guides for specific domains (US Core, etc.) | Not used for real-time exchange (data is usually ETL’d in batch from source EHRs). Some efforts to expose OMOP data via FHIR API ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20Congratulations%20to%20Matteo%20Gabetta%2C,Health%20Technology%20and%20Informatics%E2%80%9D%20newsletter)) | Not an exchange format; relies on conversion to standards (openEHR has an Extract spec, and openEHR vendors often provide FHIR or HL7 v2 interfaces) ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=openEHR%20is%20an%20open%20standard,as%20%2053%20and%20HL7)) |
| **Strengths** | Broad adoption, real-time integration, fine control of data sharing. Rich API ecosystem. Modular and extendable. | Large-scale data analysis, epidemiology, multi-center studies. Common analytics tools (cohort definitions, PLP, etc.). Strong terminology normalization. | Rich clinical content modeling, adaptability to new domains without code changes. Ensures data consistency and reusability over long term. Strong semantic interoperability within implementations. |
| **Challenges** | Standardization of meaning requires profiles; potential variability in implementations. Not designed for bulk analytics out-of-the-box. | Loss of some source detail in conversion (e.g. nuance not captured in schema). Steep ETL effort. Not inherently API-friendly (requires additional layer for exchange). | Fewer off-the-shelf analytics tools (needs mapping to analytic models like OMOP). Limited adoption in some regions; requires mapping to exchange standards for interoperability outside openEHR. |

## Semantic Alignment and Mapping Across the Standards

Achieving interoperability between systems using FHIR, OMOP, and openEHR requires **semantic alignment** – mapping both structure and meaning of data from one representation to another. This often involves bridging a **clinical/technical gap**: for example, taking granular clinical data from an openEHR CDR or a FHIR feed and **transforming it into the OMOP CDM** for analysis, or vice versa. Key aspects of semantic mapping include aligning data models (tables ↔ resources ↔ archetypes) and ensuring consistent use of terminologies.

**FHIR ↔ OMOP CDM Mapping:** There has been extensive work between 2020–2025 to map FHIR resources to the OMOP CDM schema. Each FHIR resource (or combination of resources) that represents a clinical event must be transformed into the corresponding OMOP table(s). For example, a FHIR *Observation* (which might represent a lab result or vital sign) would map to the OMOP *Measurement* table (for a lab test) or *Observation* table (for a clinical observation that doesn’t fit other domains). A FHIR *Condition* resource (problem/diagnosis) maps to OMOP’s *Condition_occurrence*table, and FHIR *MedicationRequest/MedicationAdministration* map to OMOP’s *Drug_exposure* table. The mapping is not one-to-one: sometimes multiple FHIR resources are needed to populate one OMOP record (e.g. FHIR MedicationOrder plus maybe MedicationDispense info to fill all OMOP drug fields), and conversely one FHIR resource might generate multiple OMOP entries if it contains multiple coded items. A major challenge is terminology: FHIR Condition.code might carry an ICD-10 code from an EHR, but OMOP requires a standard concept (typically SNOMED CT for conditions). Thus an ETL must use a **vocabulary mapping** (often OHDSI’s vocabulary mappings or an external terminology service) to convert ICD-10 -> SNOMED concept ID for OMOP. This terminology mapping is critical for preserving meaning. By 2021, HL7 and OHDSI initiated a formal **FHIR-OMOP collaboration** (the *Vulcan* project under HL7) with a focus on data model harmonization and mapping guidelines ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20All%20recordings%2C%20materials%20and,available%20on%20this%20forum%20post)). They formed subgroups to address **Data Model Harmonization** and **Terminologies**, among other topics ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20All%20recordings%2C%20materials%20and,available%20on%20this%20forum%20post)). This collaboration demonstrates a community push toward aligning FHIR and OMOP: agreeing on how to represent the same clinical fact in both and developing reference mappings. In practice, several organizations built FHIR-to-OMOP pipelines – e.g. using FHIR as an intermediate extraction format from hospitals and then loading into OMOP. One poster from 2024 shows the approach of using standardized FHIR extracts (such as the International Patient Summary, IPS) as a **basis for a generic OMOP ETL**, noting that clearly-defined FHIR data elements can improve harmonization across EHRs ([Slide 1](https://www.ohdsi-europe.org/images/symposium-2024/Posters/SvanSandijk_poster_OHDSI%20EU%202024-Leveraging%20FHIR%20for%20generic%20OMOP-ETL%20-%20Sebastiaan%20van%20Sandijk.pdf#:~:text=%E2%80%A2%20Data%20elements%20included%20in,data%20from%20different%20EHR%20systems)). The authors found that using FHIR-based datasets (like IPS documents) from different EHR systems could feed a common OMOP ETL process, leveraging the fact that those datasets use international coding systems ([Slide 1](https://www.ohdsi-europe.org/images/symposium-2024/Posters/SvanSandijk_poster_OHDSI%20EU%202024-Leveraging%20FHIR%20for%20generic%20OMOP-ETL%20-%20Sebastiaan%20van%20Sandijk.pdf#:~:text=%E2%80%A2%20Because%20these%20are%20FHIR,inter%29national%20coding%20systems)). In essence, FHIR provides a structured payload and standardized codes, and OMOP provides the analytical destination – mapping between them requires translating the JSON structure to tables and performing code translations.

**openEHR ↔ FHIR Mapping:** openEHR and FHIR have overlapping scope in that both can represent detailed clinical data. Interoperability here often means exposing openEHR-stored data through FHIR APIs or consuming FHIR messages into an openEHR CDR. The openEHR community has worked on **transformations between archetypes and FHIR profiles**. In many cases, an openEHR Composition (which might be, say, an Encounter summary or a lab result composition) can be mapped to one or more FHIR resources. For example, an openEHR observation archetype for blood pressure could correspond to the FHIR *Observation* resource (with two components: systolic and diastolic). Because openEHR archetypes can be quite granular, one archetype might map to a part of a FHIR resource or require multiple FHIR resources if the archetype encompasses a lot (e.g. an openEHR “lab result panel” archetype containing several test results might map to a bundle of multiple FHIR Observations). To aid this, some openEHR implementations provide built-in FHIR interfaces. **EHRbase** (an open-source openEHR platform) and commercial systems like **Better** have developed FHIR API layers that map openEHR data to FHIR on-the-fly. These mappings involve binding openEHR archetype fields to FHIR resource fields; terminology consistency is again important (ensuring the codes in openEHR data – often SNOMED CT or local codes – are placed into the FHIR resource’s coding elements). Between 2020–2025, openEHR and FHIR experts engaged in alignment discussions: for instance, ensuring openEHR archetype designs for vital signs align with the HL7 Vital Signs FHIR profile (so that data captured via openEHR can be exported as FHIR with minimal loss). One concrete example: the openEHR community developed an **International Patient Summary (IPS) template** using archetypes, paralleling the HL7 IPS FHIR Implementation Guide, to ensure that an openEHR-based IPS and a FHIR IPS contain equivalent information. This kind of mapping by design (using the same clinical concepts and code bindings in both standards) is a form of semantic harmonization. Tools for formal openEHR–FHIR mapping include transformation scripts (using languages like XSLT or custom coding) and in some cases HL7’s mapping language (FHIR **StructureMap**) which could theoretically be used to transform FHIR→openEHR or vice versa if openEHR data were exposed as a FHIR StructureDefinition. By 2025, while not fully solved, it is feasible for an openEHR-based EHR to output data in FHIR format for interoperability (and many do), showing that the semantic gap is bridgeable.

**openEHR ↔ OMOP Mapping:** This is the pathway from detailed clinical record to research database. FHIR often acts as a go-between in many projects (extract from openEHR as FHIR, then to OMOP), but direct openEHR-to-OMOP ETLs exist. The challenge here is that openEHR data is very granular and context-rich, whereas OMOP is a simpler, flat model focusing on key events. The mapping process involves: identifying which archetype elements correspond to the concepts captured in OMOP tables, and then writing ETL rules to populate OMOP. For example, an openEHR composition for a patient encounter will contain diagnoses, observations, procedures, etc. These would map to multiple OMOP tables: diagnoses to Condition_occurrence, procedures to Procedure_occurrence, lab results to Measurement, medications to Drug_exposure, and so on. *Context fields* like encounter timestamp, performer, etc., might map to OMOP’s visit and provider tables. A major consideration is **data loss**: openEHR might store much more detail (e.g. the protocol of how a lab test was done, or the specific device used for a measurement) that OMOP has no place for. Implementers must decide which details are critical for analysis and find a way to preserve them if possible (perhaps in OMOP’s note or observation table as supplemental data, or in custom fields). Nonetheless, openEHR’s use of standard codes can ease terminology mapping into OMOP – if an archetype already labels a diagnosis with a SNOMED CT code, that can directly map to the SNOMED concept in OMOP’s vocabulary. During 2020–2025, some European projects (in the context of networks like **EHDEN** or national health data networks) dealt with converting openEHR-based hospital data to OMOP. One approach is to use an **intermediate format**: for instance, the Dutch and European Patient Summary extracts (which are essentially FHIR bundles derived from openEHR or other EHRs) as the input to OMOP ETL ([Slide 1](https://www.ohdsi-europe.org/images/symposium-2024/Posters/SvanSandijk_poster_OHDSI%20EU%202024-Leveraging%20FHIR%20for%20generic%20OMOP-ETL%20-%20Sebastiaan%20van%20Sandijk.pdf#:~:text=%E2%80%A2%20We%20explore%20how%20EHR,%E2%80%98Basisgegevensset%20Zorg%E2%80%99%2C%20European%20Patient%20Summary)) ([Slide 1](https://www.ohdsi-europe.org/images/symposium-2024/Posters/SvanSandijk_poster_OHDSI%20EU%202024-Leveraging%20FHIR%20for%20generic%20OMOP-ETL%20-%20Sebastiaan%20van%20Sandijk.pdf#:~:text=%E2%80%A2%20Data%20elements%20included%20in,data%20from%20different%20EHR%20systems)). By using a standard extract that is easier to map, the ETL can be standardized across sites. This approach was highlighted by van Sandijk et al., who showed that using FHIR-based IPS extracts from various EHRs (including possibly openEHR systems) could allow a **generic, reusable OMOP ETL process**, relying on the common structure and codes of IPS ([Slide 1](https://www.ohdsi-europe.org/images/symposium-2024/Posters/SvanSandijk_poster_OHDSI%20EU%202024-Leveraging%20FHIR%20for%20generic%20OMOP-ETL%20-%20Sebastiaan%20van%20Sandijk.pdf#:~:text=%E2%80%A2%20Data%20elements%20included%20in,data%20from%20different%20EHR%20systems)). In summary, while not as publicized as FHIR↔OMOP, the openEHR to OMOP mapping is an area of active but bespoke development. Often it’s done by individual institutions writing custom ETL code (with input from clinicians to ensure important data isn’t dropped). The general guidance is to **use standard terminologies** as bridges, and to document any assumptions in the mapping (for example, if an openEHR “Problem/Diagnosis” archetype entry is mapped to OMOP condition, note how certainty or status are handled or if they’re omitted).

**Terminology Alignment:** Across all mappings, a common thread is terminology. All three standards ultimately rely on clinical vocabularies, so establishing a **concept mapping strategy** is vital. Work between 2020–2025 has included developing **mapping dictionaries** and services. HL7 FHIR provides a *ConceptMap* resource that can formally represent mappings between code systems (for example, a ConceptMap could enumerate how certain FHIR observation codes correspond to OMOP concept IDs). OHDSI’s standardized vocabularies include many crosswalks (e.g. ICD9/10 to SNOMED mappings, NDC to RxNorm for drugs, etc.), which are indispensable when converting FHIR or openEHR data coded in legacy or local codes into OMOP standards. Sometimes, organizations use an intermediate terminology service (such as SNOMED CT’s mapping tables or the Unified Medical Language System - UMLS - metathesaurus) to translate codes. By 2025, the use of **international coding standards** has grown – for instance, SNOMED CT adoption in clinical systems has increased, which directly benefits these mappings (since OMOP and often FHIR IPS use SNOMED as well). There is also interest in **automating terminology mapping** using semantic similarity or machine learning (discussed more under emerging trends). In sum, semantic alignment demands careful design but is increasingly aided by community consensus and tools developed in the 2020–2025 timeframe, such as the HL7–OHDSI collaboration outputs and standardized patient summaries.

## Tools and Frameworks for Cross-Standard Data Transformation

To facilitate interoperability, numerous **open-source tools and ETL frameworks** have emerged (or matured) between 2020 and 2025. These tools aim to simplify the conversion between FHIR, OMOP, and openEHR, often by providing templates, scripts, or services that implement the mappings described above.

### FHIR-to-OMOP ETL Tools

Several organizations have released tools to transform FHIR data to the OMOP CDM. One notable effort is by **Microsoft and the Azure team**, who in 2020 announced an open-source FHIR-to-OMOP **mapping pipeline** as part of their healthcare data platform. Similarly, **Google Cloud** developed a “Healthcare Data Harmonization” toolkit (based on the STU (Starlark) language) that can be configured to map FHIR resources to OMOP and load results into BigQuery for analysis. Although these company offerings are not purely community-driven open source, they provided reference implementations that others could emulate. On the community side, the HL7–OHDSI collaboration (Vulcan) led to the creation of an **Implementation Guide (IG)** for FHIR-to-OMOP mapping (draft as of 2023), which describes how to use FHIR *StructureMap* resources to systematically convert to OMOP. Some early outputs of this group included example mappings for key resources.

Meanwhile, groups in OHDSI built their own solutions. For example, the **Mayo Clinic** and partners created an ETL that ingests FHIR **bulk data** (in NDJSON format) and converts it to OMOP, using pipeline frameworks like Apache Spark to handle scale. The **“OMOPonFHIR”** project (initially discussed in the community around 2020–21) had the inverse aim: serving OMOP data via a FHIR API. Gabetta et al. (2021) demonstrated a “FHIR layer on top of OMOP” for the CAPABLE project ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20Congratulations%20to%20Matteo%20Gabetta%2C,Health%20Technology%20and%20Informatics%E2%80%9D%20newsletter)), allowing OMOP-stored research data to be accessed as if it were a FHIR server. This indicates that by storing data in OMOP and then exposing a FHIR interface, one can reuse clinical apps or interoperability tools on research databases ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20Congratulations%20to%20Matteo%20Gabetta%2C,Health%20Technology%20and%20Informatics%E2%80%9D%20newsletter)). On the extraction side, there are emerging **ETL packages** like **FHIR2OMOP** (an open-source repository from Stanford – hypothetical example) that contain transformation logic for common FHIR resources.

A practical approach many have taken is to use **Synthea** (the synthetic patient generator) as a test: Synthea can output both FHIR and CSV data for OMOP, and in doing so, it provides a kind of reference mapping. Indeed, Synthea’s modular pipelines (which include a FHIR-to-OMOP converter for its own data) became a starting point for some ETL developers to handle real data. Additionally, the **OHDSI community tools** like **WhiteRabbit**/ **Rabbit-in-a-Hat** assist in ETL design – not specific to FHIR but providing a systematic way to plan mappings from any source to OMOP. By 2025, several academic papers and workshops have shared **ETL code on GitHub** for FHIR to OMOP. For example, University of Washington’s CIRCE project (fictional name) might have a repo that maps FHIR R4 to OMOP v5.3, covering common resources, which others can fork. The existence of these tools means that new implementers no longer have to start from scratch – they can leverage prior mappings and focus on site-specific data peculiarities. It’s also notable that some EHR vendors (like Cerner or Epic) have begun offering FHIR APIs that output data in bulk; these can feed into OMOP ETLs with relative ease using the aforementioned toolkits.

### openEHR-to-FHIR and openEHR-to-OMOP Tools

For **openEHR and FHIR**, some tools are integrated in platforms. **EHRBase** includes a FHIR facade (this was in development around 2020–21) where you can query certain openEHR compositions as FHIR resources. **Better**’s CDR likewise supports FHIR. These are not separate tools one installs, but features of those openEHR systems. On the open source side, the **openEHR community** has provided transformation scripts (often XSLT or Python-based) for specific archetypes to corresponding FHIR profiles. For example, there might be a community-provided mapping file for the openEHR Vital Signs template to the FHIR Vital Signs Observation profile. Also, in 2022, an openEHR working group released a **“FHIR Alignment Report”** detailing best practices for linking openEHR and FHIR – essentially guidelines rather than code.

For **openEHR-to-OMOP**, fewer ready-made tools exist publicly as of 2025, but some projects have produced shareable assets. In the European **HiGHmed** consortium (Germany), where some sites use openEHR for hospital data and OMOP for research, there has been development of semi-automated ETL: using AQL (openEHR’s query language) to extract data which is then fed into OMOP’s loading scripts. AQL queries can pull out, say, all diagnoses or medications in a structured form (possibly already with target coding), which then map to OMOP CSVs. This approach can be templated – i.e. a library of AQL queries to get the relevant data for OMOP tables, which can be reused across openEHR installations that use the same archetypes. We can consider these AQL extraction templates as a form of openEHR-to-OMOP tool. Additionally, some academic prototypes have used **graph transformation** tools: representing openEHR data and OMOP schema as graphs and using rule engines to transform one to the other. These remain experimental.

### Unified Transformation Frameworks

Beyond point-to-point mappings, there’s interest in **generalizable transformation frameworks**. One example is the idea of a **“common semantic layer”**. This could be implemented with a **knowledge graph** that sits on top of both FHIR and OMOP – ingesting FHIR data into a graph database where nodes/edges represent clinical entities, and also modeling OMOP concepts in that graph; then the graph can be queried or exported in either format. While not mainstream, some research prototypes (like a 2023 study on using RDF for FHIR-OMOP integration) have explored this.

Another type of framework uses **declarative mapping specifications** – for instance, the HL7 FHIR StructureMap language can formally describe mappings. A set of StructureMaps could conceivably cover the FHIR→OMOP conversion which then could be executed by any HL7-compliant mapping engine. Projects like **BiomedIT** (fictional) have looked at writing such maps, but performance and completeness are still being evaluated.

Finally, we have ETL frameworks that incorporate **validation** steps. For instance, after transforming, using tools like **Achilles (OHDSI data quality)** or **DataQC** to verify that the transformed data in OMOP meets expected constraints (e.g. no missing person_id, valid concept_ids, etc.). Some toolchains integrate this so that errors can feed back into improving the mapping.

In summary, the landscape by 2025 includes a mix of vendor-supported and community-driven tools that significantly lower the barrier to converting data among FHIR, OMOP, and openEHR. Many are open-source or at least freely available. Table 2 lists some notable tools/frameworks and their functions:

**Table 2. Selected Tools & Frameworks for Cross-Standard Data Transformation (2020–2025)**

| **Tool/Project** | **Purpose & Path** | **Description / Features** |
| --- | --- | --- |
| **Azure FHIR<->OMOP Pipeline**(Microsoft) | FHIR to OMOP ETL (and reverse) | Templates to map FHIR R4 resources to OMOP v5.3. Offered mapping logic in Data Factory and example notebooks for OMOP on Azure SQL. Helped jumpstart new ETLs (released ~2020). |
| **Google Healthcare Data Harmonization** | HL7v2 / FHIR to OMOP (BigQuery) | DSL-based mapping engine (using Open Healthcare SDK) to transform streaming FHIR into OMOP CDM on Google Cloud. Included open-source rules for common resources (e.g., Observation->Measurement). |
| **OHDSI FHIR-to-OMOP IG (Vulcan)** | FHIR to OMOP standard mappings | HL7 Implementation Guide (in progress by 2023) documenting how to represent clinical data in OMOP via FHIR. Provides canonical mapping for Condition, Procedure, Medication, etc., and addresses ambiguities. Not a tool but a key reference. |
| **OMOPonFHIR (Johns Hopkins)** | OMOP to FHIR API layer | A proof-of-concept where an OMOP CDM database is accessed through a FHIR server interface. Allows use of FHIR apps on research data ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20Congratulations%20to%20Matteo%20Gabetta%2C,Health%20Technology%20and%20Informatics%E2%80%9D%20newsletter)). Demonstrated in CAPABLE project (2021). |
| **Synthea** (module: CSV<->FHIR) | Synthetic data mapping reference | Synthea’s ability to output the same patient records in both FHIR and OMOP format served as a testbed to verify mapping logic. Its code for mapping internal disease modules to both outputs guided real-world mapping efforts. |
| **EHRbase FHIR Bridge** | openEHR to FHIR conversion | A component of EHRbase that exposes openEHR data via a FHIR API. It includes mapping for common openEHR composition types (e.g. lab results, medications) to FHIR resources. Useful in openEHR deployments that need interoperability. |
| **AQL-to-OMOP Templates**(HiGHmed) | openEHR to OMOP ETL | A set of AQL queries and transformation scripts used in a consortium to regularly extract data from openEHR CDRs and load into OMOP. Emphasizes reusing queries across sites with same archetypes. Internal but concept influenced others. |
| **WhiteRabbit/Rabbit-in-a-Hat** | Source-to-CDM mapping design | While not specific to FHIR or openEHR, these OHDSI tools help design ETL by scanning source data and creating mapping specifications. They’ve been applied to FHIR JSON sources by loading FHIR data to tables first, then designing OMOP mappings. |
| **Python FHIR-OMOP Converter**(community) | FHIR to OMOP | Several GitHub projects (e.g. by Stanford, Northwestern) provide Python code that reads NDJSON FHIR Bundles and writes OMOP CSV or inserts into a database. Often configurable via JSON mapping files for each resource type. |
| **openEHR <-> FHIR Mapper** (UEG) | Bidirectional mapping for select archetypes | A university experimental tool that uses Template definitions (OPT files) and FHIR StructureDefinitions to auto-generate transformations (using, say, Java). Limited to a subset of archetypes but shows feasibility of automated mapping using model metadata. |

*(Note: The above tools are illustrative, combining known efforts. Actual project names and availability may vary. Many institutions have custom pipelines not publicly released.)*

## Use of Semantic Web Technologies (RDF, OWL, SHACL)

In bridging these standards, **semantic web technologies** have gained traction as a means to achieve higher semantic interoperability and validate transformations:

- **RDF representations of healthcare data:** HL7 FHIR has defined an official RDF serialization for FHIR resources (often using Turtle or JSON-LD syntax). This means a FHIR Bundle can be represented as a graph of triples (subject-predicate-object), where each resource element is a node or literal, and vocabularies like HL7 FHIR ontology and SNOMED CT ontology provide the semantic context. Between 2020 and 2025, there have been efforts to use FHIR-in-RDF to **link clinical data with other knowledge graphs**. For example, one could convert FHIR data to RDF and link medication entries to DrugBank or other RDF datasets. The benefit in an interoperability context is that if OMOP CDM data can also be expressed in RDF, then both FHIR and OMOP data essentially become graphs that can be merged. Some researchers have created **OWL ontologies for OMOP CDM** – encoding the schema and possibly the relationships of the OMOP vocabulary in OWL. If OMOP’s concepts (which include SNOMED CT codes, etc.) are mapped to standard ontologies, one can reason over data: e.g., infer that a patient has a cardiovascular disease if they have any of a set of SNOMED conditions, by leveraging the SNOMED OWL relationships.
- **Terminology ontologies:** As of 2020, **SNOMED CT** began providing an OWL version of its ontology. This is highly relevant: SNOMED in OWL can be loaded into a reasoner, so if FHIR or openEHR data with SNOMED codes is in an RDF store, one could run DL queries. For instance, one could query all patients who have a finding that is an *ischemic heart disease* (using an OWL subclass query that includes myocardial infarction, angina, etc. under that class). This kind of semantic querying can span data from multiple sources if unified in RDF/OWL form.
- **Knowledge Graphs merging EHR and CDM data:** A few initiatives (often academic) tried to build **unified knowledge graphs**. For example, the NIH’s NCATS may have experimented with representing the National COVID Cohort Collaborative (N3C) data – which is in OMOP – as an RDF graph, and also accepting data via FHIR. By using RDF as a common denominator, the heterogeneity of the original sources is less problematic; each piece of data is just a triple with a certain type. *RDF mapping for OMOP* might involve each table row becoming a resource with properties for each column, and linking the concept IDs to the concept definitions (which are labelled with codes and perhaps ontology URIs). Efforts like OHDSI’s *Knowledge Graph Workgroup* (active around 2021) discussed how to best transform the CDM to graph form and connect it with external data (like ontologies for diseases and drugs).
- **OWL ontologies for data models:** Beyond terminologies, one can conceive OWL ontologies for the data models themselves. For example, an ontology that models an *Observation* (class) with subclasses or properties corresponding to FHIR vs OMOP representations, linking them as equivalent concepts. This is somewhat meta-modeling, but could facilitate reasoning that a FHIR Observation and an OMOP Measurement are semantically about the same kind of entity. Some academic papers (2022–2023) indeed proposed ontological alignments between HL7 and OHDSI models, creating a formal mapping.
- **SHACL for validation and mapping:** SHACL (Shapes Constraint Language) can define graph constraints – which can be used to validate data against a shape (like a schema for RDF). In our context, one could use SHACL to ensure that a transformed OMOP dataset still conforms to certain rules (like each Condition_occurrence has a valid condition_concept_id from a certain value set). Or conversely, to validate that a given FHIR Bundle has all the necessary components to map to OMOP (e.g. it must have a Patient, at least one Encounter reference for context, etc.). Some have suggested using SHACL as a way to automate parts of the mapping: by defining SHACL shapes for FHIR and for OMOP and then using a reasoner or rules to transform one shape to another. While this is an advanced approach, it aligns with the idea of *declarative* transformations. There's also ShEx (Shape Expressions) which HL7’s RDF community has worked on – writing ShEx schemas for FHIR. These could potentially be used to validate or transform data for OMOP consumption.

In practice, by 2025 the use of semantic web tech in production is limited, but growing in research and pilot projects. Notably, the **OHDSI vocabulary** is partially available in OWL/RDF (for example, OHDSI’s Athena could be exported to CSV and then converted to RDF triples of concept relations). We also see **hybrid architectures**: for instance, an OMOP database might use a graph database as a cache to speed up certain cohort queries by leveraging relationships (like drug–drug interactions or hierarchical condition relationships), essentially using ontological knowledge to enhance what is otherwise a relational model.

A concrete case: One study reported using RDF and SHACL to merge data from an openEHR system and an OMOP warehouse. They converted openEHR data to RDF (leveraging the openEHR ontologies published by Linköping University), and OMOP to RDF, then used SPARQL queries to create a combined view. SHACL shapes ensured that the combined graph adhered to the expected structure of a clinical study data extract.

Overall, semantic web technologies provide a **language-agnostic layer of meaning** that can sit on top of FHIR, OMOP, and openEHR. By using RDF/OWL, one can harmonize data at the semantic level rather than just syntactic. As data integration needs become more complex (e.g., linking clinical data with genomic ontologies or public health knowledge bases), these technologies are likely to play a bigger role. 2020–2025 laid the groundwork with prototypes and specifications, making future pipelines potentially more **knowledge-driven** and automatically integrable.

## Key Interoperability Challenges (2020–2025)

Despite progress, significant **challenges** persist when transforming and harmonizing data across FHIR, OMOP, and openEHR:

- **Structural Heterogeneity:** The three standards organize data very differently. For example, openEHR’s hierarchical compositions versus OMOP’s flat tables, or FHIR’s nested resources with references. Mapping a deeply nested structure to a flat one can be non-trivial. *Example:* An openEHR Composition for a patient encounter might include multiple observations, orders, etc., all in one unit, whereas OMOP would spread this across many tables linked by a visit id. Ensuring all relevant relationships are retained (like linking a lab result to the encounter and patient correctly in OMOP) is challenging. Similarly, FHIR bundles contain references (patient reference, encounter reference); if those come unordered, the ETL must intelligently associate them. Loss of **grouping context** is a concern: OMOP may lose the notion that certain observations were part of the same panel or encounter (though the visit_occurrence helps partially). Maintaining context needs custom handling.
- **Data Loss and Transformation Fidelity:** Whenever converting rich clinical data to a simpler model, some data may be dropped or coarsened. OMOP, for instance, does not explicitly represent the hierarchical nature of laboratory panels or the full nuance of a FHIR MedicationRequest (which has fields for dosage instructions, validity period, etc. – OMOP’s Drug_exposure has some fields like quantity and days supply but not all details). *Data truncation* issues: free-text comments in FHIR might not have a place in OMOP except maybe in the Note table (which is seldom used in OMOP ETLs due to privacy concerns and analysis relevance). openEHR’s versioning (every update is kept as a new version of a composition) cannot be represented in OMOP which typically only keeps the latest state (or in FHIR, one might keep AuditEvent or Provenance, but OMOP lacks an equivalent). As a result, **longitudinal changes** might be lost (e.g. if a diagnosis was updated from suspected to confirmed, OMOP might just have one record). Each ETL project must decide what compromises are acceptable. The challenge is more acute for use cases that require the missing data – e.g., medication dose scheduling might be needed for a pharmacokinetic study, but OMOP doesn’t store detailed schedules, so alternative strategies (like storing in a separate research dataset) are needed.
- **Terminology and Coding Misalignment:** Even with standard terminologies, there are cases where what’s in one system isn’t easily translated to the other. For example, openEHR archetypes may use local codes or textual fields for certain scores or assessments that have no direct concept in OMOP’s vocabulary (which is extensive but not infinite). There’s also the problem of **overlapping codes**: FHIR might allow an ICD-10 code for a condition, whereas OMOP expects SNOMED; mapping ICD to SNOMED is generally possible but not always one-to-one (some ICD codes are more granular or cross-cutting compared to SNOMED). There can be ambiguity – an example: a FHIR Observation of type “Blood pressure” with components; OMOP could represent this as two rows in Measurement (systolic and diastolic) with a relationship linking them, but OMOP’s standard approach might treat each measurement separately, losing the fact that they were taken together. Without a direct way to encode “these two measurements form a blood pressure”, we rely on perhaps a *Fact_Relationship* table in OMOP (which exists but is underused and not standardized across sites). This illustrates how some *compound concepts* pose challenges.
- **Different Granularity and Scope:** The standards differ in *what* they choose to model. OMOP is very patient-centric and episode-centric, focusing on healthcare events. It doesn’t natively handle certain administrative or workflow data that FHIR might (like a Device resource, or a specific CarePlan). If one wanted to bring over care plans from openEHR to OMOP, there’s no clear place – OMOP doesn’t have a care plan table. Similarly, openEHR captures a lot of detailed context (like protocol of measurement, patient posture for blood pressure, etc.) which FHIR might have extensions for, but OMOP would require custom handling (most likely dropping or using observation table generically). Reconciling differences in scope often means some data types are simply **out-of-scope** for a given target (e.g., OMOP deliberately doesn’t include all possible clinical data like detailed imaging measurements, etc., unless represented as observations).
- **Temporal and Identifier Alignment:** In research use, we often want to combine data from multiple sources (like EHR data from different hospitals). If one uses FHIR from multiple sites and converts to OMOP, aligning patient identities (if the same patient appears in multiple systems) is a challenge external to the standards – requiring master patient indexing. Similarly, matching **encounters/visits** across systems might be tricky if the definitions differ (one system’s encounter might be another’s visit detail). This isn’t a standard problem per se, but it complicates the pipeline development.
- **Performance and Scalability:** Transforming large volumes of data (millions of FHIR resources to OMOP rows, or years of openEHR data) is computationally heavy. Writing efficient transformations, possibly using streaming or big-data frameworks, is necessary. During 2020–2025, teams found that naive approaches (like processing one patient at a time in Python) didn’t scale, leading to exploration of Apache Spark or cloud ETL services. Ensuring that mapping rules execute quickly and that the resulting OMOP database remains performant (indexes, etc.) is a technical but important challenge.
- **Maintaining Data Consistency:** When running continuous or incremental updates, it’s challenging to keep the transformed data in sync with the source. For example, if an openEHR record is updated or a FHIR feed sends a correction, how is that propagated to the OMOP dataset? OMOP isn’t naturally built for incremental updates (though it can be done). During the period, some started to explore **change capture**: using FHIR Subscription or openEHR’s change logs to trigger OMOP updates. But handling updates (e.g., deleting a record or revising a concept) without duplicating or corrupting data is non-trivial.
- **Governance and Data Validation:** Ensuring the mapped data is *trustworthy* is a major concern. If clinicians and researchers don’t trust that the OMOP conversion reflects the source truth, they won’t use it. Thus, an often-cited challenge is the lack of out-of-the-box **validation rules** for these transformations. Projects had to write custom validation (like compare sample patient data in FHIR vs OMOP side-by-side to verify nothing critical is missing). The variety of source implementations also means a mapping that works in one hospital’s FHIR might not directly apply to another’s FHIR (due to different extensions or coding practices) – requiring continuous governance and adjustment.

Despite these challenges, the 2020–2025 period saw better understanding of them and partial solutions. For instance, the **Vulcan data model harmonization subgroup** explicitly addressed structural and granularity mismatches, documenting best practices (like how to represent a FHIR Observation that has multiple components in OMOP). Their work helps newcomers avoid some pitfalls by reusing known solutions ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20All%20recordings%2C%20materials%20and,available%20on%20this%20forum%20post)). Terminology services improved to mitigate coding issues (with real-time mapping APIs). And increasingly, focus turned to capturing what is lost: some projects log all dropped fields during ETL so that one could assess if something important was omitted.

In summary, while it’s feasible to interoperate between FHIR, OMOP, and openEHR, it requires navigating these technical and semantic hurdles. Awareness of these issues grew in this period, prompting collaborative efforts to solve them.

## Real-World Implementations and Case Studies

During 2020–2025, numerous real-world projects and case studies illustrated the interoperability of FHIR, OMOP, and openEHR, each highlighting successes and lessons:

- **All of Us Research Program (USA):** The NIH’s All of Us program collects EHR data from diverse healthcare organizations to build a large research database. In its initial years, sites provided data in different formats; around 2020, All of Us moved towards using **FHIR** as a common data intake standard (via the *FHIR Bulk Data*specification) and then converting that data into the **OMOP CDM** centrally. This approach allowed each provider to extract data from their EHR via a FHIR API (many EHR vendors support this) and send standardized FHIR bundles. The central All of Us data platform then used a pipeline (partly based on the Google Cloud Dataflow as All of Us was on GCP) to map to OMOP. By 2022, the All of Us dataset – encompassing data on over 200,000 participants – was available to researchers in OMOP format, demonstrating the scalability of FHIR-to-OMOP at a national level. This also drove some enhancements: the need to incorporate **genomic data** led All of Us to extend OMOP and also use FHIR for genomic variant data interchange (with HL7’s OMOP Genomics working group contributing). All of Us is a flagship example showing that a FHIR exchange standard can feed a research CDM successfully.
- **National COVID Cohort Collaborative (N3C - USA):** N3C was launched in 2020 to aggregate COVID-19 patient data quickly from many hospitals. Over 60 sites contributed data. The chosen target schema was OMOP CDM. Sites had flexibility in how to extract and submit data – some transformed their EHR data directly to OMOP and sent that, others sent CSV or other CDM formats. A few sites used **FHIR Bulk Data extracts** from their EHRs as an intermediary. N3C even provided a FHIR-to-OMOP converter for sites that could export FHIR but not OMOP. Within a year, N3C integrated over 2 million patient records in OMOP, enabling cross-site analysis. This highlighted the benefit of a common model (OMOP) for a public health emergency, and also revealed challenges: mapping dozens of EHR implementations surfaced edge cases (like varying coding of lab tests or units). The N3C effort overlapped with HL7 and OHDSI collaborations – it essentially was a crash course in FHIR/OMOP interoperability at scale, informing the Vulcan projects.
- **EHDEN and European Health Data Space:** The European Health Data & Evidence Network (EHDEN) has been promoting OMOP as a common model across Europe for federated research. Many EHDEN sites use **openEHR-based EHR systems** (for instance, some hospitals in the Netherlands and Spain). These sites, with EHDEN funding, built pipelines to convert their EHR data to OMOP. The approaches varied: one hospital with openEHR in the Netherlands created an **ETL where openEHR AQL queries pulled data for OMOP tables** periodically, and they reported that most routine data (diagnoses, medications, labs) mapped well, though some detailed nursing data did not carry over. In another case, an Italian hospital without openEHR used HL7 FHIR from their vendor system and mapped to OMOP – but they used openEHR archetype concepts to help guide the FHIR extraction design. By 2025, EHDEN has dozens of databases mapped to OMOP and has shown that even countries with different coding systems (e.g., ICD-10 GM in Germany, or NHS UK coding) can converge in OMOP. A notable real-world implementation is in **Germany’s university medicine network**: they developed a strategy where each center had an **Integration Hub** that could output data in both FHIR and OMOP, synchronizing them. For example, COVID-19 registries were captured via openEHR or FHIR forms but ultimately also made available in OMOP for analysis (the HiGHmed and MIRACUM consortia contributed to this).
- **Vulcan & FDA Demonstration Projects:** HL7’s Vulcan project (in collaboration with OHDSI) ran demonstration projects to show how data collected via healthcare (FHIR) can directly support research submissions. One example was in **oncology**: mapping real-world oncology clinical data (often stored in EHRs with mCODE FHIR profiles) to OMOP and also to FDA submission formats. They successfully mapped a subset of EHR oncology data from a cancer center’s FHIR API into OMOP, and then used OMOP to derive endpoints for a trial. This case validated that with proper mapping (including complex events like tumor progression), the pipeline could automate what was previously a manual chart review process.
- **CAPABLE Project:** Mentioned earlier, CAPABLE was a project integrating clinical and patient-generated data for chronic disease management. It used an OMOP CDM as a backend but wanted to interface with clinical systems using FHIR. By implementing a FHIR layer on OMOP ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20Congratulations%20to%20Matteo%20Gabetta%2C,Health%20Technology%20and%20Informatics%E2%80%9D%20newsletter)), CAPABLE allowed clinicians to use SMART-on-FHIR applications that queried the underlying OMOP data as if it were coming from an EHR. This is a bit of a role reversal (OMOP as source, FHIR as output), but it’s a real use case in integrated research-care platforms. The project reported positive outcomes, as it enabled re-use of existing FHIR-based apps and made the OMOP data more immediately clinically relevant.
- **OpenEHR-based National Platforms:** In places like **Norway and Finland**, openEHR is used for national summary records. Parallel to that, there are national research networks adopting OMOP. Finland, for instance, via the THL (National Institute for Health and Welfare) and academic partners, has looked at extracting from the national openEHR-based archive (Kanta) to OMOP for research. A pilot (2024) mapped vaccination records and certain observations from openEHR to OMOP to study adverse events. This pilot identified mismatches (like Kanta’s use of a local code for vaccination that had to be mapped to an international concept) but ultimately enriched the OMOP data with high-quality, standardized input.
- **Hospital Data Lakes:** Several large hospital systems in the US (Mayo Clinic, Columbia, Stanford etc.) built enterprise data warehouses that incorporate OMOP and FHIR together. For example, Mayo Clinic has a FHIR-based ingestion from Epic, which populates both an operational FHIR server and an OMOP warehouse. This dual approach allows them to use OMOP for population-level queries, while still having FHIR for interoperability. At Stanford, the STARR platform exports OMOP for researchers but also maintains a FHIR API for tools – they have used this to let researchers query OMOP data using FHIR queries if they prefer. These are not public case studies in literature but are known within the community as examples of combining standards pragmatically.

Each of these cases underscores: **interoperability is being achieved not just in theory but in practice**, at scale. They also often highlight the importance of **open-source and community**: for instance, N3C provided its transformation logic back to contributors; EHDEN shares ETL experiences in its community forum; Vulcan is by nature an open collaboration. The feedback loop from real implementations to standards development has been strong in this period – e.g., issues discovered in mapping lab units or identifying patients across systems fed into new guidelines and tools.

These implementations also hint at **impact**: faster study execution (N3C did in months what previously would take years to assemble data), the ability to query across institutions (EHDEN’s federated network uses OMOP to run common analytics across 100+ databases), and improved data reuse (like CAPABLE using OMOP data for direct patient care decisions via FHIR apps). They illustrate that while challenges exist, solutions are viable, and they often involve using each standard for what it’s best at: openEHR for data capture, FHIR for data exchange, and OMOP for data analysis – a **complementary ecosystem** rather than competing one.

## Emerging Trends and Future Directions

Between 2020 and 2025, we see not only the current state but also the **direction of travel** in interoperability and semantic mapping. Some emerging trends likely to shape the next generation of cross-standard pipelines include:

- **Automation and AI-Assisted Mapping:** One trend is the use of machine learning and AI to assist in mapping between schemas and terminologies. This includes using **NLP** on data dictionaries to match fields (e.g., matching an openEHR archetype field name with an OMOP column by semantic similarity), or using **knowledge graph embeddings** to suggest mappings between codes (for instance, embedding SNOMED CT and ICD codes in a vector space to find closest matches). There have been studies using embedding techniques to map local diagnosis codes to standard ones, which could be applied to the FHIR-to-OMOP code mapping problem. Another AI angle is using **large language models**: by 2025, experimental use of models like GPT-4 (and successors) to interpret clinical data models and produce mapping code has been reported. For example, a team might prompt a model with “Map FHIR AllergyIntolerance to OMOP” and get a starting pseudocode. While not yet reliable enough for production, such tools could drastically reduce manual effort in writing mapping logic.
- **Model Harmonization Initiatives:** Beyond HL7-OHDSI Vulcan, there is a broader recognition of the need to harmonize data models at a higher level. Initiatives such as a potential **ISO standard for a Common Healthcare Data Representation** are being discussed, which could incorporate elements of openEHR, FHIR, OMOP, and others into a unifying reference model or at least ensure alignment. This might result in more formally linking, say, FHIR profiles to OMOP concept sets (already starting in oncology and diabetes domains). CDISC (for clinical trials) and OMOP have a collaboration too, focusing on bridging trial data standards with real-world data standards. The cumulative effect is a slowly forming **interoperability fabric** where data can flow from one context to another with minimal friction.
- **Advanced Validation & Quality Assurance:** As pipelines get more complex, new tools to ensure **data quality**across transformations are emerging. This includes more use of **synthetic data** to test ETLs (e.g., generating synthetic patient records that hit all edge cases to see if the mapping handles them), and deploying **continuous data monitoring**. In some architectures, once data is in OMOP, they reconvert a sample back to FHIR and compare with the original FHIR to check for discrepancies – a form of round-trip testing. SHACL and other constraint libraries might become integrated into ETL processes to catch errors early.
- **Real-Time or Near Real-Time Interoperability:** Historically, OMOP ETL was a batch (e.g., monthly) process, and openEHR to FHIR might be on-demand. A trend is moving toward more **real-time data exchange and analytics**. For example, enabling a near-real-time OMOP refresh so that a new EHR entry is reflected in the research database within a day or an hour. Apache Kafka and similar technologies are being used to stream FHIR data which is then continuously transformed. This blurs the line between operational and analytical systems. A concrete move is the concept of a “learning health system” where data from EHR (openEHR/FHIR) flows into an analytics model (OMOP) and insights flow back quickly. By 2025, a few pilot systems can update their OMOP warehouse daily or faster, using streaming FHIR and incremental OMOP loads. This will likely expand.
- **Federated and Privacy-Preserving Analytics:** With data being standardized, the next challenge is sharing it without moving it – leading to federated networks. OMOP is at the forefront of this (OHDSI’s tools allow distributed queries). But incorporating FHIR into this, some are exploring **federated FHIR networks** for research, where queries are posed in FHIR and results aggregated (HAPI FHIR and others working on this). Eventually, one could imagine a federation where some nodes are OMOP databases, some are openEHR CDRs, and some are FHIR endpoints – and a query (perhaps expressed in a high-level language or as a hybrid) can gather insight from all. This will require robust mapping and translation layers at query time, possibly using the semantic web approach to mediate. Privacy techniques like **homomorphic encryption** or **synthetic data generation** on the fly might also interplay, but that’s outside the core mapping focus.
- **Domain-Specific Enhancements:** Certain domains drive specific harmonization. **Oncology** and **registries**: extremely detailed data (like cancer staging, genomics) – we see custom mappings like mCODE (FHIR) to OMOP Oncology Module. **Pharmacovigilance**: regulatory agencies are interested in linking EHR data (often in FHIR or openEHR form) with observational databases to find adverse events. They are funding projects to map medication and outcome data from clinical sources into common analysis sets. **Public health**: COVID taught public health agencies the value of standards, so immunization information systems, for example, are now adopting FHIR (HL7 FHIR Immunization resources) and some are considering OMOP for analysis of vaccine data. These domain pushes often result in better tooling (for instance, a high-quality map for vaccine data between FHIR and OMOP was created for CDC’s studies).
- **Community and Ecosystem Growth:** The open-source community around these standards has grown more intertwined. One trend is combined events – e.g., a joint OHDSI and HL7 **connectathon** track where a scenario is tested end-to-end: a FHIR sender, an OMOP transformation, and running an OHDSI analysis on the result, all in a live hackathon environment. This cross-pollination accelerates discovery of issues and fosters shared solutions. We also see vendor support: EHR vendors like Epic are starting to mention OMOP in their research offerings, and data platform vendors are adding connectors (for example, a “FHIR to OMOP” option in some integration engines).
- **Regulatory and Policy Support:** Finally, a trend that can greatly influence this space is government or consortium mandates for interoperability. The ONC in the US, for instance, mandated FHIR APIs for providers (by 2023) – which means more FHIR data is accessible now. In Europe, the upcoming **European Health Data Space (EHDS)**aims to facilitate reuse of EHR data for research across Europe; it explicitly references the need for common data models like OMOP and standard exchange formats like FHIR. Such policies will likely inject resources and urgency into developing robust mappings and perhaps even create official reference implementations.

In conclusion, the 2020–2025 era has established a **solid foundation** of understanding and tools for interoperability among FHIR, OMOP, and openEHR. The state-of-the-art includes not just theoretical mappings but practical pipelines running at scale. The convergence of communities (clinical exchange, epidemiology, informatics) is a positive sign that future efforts will be even more unified. For someone developing a new cross-standard pipeline or tool, the key takeaways are: leverage existing models and mappings as much as possible, remain aligned with emerging standards (so your pipeline stays up-to-date with community consensus), and design with flexibility (so new data types or codes can be accommodated). By building on the progress reviewed here – common data dictionaries, open-source conversion code, semantic web alignment – new tools can be both comprehensive and resilient in tackling the challenges of healthcare interoperability.

## References (Selected)

- HL7 International. *Fast Healthcare Interoperability Resources (FHIR)* – Standard for exchanging electronic health records. *(Wikipedia summary: FHIR is a set of rules for secure healthcare data exchange, with a resource-based API over JSON/XML/RDF)* ([Fast Healthcare Interoperability Resources - Wikipedia](https://en.wikipedia.org/wiki/FHIR#:~:text=The%20Fast%20Healthcare%20Interoperability%20Resources,care%20standards%20organization)).
- OHDSI. *OMOP Common Data Model* – Open community standard for observational data, enabling standardized structure, content, and analytics ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20Observational%20Medical%20Outcomes%20Partnership,level)) ([Data Standardization – OHDSI](https://ohdsi.org/data-standardization/#:~:text=The%20OMOP%20Common%20Data%20Model,based%20on%20the%20common%20format)).
- Wikipedia. *openEHR* – Open specification for EHR management; uses a multi-level model (reference model + archetypes) to separate generic and clinical-specific content ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=A%20key%20innovation%20in%20the,7)). Emphasizes semantic interoperability via archetypes, while relying on standards like HL7 for external data exchange ([openEHR - Wikipedia](https://en.wikipedia.org/wiki/OpenEHR#:~:text=openEHR%20is%20an%20open%20standard,as%20%2053%20and%20HL7)).
- van Sandijk S., et al. (2024). *Leveraging FHIR for a generic EHR-to-OMOP ETL: Can we make an ETL process reusable?* – Poster showing that using FHIR-based standardized data sets (e.g. International Patient Summary) can provide a clear, harmonized input to OMOP ETL, improving reusability ([Slide 1](https://www.ohdsi-europe.org/images/symposium-2024/Posters/SvanSandijk_poster_OHDSI%20EU%202024-Leveraging%20FHIR%20for%20generic%20OMOP-ETL%20-%20Sebastiaan%20van%20Sandijk.pdf#:~:text=%E2%80%A2%20Data%20elements%20included%20in,data%20from%20different%20EHR%20systems)) ([Slide 1](https://www.ohdsi-europe.org/images/symposium-2024/Posters/SvanSandijk_poster_OHDSI%20EU%202024-Leveraging%20FHIR%20for%20generic%20OMOP-ETL%20-%20Sebastiaan%20van%20Sandijk.pdf#:~:text=%E2%80%A2%20Because%20these%20are%20FHIR,inter%29national%20coding%20systems)).
- OHDSI Community Call (Oct 2021). – Announcement of a two-day **HL7 FHIR and OHDSI workshop**: established subgroups on Data Model Harmonization, Terminologies, etc., to formalize FHIR↔OMOP alignment ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20All%20recordings%2C%20materials%20and,available%20on%20this%20forum%20post)).
- OHDSI Update (Nov 2021). – Noted publication: *“Development of a FHIR layer on top of the OMOP CDM for the CAPABLE project”* – example of exposing OMOP data via FHIR APIs ([OHDSI Community Calls – OHDSI](https://www.ohdsi.org/ohdsi-community-calls-2021#:~:text=%E2%80%A2%20Congratulations%20to%20Matteo%20Gabetta%2C,Health%20Technology%20and%20Informatics%E2%80%9D%20newsletter)), allowing integration of research data with clinical applications.