# Development and Testing Environments for FHIR to OMOP Transformation Pipeline

This document provides detailed guidance on setting up development and testing environments for the FHIR to OMOP transformation pipeline, including local development setup, testing strategies, and deployment workflows.

## 1. Local Development Environment

### 1.1 Docker-based Development Environment

The following Docker Compose configuration provides a complete local development environment with all necessary components:

```yaml
# docker-compose.yml for local development
version: '3.8'
services:
  # FHIR Server
  hapi-fhir-server:
    image: hapiproject/hapi:latest
    ports:
      - "8080:8080"
    environment:
      - hapi.fhir.default_encoding=json
      - hapi.fhir.bulk_export_enabled=true
      - spring.datasource.url=**********************************************
      - spring.datasource.username=admin
      - spring.datasource.password=admin
    volumes:
      - hapi-data:/data/hapi
    depends_on:
      - hapi-fhir-postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]
      interval: 30s
      timeout: 10s
      retries: 5

  hapi-fhir-postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=hapi
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin
    volumes:
      - hapi-postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d hapi"]
      interval: 10s
      timeout: 5s
      retries: 5

  # OMOP CDM Database
  omop-postgres:
    image: postgres:13
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=omop_cdm
      - POSTGRES_USER=omop_admin
      - POSTGRES_PASSWORD=omop_password
    volumes:
      - omop-postgres-data:/var/lib/postgresql/data
      - ./sql/omop_cdm_schema.sql:/docker-entrypoint-initdb.d/01_omop_cdm_schema.sql
      - ./sql/omop_vocabulary_load.sql:/docker-entrypoint-initdb.d/02_omop_vocabulary_load.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omop_admin -d omop_cdm"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Jupyter Notebook for development and testing
  jupyter:
    image: jupyter/datascience-notebook:python-3.9
    ports:
      - "8888:8888"
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./data:/home/<USER>/data
    environment:
      - JUPYTER_ENABLE_LAB=yes
    command: start-notebook.sh --NotebookApp.token='' --NotebookApp.password=''

  # Adminer for database management
  adminer:
    image: adminer:latest
    ports:
      - "8081:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=omop-postgres
      - ADMINER_DESIGN=pappu687

volumes:
  hapi-data:
  hapi-postgres-data:
  omop-postgres-data:
```

### 1.2 Directory Structure

```
fhir-to-omop/
├── data/                      # Data files
│   ├── fhir/                  # FHIR resource examples
│   ├── omop/                  # OMOP reference data
│   └── vocabulary/            # Vocabulary files
├── notebooks/                 # Jupyter notebooks for development
│   ├── 01_fhir_exploration.ipynb
│   ├── 02_mapping_development.ipynb
│   └── 03_transformation_testing.ipynb
├── sql/                       # SQL scripts
│   ├── omop_cdm_schema.sql    # OMOP CDM schema definition
│   ├── omop_vocabulary_load.sql # Vocabulary loading scripts
│   └── validation_queries.sql # Validation queries
├── src/                       # Source code
│   ├── etl/                   # ETL modules
│   ├── mapping/               # Mapping definitions
│   ├── validation/            # Validation scripts
│   └── utils/                 # Utility functions
├── tests/                     # Test files
│   ├── unit/                  # Unit tests
│   ├── integration/           # Integration tests
│   └── fixtures/              # Test fixtures
├── docker-compose.yml         # Docker Compose configuration
├── .env                       # Environment variables
└── README.md                  # Project documentation
```

### 1.3 Environment Setup Script

```bash
#!/bin/bash
# setup_dev_environment.sh

# Create directory structure
mkdir -p data/{fhir,omop,vocabulary}
mkdir -p notebooks
mkdir -p sql
mkdir -p src/{etl,mapping,validation,utils}
mkdir -p tests/{unit,integration,fixtures}

# Download OMOP CDM schema
curl -o sql/omop_cdm_schema.sql https://raw.githubusercontent.com/OHDSI/CommonDataModel/v5.4.0/PostgreSQL/OMOP_CDM_postgresql_5.4_ddl.sql

# Create sample environment file
cat > .env << EOL
# FHIR Server Configuration
FHIR_SERVER_URL=http://localhost:8080/fhir
FHIR_SERVER_USERNAME=admin
FHIR_SERVER_PASSWORD=admin

# OMOP Database Configuration
OMOP_DB_HOST=localhost
OMOP_DB_PORT=5432
OMOP_DB_NAME=omop_cdm
OMOP_DB_USERNAME=omop_admin
OMOP_DB_PASSWORD=omop_password

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=etl.log
EOL

# Create environment.yml
cat > environment.yml << EOL
name: fhir-omop_env
channels:
  - conda-forge
  - defaults
dependencies:
  # Core Python and data processing
  - python=3.11
  - pip
  - numpy
  - pandas
  - sqlalchemy
  - psycopg2
  - requests
  - python-dotenv

  # Testing
  - pytest
  - pytest-cov

  # Jupyter and interactive computing
  - ipykernel
  - jupyter
  - jupyterlab>=3
  - ipywidgets>=7.6
  - nbformat

  # Visualization
  - matplotlib
  - seaborn
  - plotly

  # External integrations
  - openjdk=11
  - pip:
    - fhir-core==1.0.1
    - fhir.resources==8.0.0
    - python-dateutil
    - typing-extensions
    - typing-inspection
EOL

# Create Conda environment
conda env create -f environment.yml
conda activate fhir-omop_env

# Create README.md
cat > README.md << EOL
# FHIR to OMOP Transformation Pipeline

This repository contains a pipeline for transforming FHIR resources to OMOP CDM.

## Setup

1. Install Docker and Docker Compose
2. Run \`docker-compose up -d\`
3. Access Jupyter Lab at http://localhost:8888
4. Access HAPI FHIR server at http://localhost:8080
5. Access Adminer at http://localhost:8081

## Development

1. Activate the Conda environment:
   ```bash
   conda activate fhir-omop_env
   ```

2. See the notebooks directory for development examples.
EOL

echo "Development environment setup complete!"
```

## 2. Testing Strategies

### 2.1 Unit Testing

Unit tests should be implemented for all transformation components using pytest:

```python
# tests/unit/test_patient_mapper.py
import pytest
from src.mapping.patient_mapper import map_patient_to_person

def test_map_patient_to_person():
    # Sample FHIR Patient resource
    fhir_patient = {
        "resourceType": "Patient",
        "id": "example",
        "gender": "male",
        "birthDate": "1974-12-25",
        "name": [
            {
                "use": "official",
                "family": "Chalmers",
                "given": ["Peter", "James"]
            }
        ]
    }

    # Map to OMOP Person
    person = map_patient_to_person(fhir_patient)

    # Assertions
    assert person["person_id"] is not None
    assert person["gender_concept_id"] == 8507  # Male concept ID
    assert person["birth_datetime"] == "1974-12-25T00:00:00"
    assert person["person_source_value"] == "example"
```

### 2.2 Integration Testing

Integration tests should verify the end-to-end transformation process:

```python
# tests/integration/test_patient_transformation.py
import pytest
import requests
import psycopg2
from src.etl.patient_transformer import transform_patients

def test_patient_transformation(fhir_server, omop_database):
    # Create test patient in FHIR server
    patient_data = {
        "resourceType": "Patient",
        "gender": "female",
        "birthDate": "1985-08-01",
        "name": [
            {
                "use": "official",
                "family": "Smith",
                "given": ["Jane"]
            }
        ]
    }

    response = requests.post(
        f"{fhir_server}/Patient",
        json=patient_data,
        headers={"Content-Type": "application/fhir+json"}
    )
    patient_id = response.json()["id"]

    # Run transformation
    transform_patients(fhir_server, omop_database)

    # Verify data in OMOP database
    conn = psycopg2.connect(omop_database)
    cursor = conn.cursor()
    cursor.execute(
        "SELECT * FROM person WHERE person_source_value = %s",
        (patient_id,)
    )
    result = cursor.fetchone()

    # Assertions
    assert result is not None
    assert result[3] == 8532  # Female concept ID

    # Clean up
    requests.delete(f"{fhir_server}/Patient/{patient_id}")
    cursor.execute(
        "DELETE FROM person WHERE person_source_value = %s",
        (patient_id,)
    )
    conn.commit()
    cursor.close()
    conn.close()
```

### 2.3 Data Quality Testing

Data quality tests should verify the correctness and completeness of transformed data:

```python
# tests/integration/test_data_quality.py
import pytest
import pandas as pd
from sqlalchemy import create_engine

def test_person_data_quality(omop_connection_string):
    # Connect to OMOP database
    engine = create_engine(omop_connection_string)

    # Check for missing values in required fields
    query = """
    SELECT COUNT(*) as missing_count
    FROM person
    WHERE gender_concept_id IS NULL
       OR year_of_birth IS NULL
       OR person_id IS NULL
    """
    result = pd.read_sql(query, engine)
    assert result.iloc[0]['missing_count'] == 0

    # Check for valid concept IDs
    query = """
    SELECT COUNT(*) as invalid_count
    FROM person p
    LEFT JOIN concept c ON p.gender_concept_id = c.concept_id
    WHERE c.concept_id IS NULL
    """
    result = pd.read_sql(query, engine)
    assert result.iloc[0]['invalid_count'] == 0
```

### 2.4 Performance Testing

Performance tests should measure the throughput and latency of the transformation pipeline:

```python
# tests/performance/test_transformation_performance.py
import pytest
import time
import pandas as pd
from src.etl.batch_transformer import transform_batch

def test_transformation_throughput(large_test_dataset, omop_database):
    # Measure time to transform a large batch of resources
    start_time = time.time()
    transform_batch(large_test_dataset, omop_database)
    end_time = time.time()

    # Calculate throughput
    duration = end_time - start_time
    resource_count = len(large_test_dataset)
    throughput = resource_count / duration  # resources per second

    # Log results
    print(f"Transformed {resource_count} resources in {duration:.2f} seconds")
    print(f"Throughput: {throughput:.2f} resources/second")

    # Assert minimum throughput
    assert throughput >= 100  # Adjust based on requirements
```

## 3. Continuous Integration and Deployment

### 3.1 GitHub Actions Workflow

```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_DB: omop_cdm_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      fhir-server:
        image: hapiproject/hapi:latest
        ports:
          - 8080:8080
        options: >-
          --health-cmd "curl -f http://localhost:8080/fhir/metadata || exit 1"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5

    steps:
    - uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'

    - name: Set up Conda
      uses: conda-incubator/setup-miniconda@v2
      with:
        auto-update-conda: true
        python-version: '3.11'
        activate-environment: fhir-omop_env
        environment-file: environment.yml

    - name: Initialize test database
      run: |
        psql -h localhost -U test_user -d omop_cdm_test -f sql/omop_cdm_schema.sql
      env:
        PGPASSWORD: test_password

    - name: Run tests
      run: |
        pytest --cov=src tests/
      env:
        FHIR_SERVER_URL: http://localhost:8080/fhir
        OMOP_DB_HOST: localhost
        OMOP_DB_PORT: 5432
        OMOP_DB_NAME: omop_cdm_test
        OMOP_DB_USERNAME: test_user
        OMOP_DB_PASSWORD: test_password

    - name: Upload coverage report
      uses: codecov/codecov-action@v1

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Login to DockerHub
      uses: docker/login-action@v1
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Build and push
      uses: docker/build-push-action@v2
      with:
        context: .
        push: true
        tags: username/fhir-to-omop:latest
```

### 3.2 AWS Deployment Pipeline

```yaml
# .github/workflows/aws-deploy.yml
name: Deploy to AWS

on:
  release:
    types: [created]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: fhir-to-omop
        IMAGE_TAG: ${{ github.event.release.tag_name }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: Deploy to ECS
      run: |
        aws ecs update-service --cluster fhir-omop-cluster --service fhir-omop-service --force-new-deployment
```

### 3.3 Azure Deployment Pipeline

```yaml
# .github/workflows/azure-deploy.yml
name: Deploy to Azure

on:
  release:
    types: [created]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Login to Azure
      uses: azure/login@v1
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Build and push to Azure Container Registry
      uses: azure/docker-login@v1
      with:
        login-server: ${{ secrets.ACR_LOGIN_SERVER }}
        username: ${{ secrets.ACR_USERNAME }}
        password: ${{ secrets.ACR_PASSWORD }}

    - run: |
        docker build -t ${{ secrets.ACR_LOGIN_SERVER }}/fhir-to-omop:${{ github.event.release.tag_name }} .
        docker push ${{ secrets.ACR_LOGIN_SERVER }}/fhir-to-omop:${{ github.event.release.tag_name }}

    - name: Deploy to Azure Container Instances
      uses: azure/aci-deploy@v1
      with:
        resource-group: fhir-omop-rg
        dns-name-label: fhir-omop-${{ github.event.release.tag_name }}
        image: ${{ secrets.ACR_LOGIN_SERVER }}/fhir-to-omop:${{ github.event.release.tag_name }}
        registry-login-server: ${{ secrets.ACR_LOGIN_SERVER }}
        registry-username: ${{ secrets.ACR_USERNAME }}
        registry-password: ${{ secrets.ACR_PASSWORD }}
        name: fhir-omop-container
        location: 'eastus'
```

### 3.4 GCP Deployment Pipeline

```yaml
# .github/workflows/gcp-deploy.yml
name: Deploy to GCP

on:
  release:
    types: [created]

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v0
      with:
        project_id: ${{ secrets.GCP_PROJECT_ID }}
        service_account_key: ${{ secrets.GCP_SA_KEY }}
        export_default_credentials: true

    - name: Build and push to Google Container Registry
      run: |
        gcloud auth configure-docker
        docker build -t gcr.io/${{ secrets.GCP_PROJECT_ID }}/fhir-to-omop:${{ github.event.release.tag_name }} .
        docker push gcr.io/${{ secrets.GCP_PROJECT_ID }}/fhir-to-omop:${{ github.event.release.tag_name }}

    - name: De
(Content truncated due to size limit. Use line ranges to read in chunks)