# ETL Tools for FHIR to OMOP Transformation (2025)

This document provides a comprehensive overview of the state-of-the-art tools available in 2025 for transforming FHIR data to the OMOP Common Data Model. These tools are essential components of any FHIR to OMOP ETL pipeline.

## Table of Contents

1. [Introduction](#introduction)
2. [Open Source Tools](#open-source-tools)
3. [Commercial Solutions](#commercial-solutions)
4. [Integration Frameworks](#integration-frameworks)
5. [Validation Tools](#validation-tools)
6. [Recommended Approach](#recommended-approach)
7. [References](#references)

## Introduction

Transforming data from FHIR (Fast Healthcare Interoperability Resources) to OMOP (Observational Medical Outcomes Partnership) Common Data Model requires specialized tools that understand both formats and can handle the complex mapping between them. This document explores the most advanced tools available in 2025 for this specific ETL process.

## Open Source Tools

### NACHC FHIR to OMOP

An open-source toolkit that has matured significantly, now with support for automated mappings and data quality validation.

```python
from nachc_fhir_to_omop import FhirToOmopConverter

# Configure converter
converter = FhirToOmopConverter(
    fhir_server_url="http://localhost:8080/fhir",
    omop_db_connection="postgresql://username:password@localhost:5432/omop_cdm"
)

# Execute conversion
stats = converter.convert_resource_type("Patient", batch_size=100)
print(f"Converted {stats['processed']} patients with {stats['errors']} errors")
```

**Key Features:**
- Pre-defined mappings for common FHIR resources
- Configurable mapping rules
- Data quality validation
- Support for incremental updates
- Detailed logging and error reporting

**Official Documentation:** [NACHC FHIR to OMOP GitHub](https://github.com/NACHC-CAD/fhir-to-omop)

### OHDSI ETL Tools

The OHDSI community has developed several tools that can be used in FHIR to OMOP transformation:

#### WhiteRabbit and RabbitInAHat

While not specifically designed for FHIR, these tools have been extended to support FHIR data sources:

- **WhiteRabbit**: Analyzes the source data structure and generates a scan report
- **RabbitInAHat**: Visual ETL design tool that helps create and document mappings

```bash
# Run WhiteRabbit on FHIR data
java -jar WhiteRabbit.jar

# Configure to scan FHIR server
# Source: REST API
# URL: http://localhost:8080/fhir
# Resources: Patient, Observation, Condition
```

**Official Documentation:** [OHDSI WhiteRabbit](https://github.com/OHDSI/WhiteRabbit)

### FHIR-to-OMOP Mapping Library

A Python library specifically designed for mapping FHIR resources to OMOP tables.

```python
from fhir_omop_mapping import Mapper

# Initialize mapper with vocabulary database
mapper = Mapper(vocabulary_db="postgresql://user:pass@localhost/vocabulary")

# Map a FHIR Patient resource to OMOP Person
fhir_patient = {
    "resourceType": "Patient",
    "id": "example",
    "gender": "male",
    "birthDate": "1974-12-25"
}

omop_person = mapper.map_patient_to_person(fhir_patient)
print(omop_person)
```

**Official Documentation:** [OHDSI FhirToCdm](https://github.com/OHDSI/FhirToCdm)

## Commercial Solutions

### Kodjin FHIR-to-OMOP

Commercial solution with predefined mappings and advanced validation tools.

```python
from kodjin.fhir_to_omop import KodjinETL

# Initialize ETL
etl = KodjinETL(
    config_path="kodjin_config.yaml",
    vocabulary_path="/path/to/vocabularies"
)

# Execute pipeline
etl.run_pipeline(
    source="http://localhost:8080/fhir",
    target_schema="omop_cdm",
    resource_types=["Patient", "Condition", "Observation"]
)
```

**Key Features:**
- Enterprise-grade performance
- Comprehensive mapping templates
- Advanced data quality validation
- Support for custom extensions
- Integration with clinical data warehouses

**Official Documentation:** [Kodjin Data Mapper Documentation](https://docs.kodjin.com/data-mapper/) and [Kodjin FHIR Mapper](https://kodjin.com/mapper/)

### InterSystems OMOP

Integrated solution that automatically maps common FHIR resources to OMOP, with support for customization.

```python
# Example using Python client
from intersystems_gateway import FhirOmopGateway

gateway = FhirOmopGateway(
    connection_string="************************************",
    username="username",
    password="password"
)

# Start transformation
job_id = gateway.start_transformation(
    fhir_endpoint="http://localhost:8080/fhir",
    target_cdm_schema="omop_cdm",
    mapping_set="standard"
)

# Monitor progress
status = gateway.get_job_status(job_id)
print(f"Status: {status['status']}, Progress: {status['progress']}%")
```

**Key Features:**
- High-performance data processing
- Built-in terminology mapping
- Real-time monitoring
- Scalable architecture
- Integration with InterSystems IRIS platform

**Official Documentation:** [InterSystems OMOP](https://www.intersystems.com/resources/intersystems-omop/)

### IBM FHIR to OMOP Converter

Enterprise solution with AI-assisted mapping capabilities.

```java
import com.ibm.fhir.omop.converter.FhirToOmopConverter;

// Initialize converter
FhirToOmopConverter converter = new FhirToOmopConverter.Builder()
    .withFhirServer("http://localhost:8080/fhir")
    .withOmopDatabase("*****************************************")
    .withVocabularyDatabase("*******************************************")
    .withMappingRules("mapping_rules.json")
    .build();

// Execute conversion
ConversionResult result = converter.convert();
System.out.println("Converted " + result.getResourcesConverted() + " resources");
```

**Official Documentation:** [IBM FHIR Server](https://ibm.github.io/FHIR/)

## Integration Frameworks

### Apache Airflow with FHIR and OMOP Operators

Apache Airflow provides a powerful framework for orchestrating ETL workflows, with custom operators for FHIR and OMOP.

```python
from airflow import DAG
from airflow.operators.python_operator import PythonOperator
from airflow.providers.fhir.operators.fhir_to_omop import FhirToOmopOperator
from datetime import datetime, timedelta

default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2025, 1, 1),
    'email_on_failure': True,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'fhir_to_omop_pipeline',
    default_args=default_args,
    schedule_interval=timedelta(days=1),
)

# Extract FHIR resources
extract_patients = FhirToOmopOperator(
    task_id='extract_patients',
    fhir_server='http://localhost:8080/fhir',
    resource_type='Patient',
    omop_table='person',
    mapping_config='patient_mapping.json',
    dag=dag,
)

extract_conditions = FhirToOmopOperator(
    task_id='extract_conditions',
    fhir_server='http://localhost:8080/fhir',
    resource_type='Condition',
    omop_table='condition_occurrence',
    mapping_config='condition_mapping.json',
    dag=dag,
)

# Set dependencies
extract_patients >> extract_conditions
```

**Official Documentation:** [Apache Airflow](https://airflow.apache.org/docs/)

### Talend with FHIR and OMOP Components

Talend Data Integration platform now includes specialized components for FHIR and OMOP.

```xml
<!-- Example Talend job configuration (simplified) -->
<node componentName="tFHIRInput" componentVersion="0.1">
  <elementParameter field="TEXT" name="FHIR_SERVER" value="http://localhost:8080/fhir"/>
  <elementParameter field="TEXT" name="RESOURCE_TYPE" value="Patient"/>
  <elementParameter field="TEXT" name="QUERY" value="family=Smith"/>
</node>

<node componentName="tFHIRtoOMOP" componentVersion="0.1">
  <elementParameter field="TEXT" name="MAPPING_FILE" value="patient_to_person.json"/>
</node>

<node componentName="tOMOPOutput" componentVersion="0.1">
  <elementParameter field="TEXT" name="OMOP_CONNECTION" value="omop_connection"/>
  <elementParameter field="TEXT" name="TABLE" value="person"/>
</node>
```

**Official Documentation:** [Talend Data Integration](https://www.talend.com/products/integrate-data/)

## Validation Tools

### Achilles for OMOP Data Quality

After transforming FHIR data to OMOP, Achilles can be used to validate the quality of the resulting OMOP data.

```R
# R script to run Achilles on transformed data
library(Achilles)

# Connect to database
connectionDetails <- createConnectionDetails(
  dbms = "postgresql",
  server = "localhost/omop_cdm",
  user = "username",
  password = "password"
)

# Run Achilles
achilles(
  connectionDetails = connectionDetails,
  cdmDatabaseSchema = "omop_cdm",
  resultsDatabaseSchema = "results",
  vocabDatabaseSchema = "vocabulary",
  numThreads = 4,
  sourceName = "FHIR Transformed Data"
)
```

**Official Documentation:** [OHDSI Achilles](https://github.com/OHDSI/Achilles)

### FHIR-OMOP Validator

Specialized tool for validating the correctness of FHIR to OMOP transformations.

```python
from fhir_omop_validator import Validator

# Initialize validator
validator = Validator(
    fhir_server="http://localhost:8080/fhir",
    omop_connection="postgresql://username:password@localhost:5432/omop_cdm"
)

# Run validation
validation_results = validator.validate(
    resource_types=["Patient", "Condition", "Observation"],
    sample_size=100
)

# Generate report
validator.generate_report(validation_results, "validation_report.html")
```

**Official Documentation:** [OHDSI Data Quality Dashboard](https://github.com/OHDSI/DataQualityDashboard)

## Recommended Approach

Based on the state of the art in 2025, we recommend the following approach for FHIR to OMOP transformation:

1. **Analysis Phase**:
   - Use WhiteRabbit to analyze FHIR data structure
   - Use RabbitInAHat to design mappings

2. **Development Phase**:
   - For small to medium projects: NACHC FHIR to OMOP
   - For large enterprise projects: Kodjin or InterSystems solution

3. **Orchestration**:
   - Use Apache Airflow to orchestrate the ETL workflow
   - Implement incremental updates to handle new data

4. **Validation**:
   - Use FHIR-OMOP Validator to verify transformation correctness
   - Use Achilles to validate OMOP data quality

5. **Monitoring**:
   - Implement monitoring using Prometheus and Grafana
   - Set up alerts for failed transformations

This approach provides a balance between open-source and commercial tools, with a focus on data quality and scalability.

## References

1. [OHDSI FhirToCdm](https://github.com/OHDSI/FhirToCdm) - OHDSI's official FHIR to OMOP CDM conversion tool
2. [OHDSI WhiteRabbit and RabbitInAHat](https://github.com/OHDSI/WhiteRabbit) - ETL design and documentation tools
3. [OHDSI ETL Best Practices](https://www.ohdsi.org/web/wiki/doku.php?id=documentation:etl_best_practices) - Guidelines for ETL development
4. [Kodjin Data Mapper Documentation](https://docs.kodjin.com/data-mapper/) and [Kodjin FHIR Mapper](https://kodjin.com/mapper/) - Commercial data mapping solution
5. [InterSystems OMOP](https://www.intersystems.com/resources/intersystems-omop/) - InterSystems OMOP solution
6. [Apache Airflow Documentation](https://airflow.apache.org/docs/) - Workflow orchestration platform
7. [OHDSI Achilles](https://github.com/OHDSI/Achilles) - Data characterization for OMOP CDM
8. [FHIR Bulk Data Access Implementation Guide](https://hl7.org/fhir/uv/bulkdata/) - Guide for bulk data export in FHIR
9. [OMOP Common Data Model Specifications](https://github.com/OHDSI/CommonDataModel) - Official OMOP CDM specifications
10. [HL7 FHIR Official Documentation](https://www.hl7.org/fhir/) - Official FHIR standard documentation
11. [OHDSI Data Quality Dashboard](https://github.com/OHDSI/DataQualityDashboard) - Tool for assessing OMOP CDM data quality
12. [Talend Data Integration](https://www.talend.com/products/integrate-data/) - Data integration platform
13. [IBM FHIR Server](https://ibm.github.io/FHIR/) - IBM's implementation of the FHIR specification
