# ETL Documentation

This section contains documentation related to the Extract, Transform, Load (ETL) processes for converting FHIR data to the OMOP Common Data Model.

## Available Documentation

- [ETL Tools for FHIR to OMOP Transformation (2025)](fhir_to_omop_tools.md) - Comprehensive overview of state-of-the-art tools for transforming FHIR data to OMOP

## Directory Structure

```
etl/
├── README.md                  # This file
└── fhir_to_omop_tools.md      # ETL tools documentation
```

## Upcoming Documentation

Additional documentation is planned for:

- ETL process overview
- FHIR to OMOP mapping strategies
- Data transformation rules
- Validation and quality checks
- Performance optimization
- Incremental updates
- Error handling and logging

## Related Documentation

- [FHIR API Tools](../fhir/api/fhir_tools_2025.md) - Overview of tools for interacting with FHIR servers
- [OMOP Documentation](../omop/) - Documentation related to the OMOP Common Data Model
