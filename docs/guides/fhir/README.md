# FHIR Documentation

This section contains documentation related to the FHIR (Fast Healthcare Interoperability Resources) components of the project.

## Server Setup and Configuration

- [FHIR Server Setup Guide](server/setup.md) - Complete guide to setting up the HAPI FHIR server using Docker

## API Usage

- [FHIR Query Guide](api/fhir_query_guide.md) - Comprehensive guide on building effective FHIR queries
- [Terminology Services](api/terminology_services.md) - Examples of using FHIR terminology services
- [FHIR Tools (2025)](api/fhir_tools_2025.md) - State of the art tools for interacting with FHIR servers

## Data Loading and Performance

- [Data Loading Methods](data-loading/README.md) - Methods for loading data into the FHIR server
- [Performance Metrics](data-loading/performance-metrics.md) - Performance testing and optimization for FHIR data loading

## Directory Structure

```
fhir/
├── server/                     # FHIR server setup and configuration
│   └── setup.md                # FHIR server setup guide
├── api/                        # FHIR API usage examples
│   ├── README.md               # API documentation overview
│   ├── fhir_query_guide.md     # Guide on building effective FHIR queries
│   ├── terminology_services.md # Terminology services examples
│   └── fhir_tools_2025.md      # FHIR tools state of the art (2025)
├── data-loading/               # FHIR data loading documentation
│   ├── README.md               # Overview of data loading methods
│   ├── quick-reference.md      # Quick reference for data loading
│   ├── performance-metrics.md  # Performance metrics and testing guide
│   └── ...                     # Other data loading guides
└── resources/                  # FHIR resource documentation (future)
```

## Future Documentation

As the project progresses, additional documentation will be added for:

- FHIR resource structures and examples
- Advanced server configuration options
- Security and authentication
- FHIR profiles and extensions
- Additional query patterns and examples
