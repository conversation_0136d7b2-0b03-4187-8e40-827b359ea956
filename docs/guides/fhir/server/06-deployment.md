# Deployment and Exportability

This section covers the creation of scripts to automate the setup process and make the project easily exportable.

## Stage 9: Setup Script and Project Exportability

**Objective**: Create scripts to automate the setup process and make the project easily exportable.

### Step 1: Create Setup Script

Create a `setup.sh` script to automate the verification and installation process:

```bash
cat > setup.sh << EOL
#!/bin/bash

echo "=== FHIR Server Setup Script ==="
echo "This script will verify and install all required components for the FHIR server."

# Function to check if a command exists
command_exists() {
    command -v "\$1" >/dev/null 2>&1
}

# Check OS
echo "\nChecking operating system..."
OS=\$(uname -s)
case "\$OS" in
    Linux*)
        echo "✅ Operating system: Linux"
        ;;
    Darwin*)
        echo "✅ Operating system: macOS"
        ;;
    *)
        echo "⚠️ Operating system: \$OS (not fully tested)"
        ;;
esac

# Check Docker
echo "\nChecking Docker installation..."
if command_exists docker; then
    DOCKER_VERSION=\$(docker --version | awk '{print \$3}' | sed 's/,//')
    echo "✅ Docker is installed (version \$DOCKER_VERSION)"
else
    echo "❌ Docker is not installed"
    echo "Please install Docker from https://www.docker.com/products/docker-desktop"
    exit 1
fi

# Check Docker Compose
echo "\nChecking Docker Compose installation..."
if command_exists docker-compose; then
    COMPOSE_VERSION=\$(docker-compose --version | awk '{print \$3}' | sed 's/,//')
    echo "✅ Docker Compose is installed (version \$COMPOSE_VERSION)"
else
    echo "❌ Docker Compose is not installed"
    echo "Please install Docker Compose from https://docs.docker.com/compose/install/"
    exit 1
fi

# Check Java
echo "\nChecking Java installation..."
if command_exists java; then
    JAVA_VERSION=\$(java -version 2>&1 | awk -F '"' '/version/ {print \$2}')
    echo "✅ Java is installed (version \$JAVA_VERSION)"
else
    echo "⚠️ Java is not installed (not required for Docker deployment)"
fi

# Check Python
echo "\nChecking Python installation..."
if command_exists python3; then
    PYTHON_VERSION=\$(python3 --version 2>&1 | awk '{print \$2}')
    echo "✅ Python is installed (version \$PYTHON_VERSION)"
else
    echo "❌ Python is not installed"
    echo "Please install Python from https://www.python.org/downloads/"
    exit 1
fi

# Check if ports are available
echo "\nChecking if required ports are available..."
if command_exists lsof; then
    if lsof -i :8080 > /dev/null 2>&1; then
        echo "⚠️ Port 8080 is already in use. You may need to change the FHIR_PORT in .env"
    else
        echo "✅ Port 8080 is available"
    fi

    if lsof -i :5432 > /dev/null 2>&1; then
        echo "⚠️ Port 5432 is already in use. You may need to change the POSTGRES_PORT in .env"
    else
        echo "✅ Port 5432 is available"
    fi
else
    echo "⚠️ Cannot check port availability (lsof not installed)"
fi

# Create .env file if it doesn't exist
echo "\nChecking for .env file..."
if [ ! -f ".env" ]; then
    echo "Creating .env file with default values"
    cat > .env << ENVEOF
# HAPI FHIR Server Configuration

# Server configuration
FHIR_PORT=8080
FHIR_VERSION=R4

# Authentication (uncomment to enable)
#FHIR_USERNAME=fhir_user
#FHIR_PASSWORD=secure_fhir_password

# PostgreSQL configuration
POSTGRES_DB=hapi
POSTGRES_USER=admin
POSTGRES_PASSWORD=secure_password
POSTGRES_PORT=5432
ENVEOF
    echo "✅ Created .env file"
else
    echo "✅ .env file already exists"
fi

# Check if docker-compose.yml exists
echo "\nChecking for docker-compose.yml file..."
if [ ! -f "docker-compose.yml" ]; then
    echo "Creating docker-compose-postgres.yml file with PostgreSQL database"
    cat > docker-compose-postgres.yml << DOCKEREOF
services:
  # HAPI FHIR Server with PostgreSQL
  # Based on the official HAPI FHIR JPA Server Starter example:
  # https://github.com/hapifhir/hapi-fhir-jpaserver-starter
  hapi-fhir-server:
    image: hapiproject/hapi:latest                  # Official HAPI FHIR server image
    ports:
      - "\${FHIR_PORT:-8080}:8080"                   # Exposes container port 8080 to the host port defined by FHIR_PORT or defaults to 8080
    environment:
      # Basic FHIR server settings
      - hapi.fhir.default_encoding=json             # Sets default encoding to JSON
      - hapi.fhir.fhir_version=\${FHIR_VERSION:-R4}  # Sets the FHIR version to use

      # Bulk operations configuration
      - hapi.fhir.bulk_export_enabled=true          # Enables FHIR bulk export feature
      - hapi.fhir.bulk_import_enabled=true          # Enables FHIR bulk import feature
      - hapi.fhir.client_id_strategy=ANY            # Allows any client ID for bulk operations

      # FHIR Server Data Validation Configuration
      # By default, the server enforces data integrity and validation
      # For bulk data loading, uncomment the following lines to disable validation and referential integrity checks
      # After completing data import, comment these lines again to restore default security and integrity settings
      # -----------------------------------------------------------------------------------------------------
      # - hapi.fhir.validation.enabled=false                        # Disables validation
      # - hapi.fhir.validation.request_validator.mode=NONE          # Disables request validation
      # - hapi.fhir.auto_create_placeholder_reference_targets=true  # Creates placeholder resources for missing references
      # - hapi.fhir.enforce_referential_integrity_on_write=false    # Disables referential integrity checks on write
      # - hapi.fhir.enforce_referential_integrity_on_delete=false   # Disables referential integrity checks on delete

      # PostgreSQL configuration
      # Using the recommended dialect from the HAPI FHIR documentation:
      # https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
      - spring.datasource.url=*******************************:\${POSTGRES_PORT}/\${POSTGRES_DB}  # JDBC connection URL to PostgreSQL database
      - spring.datasource.username=\${POSTGRES_USER}                                            # Database username
      - spring.datasource.password=\${POSTGRES_PASSWORD}                                        # Database password
      - spring.datasource.driverClassName=org.postgresql.Driver                                 # JDBC driver for PostgreSQL
      - spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect  # Optimized dialect for HAPI FHIR and PostgreSQL
      - spring.jpa.properties.hibernate.search.enabled=false                                   # Disables advanced search (not required for most PostgreSQL use cases)
    volumes:
      - hapi-data:/data/hapi                        # Persistent volume for FHIR server data
    restart: unless-stopped                         # Restart container unless stopped manually
    depends_on:
      - fhir-postgres                               # Waits for the database to be ready before starting
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]  # Checks if the metadata endpoint is available
      interval: 30s                                 # Interval between health checks
      timeout: 10s                                  # Maximum wait time for a response
      retries: 5                                    # Number of retries before marking as unhealthy

  # PostgreSQL database service
  # PostgreSQL 14 is used for compatibility and stability with HAPI FHIR
  # Note: PostgreSQL 15 and 16 have known compatibility issues with HAPI FHIR
  # See: https://groups.google.com/g/hapi-fhir/c/gQLLcRtlwpI
  fhir-postgres:
    image: postgres:14                              # Official PostgreSQL version 14 image
    environment:
      POSTGRES_DB: \${POSTGRES_DB}                  # Name of the database to create
      POSTGRES_USER: \${POSTGRES_USER}              # Database admin user
      POSTGRES_PASSWORD: \${POSTGRES_PASSWORD}      # Admin user password
    volumes:
      - postgres-data:/var/lib/postgresql/data    # Persistent volume for PostgreSQL data
    restart: unless-stopped                       # Restart container unless stopped manually
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U \${POSTGRES_USER} -d \${POSTGRES_DB}"]  # Checks if the database is ready
      interval: 10s                               # Interval between health checks
      timeout: 5s                                 # Maximum wait time for a response
      retries: 5                                  # Number of retries before marking as unhealthy

volumes:
  hapi-data:                                      # Persistent volume for FHIR server data
  postgres-data:                                  # Persistent volume for PostgreSQL data
DOCKEREOF
    echo "✅ Created docker-compose.yml file"
else
    echo "✅ docker-compose.yml file already exists"
fi

# Install Python dependencies
echo "\nInstalling Python dependencies..."
pip3 install requests > /dev/null 2>&1
echo "✅ Python dependencies installed"

echo "\n=== Setup Complete ==="
echo "You can now start the FHIR server with: docker-compose up -d"
EOL

chmod +x setup.sh
```

### Step 2: Create a GitHub Repository

Prepare the project for GitHub:

```bash
# Initialize Git repository if not already done
git init

# Create .gitignore file
cat > .gitignore << EOL
# Environment variables
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Docker volumes
volumes/

# Logs
*.log

# Test outputs
patient_response.json

# IDE files
.idea/
.vscode/
*.swp
*.swo
EOL

# Create a basic README for GitHub
cat > README_GITHUB.md << EOL
# FHIR Server Development Environment

This repository contains a Docker-based setup for a local HAPI FHIR server environment.

## Quick Start

1. Clone this repository:
   \`\`\`bash
   git clone https://github.com/yourusername/fhir-server.git
   cd fhir-server
   \`\`\`

2. Run the setup script:
   \`\`\`bash
   ./setup.sh
   \`\`\`

3. Start the FHIR server:
   \`\`\`bash
   docker-compose up -d
   \`\`\`

4. Verify the server is running:
   \`\`\`bash
   curl http://localhost:8080/fhir/metadata
   \`\`\`

5. Run the automated tests:
   \`\`\`bash
   python3 test_fhir_server.py
   \`\`\`

## Components

- HAPI FHIR Server (R4)
- PostgreSQL database for robust data storage
- Postman collection for testing
- Automated test scripts

## Documentation

See the [FHIR_Server_Setup_Plan.md](FHIR_Server_Setup_Plan.md) file for detailed setup instructions.

## License

MIT
EOL
```

## Creating a Distribution Package

To make it easy for others to use your FHIR server setup, you can create a distribution package:

```bash
# Create a distribution directory
mkdir -p dist

# Copy all necessary files
cp -r docker-compose.yml .env setup.sh start-fhir-server.sh test_fhir_server.py README.md HAPI_FHIR_Server.postman_collection.json HAPI_FHIR_Local.postman_environment.json dist/

# Create a zip archive
cd dist
zip -r fhir-server-setup.zip *
cd ..

echo "Distribution package created at dist/fhir-server-setup.zip"
```

## Data Loading and Interaction

Once your FHIR server is deployed, you'll likely want to load data into it and interact with it. For detailed information on different methods to interact with the FHIR server and load data, see the [Data Interaction and Loading](09-data_interaction.md) guide.

## Next Steps

Now that you've created scripts to automate the setup process and make the project easily exportable, you can proceed to the [Security Considerations](07-security.md) section to learn about security best practices for the FHIR server.
