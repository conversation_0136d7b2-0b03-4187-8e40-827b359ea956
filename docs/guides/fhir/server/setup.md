# FHIR Server Setup and Implementation Plan

> **Note**: This document has been modularized for better readability. Please refer to the [index page](00-index.md) for the modular version of this documentation.

## Table of Contents

- [FHIR Server Setup and Implementation Plan](#fhir-server-setup-and-implementation-plan)
  - [Table of Contents](#table-of-contents)
- [Local FHIR-OMOP Deployment Architecture: Modular Design for Clinical Research](#local-fhir-omop-deployment-architecture-modular-design-for-clinical-research)
  - [Overview](#overview)
  - [Architectural Justification](#architectural-justification)
  - [Key Features](#key-features)
  - [Structure and Components](#structure-and-components)
  - [Upgrade Capability](#upgrade-capability)
  - [Phase 1: FHIR Server Setup and Basic Interaction](#phase-1-fhir-server-setup-and-basic-interaction)
    - [Stage 1: Verify Current Tech Stack](#stage-1-verify-current-tech-stack)
      - [Step 1: Verify Environment](#step-1-verify-environment)
    - [Stage 2: Install Missing Components](#stage-2-install-missing-components)
      - [Step 1: Install Docker (if not installed)](#step-1-install-docker-if-not-installed)
      - [Step 2: Install Java (if not installed or version \< 11)](#step-2-install-java-if-not-installed-or-version--11)
      - [Step 3: Install Python (if not installed or version \< 3.8)](#step-3-install-python-if-not-installed-or-version--38)
      - [Step 4: Install Git (if not installed)](#step-4-install-git-if-not-installed)
    - [Stage 3: Learn Docker Fundamentals](#stage-3-learn-docker-fundamentals)
      - [Step 1: Docker Terminology and Concepts](#step-1-docker-terminology-and-concepts)
      - [Step 2: Create a Simple Docker Example](#step-2-create-a-simple-docker-example)
    - [Stage 4: Set Up HAPI FHIR Server](#stage-4-set-up-hapi-fhir-server)
      - [Step 1: Create Project Directory Structure](#step-1-create-project-directory-structure)
      - [Step 2: Create Environment Configuration File](#step-2-create-environment-configuration-file)
      - [Step 3: Create Docker Compose File for HAPI FHIR with H2 Database](#step-3-create-docker-compose-file-for-hapi-fhir-with-h2-database)
      - [Step 4: Start the HAPI FHIR Server](#step-4-start-the-hapi-fhir-server)
      - [Step 5: Verify HAPI FHIR Server is Running](#step-5-verify-hapi-fhir-server-is-running)
    - [Stage 5: Install and Learn Postman](#stage-5-install-and-learn-postman)
      - [Step 1: Install Postman](#step-1-install-postman)
      - [Step 2: Postman Basics](#step-2-postman-basics)
    - [Stage 6: Create FHIR Resource Examples with Postman](#stage-6-create-fhir-resource-examples-with-postman)
      - [Step 1: Create a Patient Resource](#step-1-create-a-patient-resource)
      - [Step 2: Read a Patient Resource](#step-2-read-a-patient-resource)
      - [Step 3: Search for Patients](#step-3-search-for-patients)
      - [Step 4: Update a Patient Resource](#step-4-update-a-patient-resource)
      - [Step 5: Create Other FHIR Resources](#step-5-create-other-fhir-resources)
    - [Stage 7: Create Documentation](#stage-7-create-documentation)
      - [Step 1: Create a README.md File](#step-1-create-a-readmemd-file)
      - [Step 2: Export Postman Collection](#step-2-export-postman-collection)
      - [Step 3: Export Postman Environment](#step-3-export-postman-environment)
    - [Stage 8: Test and Validate](#stage-8-test-and-validate)
      - [Step 1: Test FHIR Server Functionality](#step-1-test-fhir-server-functionality)
      - [Step 2: Create Automated Test Script](#step-2-create-automated-test-script)
      - [Step 3: Validate Postman Collection](#step-3-validate-postman-collection)
      - [Step 4: Review Documentation](#step-4-review-documentation)
    - [Stage 9: Setup Script and Project Exportability](#stage-9-setup-script-and-project-exportability)
      - [Step 1: Create Setup Script](#step-1-create-setup-script)
      - [Step 2: Create a GitHub Repository](#step-2-create-a-github-repository)
  - [Security Considerations](#security-considerations)
    - [Basic Authentication for HAPI FHIR Server](#basic-authentication-for-hapi-fhir-server)
    - [Secure Database Credentials](#secure-database-credentials)
    - [Volume Management for Data Persistence](#volume-management-for-data-persistence)
  - [Future Work: Phase 2 Preview](#future-work-phase-2-preview)

# Local FHIR-OMOP Deployment Architecture: Modular Design for Clinical Research

## Overview

This setup creates a local dev environment with a FHIR (Fast Healthcare Interoperability Resources) server linked to a PostgreSQL DB. It’s the foundation for transforming FHIR into OMOP CDM (Common Data Model). Designed to be modular and scalable, the architecture supports clinical research in a reproducible, isolated setup.

## Architectural Justification

The proposed architecture is based on solid principles supported by our research:

1. **Docker containers for isolation and reproducibility**: Following recommendations from the [Vulcan FHIR-OMOP project](https://build.fhir.org/ig/HL7/vulcan-fhir-omop/), we use Docker to ensure consistent environments and eliminate "works on my machine" issues. As noted in the [FHIR to OMOP Cookbook](docs/research/pdfs/FHIR%20to%20OMOP%20Cookbook_v04.pdf), "containerization significantly facilitates deployment and reduces barriers to entry for new users."

2. **HAPI FHIR as reference FHIR server**: We selected [HAPI FHIR](https://hapifhir.io/) as it is the open-source reference implementation for FHIR R4, widely adopted in the research community. According to Microsoft's FHIR-to-OMOP Transformation Service documentation, "HAPI FHIR offers the most complete and standards-compliant implementation of the FHIR model."

3. **PostgreSQL for persistent storage**: We chose PostgreSQL for its robustness, performance, and compatibility with OMOP CDM. As [OHDSI documentation](https://ohdsi.github.io/TheBookOfOhdsi/CommonDataModel.html) indicates, "PostgreSQL is one of the recommended databases for OMOP CDM implementations due to its ability to handle large volumes of data and support for complex queries."

4. **FHIR R4 as standard version**: We use [FHIR R4](https://hl7.org/fhir/R4/) as it contains normative (stable) content and is the most widely implemented version. The [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel) documentation notes that "most FHIR-to-OMOP mapping tools are optimized for FHIR R4."

5. **OMOP CDM v5.4.2 as target model**: Although not implemented in this phase, we selected [OMOP CDM v5.4.2](https://github.com/OHDSI/CommonDataModel/releases/tag/v5.4.2) as the ultimate target due to its wide adoption and tool support. According to [OHDSI ETL-CDMBuilder](https://github.com/OHDSI/ETL-CDMBuilder) documentation, "OMOP CDM v5.4.2 offers the best balance between stability and features for clinical research projects."

## Key Features

- **Container-based architecture**: The entire environment runs in Docker containers, facilitating portability and reproducibility.
- **Complete parameterization**: All configurations are managed through environment variables, allowing adaptations without modifying code.
- **Automated testing**: Includes validation scripts to verify proper functioning of the FHIR server.
- **Comprehensive documentation**: Provides detailed guides for installation, configuration, and usage.
- **Basic security incorporated**: Includes options for authentication and credential protection.
- **FHIR interaction examples**: Contains Postman collections to facilitate learning and testing.

## Structure and Components

The deployment is structured in logical layers:

1. **Infrastructure layer**: Docker and Docker Compose for container orchestration.
2. **Persistence layer**: PostgreSQL for FHIR data storage.
3. **Application layer**: HAPI FHIR server for FHIR resource management.
4. **Utilities layer**: Configuration scripts, tests, and examples.

## Upgrade Capability

The design facilitates future upgrades through:

1. **Explicit versioning**: All dependencies have specific documented versions.
2. **Persistent volumes**: Data is stored in Docker volumes separate from code.
3. **Externalized configuration**: Parameters in `.env` files to facilitate changes.
4. **Modular design**: Each component can be upgraded independently.
5. **Migration path to OMOP**: Prepared for phase 2 which will implement OMOP CDM and ETL.

In future iterations, this environment can be expanded to include:
- Complete OMOP CDM implementation
- Automated FHIR-to-OMOP ETL processes
- Visualization and analysis tools
- Integration with standard vocabularies
- Data federation capabilities

---

This document outlines a comprehensive, step-by-step plan for setting up a local FHIR server environment as part of Phase 1 of our FHIR to OMOP transformation project.

## Phase 1: FHIR Server Setup and Basic Interaction

### Stage 1: Verify Current Tech Stack

**Objective**: Assess the current technology environment to identify what's already installed and what needs to be installed.

#### Step 1: Verify Environment

Run the following commands to check your current environment:

```bash
# Check OS version
uname -a

# For macOS, also run:
sw_vers

# Check if Java is installed and its version (need 11+)
java -version

# Check if Docker is installed and its version (need 20.10.x+)
docker --version

# Check Docker Compose (modern Docker Desktop uses 'docker compose')
docker compose version

# Check if Python is installed and its version (need 3.8+)
python --version

# Check if Git is installed and its version
git --version

# Check if ports 8080 (HAPI FHIR) and 5432 (PostgreSQL) are available
lsof -i :8080
lsof -i :5432
```

**Note**: If Docker is installed via Docker Desktop but not available in your PATH, you can add it with:

```bash
echo 'export PATH=$PATH:/Applications/Docker.app/Contents/Resources/bin' >> ~/.zshrc
source ~/.zshrc
```

### Stage 2: Install Missing Components

**Objective**: Install any missing components required for the development environment.

#### Step 1: Install Docker (if not installed)

**For macOS**:
1. Download Docker Desktop from [Docker's official website](https://www.docker.com/products/docker-desktop)
2. Install the downloaded .dmg file by dragging it to the Applications folder
3. Launch Docker Desktop and complete the setup process
4. Verify installation:
   ```bash
   docker --version
   docker-compose --version
   ```

**For Windows**:
1. Download Docker Desktop from [Docker's official website](https://www.docker.com/products/docker-desktop)
2. Run the installer and follow the prompts
3. Ensure WSL 2 is enabled if prompted
4. Launch Docker Desktop and complete the setup process
5. Verify installation:
   ```bash
   docker --version
   docker-compose --version
   ```

**For Linux (Ubuntu)**:
```bash
# Update package index
sudo apt-get update

# Install prerequisites
sudo apt-get install -y apt-transport-https ca-certificates curl software-properties-common

# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo apt-key add -

# Add Docker repository
sudo add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable"

# Update package index again
sudo apt-get update

# Install Docker CE
sudo apt-get install -y docker-ce

# Add your user to the docker group to run Docker without sudo
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Verify installations
docker --version
docker-compose --version
```

#### Step 2: Install Java (if not installed or version < 11)

**For macOS**:
```bash
# Using Homebrew
brew install openjdk@11

# Set JAVA_HOME
echo 'export JAVA_HOME=$(/usr/libexec/java_home -v 11)' >> ~/.zshrc
source ~/.zshrc
```

**For Windows**:
1. Download OpenJDK 11 from [AdoptOpenJDK](https://adoptopenjdk.net/)
2. Run the installer and follow the prompts
3. Set JAVA_HOME environment variable:
   - Right-click on 'This PC' > Properties > Advanced system settings > Environment Variables
   - Add a new system variable JAVA_HOME with the path to your JDK installation
   - Add %JAVA_HOME%\bin to your PATH variable

**For Linux (Ubuntu)**:
```bash
sudo apt-get update
sudo apt-get install -y openjdk-11-jdk
echo 'export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64' >> ~/.bashrc
source ~/.bashrc
```

#### Step 3: Install Python (if not installed or version < 3.8)

**For macOS**:
```bash
# Using Homebrew
brew install python@3.11

# Verify installation
python3 --version
```

**For Windows**:
1. Download Python 3.11 from [python.org](https://www.python.org/downloads/)
2. Run the installer, check "Add Python to PATH"
3. Verify installation:
   ```bash
   python --version
   ```

**For Linux (Ubuntu)**:
```bash
sudo apt-get update
sudo apt-get install -y python3.11 python3-pip
python3 --version
```

#### Step 4: Install Git (if not installed)

**For macOS**:
```bash
# Using Homebrew
brew install git
```

**For Windows**:
1. Download Git from [git-scm.com](https://git-scm.com/download/win)
2. Run the installer and follow the prompts
3. Verify installation:
   ```bash
   git --version
   ```

**For Linux (Ubuntu)**:
```bash
sudo apt-get update
sudo apt-get install -y git
```

### Stage 3: Learn Docker Fundamentals

**Objective**: Understand basic Docker concepts to effectively use containerized applications.

#### Step 1: Docker Terminology and Concepts

**Docker Basics**:
- **Container**: A lightweight, standalone, executable package that includes everything needed to run an application: code, runtime, system tools, libraries, and settings.
- **Image**: A read-only template used to create containers. Images are built from a set of instructions written in a Dockerfile.
- **Dockerfile**: A text file containing instructions to build a Docker image.
- **Docker Hub**: A cloud-based registry service for finding and sharing container images.
- **Docker Compose**: A tool for defining and running multi-container Docker applications using a YAML file.

**Key Docker Commands**:
```bash
# List running containers
docker ps

# List all containers (including stopped ones)
docker ps -a

# List available images
docker images

# Pull an image from Docker Hub
docker pull [image_name]:[tag]

# Run a container
docker run [options] [image_name]:[tag]

# Stop a container
docker stop [container_id_or_name]

# Remove a container
docker rm [container_id_or_name]

# Remove an image
docker rmi [image_id_or_name]

# View container logs
docker logs [container_id_or_name]

# Execute a command in a running container
docker exec -it [container_id_or_name] [command]
```

**Docker Compose Commands**:
```bash
# Start services defined in docker-compose.yml
docker-compose up

# Start services in detached mode (background)
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs

# View logs for a specific service
docker-compose logs [service_name]
```

#### Step 2: Create a Simple Docker Example

Create a directory for your Docker example:
```bash
mkdir docker-hello-world
cd docker-hello-world
```

Create a simple Python application:
```bash
# Create a Python file
cat > app.py << EOL
print("Hello, Docker World!")
EOL
```

Create a Dockerfile:
```bash
# Create a Dockerfile
cat > Dockerfile << EOL
# Use an official Python runtime as a parent image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Run app.py when the container launches
CMD ["python", "app.py"]
EOL
```

Build and run the Docker image:
```bash
# Build the image
docker build -t hello-docker .

# Run the container
docker run hello-docker
```

You should see the output: "Hello, Docker World!"

### Stage 4: Set Up HAPI FHIR Server

**Objective**: Deploy a local HAPI FHIR server using Docker.

#### Step 1: Create Project Directory Structure
```bash
# Create a directory for the FHIR server project
mkdir -p servers/fhir-server
cd servers/fhir-server
```

#### Step 2: Create Environment Configuration File

Create a `.env` file for environment variables:
```bash
cat > .env << EOL
# HAPI FHIR Server Configuration

# Server configuration
FHIR_PORT=8080
FHIR_VERSION=R4

# Authentication (uncomment to enable)
#FHIR_USERNAME=fhir_user
#FHIR_PASSWORD=secure_fhir_password

# PostgreSQL configuration (for future use)
#POSTGRES_DB=hapi
#POSTGRES_USER=admin
#POSTGRES_PASSWORD=secure_password
#POSTGRES_PORT=5432
EOL
```

#### Step 3: Create Docker Compose File for HAPI FHIR with H2 Database

Create a `docker-compose.yml` file that uses the environment variables and H2 in-memory database for simplicity:
```bash
cat > docker-compose.yml << EOL

services:
  # HAPI FHIR Server with H2 Database (simpler configuration)
  hapi-fhir-server:
    image: hapiproject/hapi:latest
    ports:
      - "${FHIR_PORT:-8080}:8080"
    environment:
      - hapi.fhir.default_encoding=json
      - hapi.fhir.bulk_export_enabled=true
      - hapi.fhir.fhir_version=${FHIR_VERSION:-R4}
      # Use H2 in-memory database instead of PostgreSQL
      - spring.datasource.url=jdbc:h2:mem:hapi
      - spring.datasource.username=sa
      - spring.datasource.password=
      - spring.datasource.driverClassName=org.h2.Driver
      - spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
    volumes:
      - hapi-data:/data/hapi
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  hapi-data:
EOL
```

#### Step 4: Start the HAPI FHIR Server
```bash
# Start the HAPI FHIR server
docker-compose up -d

# Check if containers are running
docker-compose ps

# View logs to monitor startup
docker-compose logs -f
```

#### Step 5: Verify HAPI FHIR Server is Running

Open a web browser and navigate to:
```
http://localhost:8080/
```

You should see the HAPI FHIR server welcome page. To check the FHIR capabilities, navigate to:
```
http://localhost:8080/fhir/metadata
```

This will display the FHIR CapabilityStatement in JSON format, showing which FHIR resources and operations are supported.

### Stage 5: Install and Learn Postman

**Objective**: Install Postman and learn how to use it to interact with the FHIR server.

#### Step 1: Install Postman

**For macOS**:
1. Download Postman from [Postman's official website](https://www.postman.com/downloads/)
2. Install the downloaded .dmg file by dragging it to the Applications folder
3. Launch Postman from the Applications folder

**For Windows**:
1. Download Postman from [Postman's official website](https://www.postman.com/downloads/)
2. Run the installer and follow the prompts
3. Launch Postman from the Start menu

**For Linux (Ubuntu)**:
```bash
# Download Postman
wget https://dl.pstmn.io/download/latest/linux64 -O postman.tar.gz

# Extract the archive
sudo tar -xzf postman.tar.gz -C /opt

# Create a symbolic link
sudo ln -s /opt/Postman/Postman /usr/bin/postman

# Create a desktop entry
cat > ~/.local/share/applications/postman.desktop << EOL
[Desktop Entry]
Encoding=UTF-8
Name=Postman
Exec=/opt/Postman/Postman
Icon=/opt/Postman/app/resources/app/assets/icon.png
Terminal=false
Type=Application
Categories=Development;
EOL

# Launch Postman
postman
```

#### Step 2: Postman Basics

**Postman Terminology**:
- **Collection**: A group of related API requests
- **Request**: A single HTTP request to an API endpoint
- **Environment**: A set of variables that can be used across requests
- **Variables**: Key-value pairs that can be used in requests
- **Tests**: JavaScript code to validate responses
- **Pre-request Scripts**: JavaScript code to run before a request is sent

**Creating a FHIR Collection**:
1. Open Postman
2. Click "Create a Collection" or the "+" button next to Collections
3. Name the collection "HAPI FHIR Server"
4. Click "Create"

**Setting Up Environment Variables**:
1. Click on "Environments" in the sidebar
2. Click "Create Environment"
3. Name the environment "HAPI FHIR Local"
4. Add the following variables:
   - `fhir_server`: `http://localhost:8080/fhir`
   - `content_type`: `application/fhir+json`
5. Click "Save"
6. Select the "HAPI FHIR Local" environment from the dropdown in the top right

### Stage 6: Create FHIR Resource Examples with Postman

**Objective**: Create documentation and examples for interacting with FHIR resources using Postman.

#### Step 1: Create a Patient Resource

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Create Patient"
3. Set the method to "POST"
4. Set the URL to `{{fhir_server}}/Patient`
5. Go to the "Headers" tab and add:
   - `Content-Type`: `{{content_type}}`
6. Go to the "Body" tab, select "raw", and set the format to "JSON"
7. Add the following JSON:
```json
{
  "resourceType": "Patient",
  "active": true,
  "name": [
    {
      "use": "official",
      "family": "Smith",
      "given": ["John"]
    }
  ],
  "gender": "male",
  "birthDate": "1970-01-01",
  "address": [
    {
      "use": "home",
      "line": ["123 Main St"],
      "city": "Anytown",
      "state": "CA",
      "postalCode": "12345",
      "country": "USA"
    }
  ],
  "telecom": [
    {
      "system": "phone",
      "value": "************",
      "use": "home"
    },
    {
      "system": "email",
      "value": "<EMAIL>"
    }
  ]
}
```
8. Click "Save"
9. Click "Send" to create the patient

**Add a test script**:
1. Go to the "Tests" tab
2. Add the following JavaScript:
```javascript
// Check if the request was successful
pm.test("Status code is 201 Created", function() {
    pm.response.to.have.status(201);
});

// Save the patient ID for later use
if (pm.response.code === 201) {
    var jsonData = pm.response.json();
    pm.environment.set("patient_id", jsonData.id);
    console.log("Patient ID: " + jsonData.id);
}
```
3. Click "Save" and "Send" again

#### Step 2: Read a Patient Resource

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Read Patient"
3. Set the method to "GET"
4. Set the URL to `{{fhir_server}}/Patient/{{patient_id}}`
5. Go to the "Headers" tab and add:
   - `Accept`: `{{content_type}}`
6. Click "Save"
7. Click "Send" to retrieve the patient

#### Step 3: Search for Patients

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Search Patients"
3. Set the method to "GET"
4. Set the URL to `{{fhir_server}}/Patient?family=Smith`
5. Go to the "Headers" tab and add:
   - `Accept`: `{{content_type}}`
6. Click "Save"
7. Click "Send" to search for patients

#### Step 4: Update a Patient Resource

**Create a new request**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Update Patient"
3. Set the method to "PUT"
4. Set the URL to `{{fhir_server}}/Patient/{{patient_id}}`
5. Go to the "Headers" tab and add:
   - `Content-Type`: `{{content_type}}`
6. Go to the "Body" tab, select "raw", and set the format to "JSON"
7. Add the following JSON:
```json
{
  "resourceType": "Patient",
  "id": "{{patient_id}}",
  "active": true,
  "name": [
    {
      "use": "official",
      "family": "Smith",
      "given": ["John", "Robert"]
    }
  ],
  "gender": "male",
  "birthDate": "1970-01-01",
  "address": [
    {
      "use": "home",
      "line": ["456 Oak Ave"],
      "city": "Anytown",
      "state": "CA",
      "postalCode": "12345",
      "country": "USA"
    }
  ],
  "telecom": [
    {
      "system": "phone",
      "value": "************",
      "use": "home"
    },
    {
      "system": "email",
      "value": "<EMAIL>"
    }
  ]
}
```
8. Click "Save"
9. Click "Send" to update the patient

#### Step 5: Create Other FHIR Resources

**Create an Observation Resource**:
1. In the "HAPI FHIR Server" collection, click "Add request"
2. Name it "Create Observation"
3. Set the method to "POST"
4. Set the URL to `{{fhir_server}}/Observation`
5. Go to the "Headers" tab and add:
   - `Content-Type`: `{{content_type}}`
6. Go to the "Body" tab, select "raw", and set the format to "JSON"
7. Add the following JSON:
```json
{
  "resourceType": "Observation",
  "status": "final",
  "category": [
    {
      "coding": [
        {
          "system": "http://terminology.hl7.org/CodeSystem/observation-category",
          "code": "vital-signs",
          "display": "Vital Signs"
        }
      ]
    }
  ],
  "code": {
    "coding": [
      {
        "system": "http://loinc.org",
        "code": "8867-4",
        "display": "Heart rate"
      }
    ],
    "text": "Heart rate"
  },
  "subject": {
    "reference": "Patient/{{patient_id}}"
  },
  "effectiveDateTime": "2023-01-01T12:00:00Z",
  "valueQuantity": {
    "value": 80,
    "unit": "beats/minute",
    "system": "http://unitsofmeasure.org",
    "code": "/min"
  }
}
```
8. Click "Save"
9. Click "Send" to create the observation

### Stage 7: Create Documentation

**Objective**: Create comprehensive documentation for the FHIR server setup and usage.

#### Step 1: Create a README.md File

Create a README.md file in the project directory with detailed instructions for:
- Setting up the FHIR server
- Using Postman to interact with FHIR resources
- Common operations (create, read, update, search)
- Troubleshooting common issues

#### Step 2: Export Postman Collection

1. In Postman, click on the "HAPI FHIR Server" collection
2. Click the "..." (more actions) button
3. Select "Export"
4. Choose "Collection v2.1" format
5. Save the file as "HAPI_FHIR_Server.postman_collection.json" in the project directory

#### Step 3: Export Postman Environment

1. In Postman, click on "Environments" in the sidebar
2. Click the "..." (more actions) button next to the "HAPI FHIR Local" environment
3. Select "Export"
4. Save the file as "HAPI_FHIR_Local.postman_environment.json" in the project directory

### Stage 8: Test and Validate

**Objective**: Ensure the FHIR server is working correctly and the documentation is accurate.

#### Step 1: Test FHIR Server Functionality
```bash
# Restart the FHIR server to ensure a clean state
docker-compose down
docker-compose up -d

# Wait for the server to start
sleep 30

# Test the server using curl
curl -X GET http://localhost:8080/fhir/metadata -H "Accept: application/fhir+json"

# Verify the response contains a CapabilityStatement
curl -X GET http://localhost:8080/fhir/metadata -H "Accept: application/fhir+json" | grep -q "CapabilityStatement" && echo "FHIR Server is running correctly" || echo "FHIR Server validation failed"

# Test creating a patient
curl -X POST http://localhost:8080/fhir/Patient \
  -H "Content-Type: application/fhir+json" \
  -d '{"resourceType":"Patient","name":[{"family":"Test","given":["Patient"]}]}' \
  -o patient_response.json

# Extract the patient ID for further testing
PATIENT_ID=$(grep -o '"id":"[^"]*"' patient_response.json | cut -d '"' -f 4)
echo "Created test patient with ID: $PATIENT_ID"

# Test retrieving the patient
curl -X GET http://localhost:8080/fhir/Patient/$PATIENT_ID -H "Accept: application/fhir+json"
```

#### Step 2: Create Automated Test Script

Create a Python script for automated testing:

```bash
cat > test_fhir_server.py << EOL
#!/usr/bin/env python3
"""
Script to test connectivity and functionality of the HAPI FHIR server.
"""
import os
import sys
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# FHIR server configuration
FHIR_PORT = os.getenv("FHIR_PORT", "8080")
FHIR_SERVER_URL = f"http://localhost:{FHIR_PORT}/fhir"
FHIR_USERNAME = os.getenv("FHIR_USERNAME", "")
FHIR_PASSWORD = os.getenv("FHIR_PASSWORD", "")

# Authentication configuration
auth = None
if FHIR_USERNAME and FHIR_PASSWORD:
    auth = (FHIR_USERNAME, FHIR_PASSWORD)

def check_server_status():
    """Check if the FHIR server is running."""
    try:
        response = requests.get(f"{FHIR_SERVER_URL}/metadata", auth=auth)
        if response.status_code == 200:
            print("✅ FHIR server is running.")
            return True
        else:
            print(f"❌ FHIR server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Could not connect to FHIR server: {e}")
        return False

def create_test_patient():
    """Create a test patient in the FHIR server."""
    patient_data = {
        "resourceType": "Patient",
        "active": True,
        "name": [
            {
                "use": "official",
                "family": "Test",
                "given": ["Patient"]
            }
        ],
        "gender": "male",
        "birthDate": "1970-01-01"
    }

    headers = {"Content-Type": "application/fhir+json"}

    try:
        response = requests.post(
            f"{FHIR_SERVER_URL}/Patient",
            json=patient_data,
            headers=headers,
            auth=auth
        )

        if response.status_code in [200, 201]:
            patient_id = response.json().get("id")
            print(f"✅ Test patient created with ID: {patient_id}")
            return patient_id
        else:
            print(f"❌ Error creating test patient: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when creating patient: {e}")
        return None

def get_patient(patient_id):
    """Get a patient by ID."""
    try:
        response = requests.get(
            f"{FHIR_SERVER_URL}/Patient/{patient_id}",
            auth=auth
        )

        if response.status_code == 200:
            print(f"✅ Patient retrieved successfully:")
            patient_data = response.json()
            print(f"  - ID: {patient_data.get('id')}")
            name = patient_data.get('name', [{}])[0]
            full_name = f"{' '.join(name.get('given', []))} {name.get('family', '')}"
            print(f"  - Name: {full_name}")
            print(f"  - Gender: {patient_data.get('gender', 'not specified')}")
            print(f"  - Birth date: {patient_data.get('birthDate', 'not specified')}")
            return True
        else:
            print(f"❌ Error retrieving patient: {response.status_code}")
            print(response.text)
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when retrieving patient: {e}")
        return False

def run_tests():
    """Run all tests."""
    print("\n=== HAPI FHIR Server Test ===\n")

    # Check server status
    if not check_server_status():
        print("\n❌ The FHIR server is not available. Make sure it is running.")
        sys.exit(1)

    # Create test patient
    print("\n--- Creating test patient ---")
    patient_id = create_test_patient()
    if not patient_id:
        print("\n❌ Could not create the test patient.")
        sys.exit(1)

    # Retrieve patient
    print("\n--- Retrieving patient ---")
    if not get_patient(patient_id):
        print("\n❌ Could not retrieve the patient.")
        sys.exit(1)

    print("\n✅ All tests completed successfully.")
    print(f"The HAPI FHIR server is working correctly at: {FHIR_SERVER_URL}")

if __name__ == "__main__":
    run_tests()
EOL

chmod +x test_fhir_server.py
```

Run the automated tests:
```bash
python3 test_fhir_server.py
```

#### Step 3: Validate Postman Collection
1. Import the exported collection into a new Postman instance
2. Import the exported environment into a new Postman instance
3. Run through each request to ensure they work as expected

#### Step 4: Review Documentation
1. Review the README.md file for accuracy
2. Ensure all commands and instructions are correct
3. Check for any missing information or steps

### Stage 9: Setup Script and Project Exportability

**Objective**: Create scripts to automate the setup process and make the project easily exportable.

#### Step 1: Create Setup Script

Create a `setup.sh` script to automate the verification and installation process:

```bash
cat > setup.sh << EOL
#!/bin/bash

echo "=== FHIR Server Setup Script ==="
echo "This script will verify and install all required components for the FHIR server."

# Function to check if a command exists
command_exists() {
    command -v "\$1" >/dev/null 2>&1
}

# Check OS
echo "\nChecking operating system..."
OS=\$(uname -s)
case "\$OS" in
    Linux*)
        echo "✅ Operating system: Linux"
        ;;
    Darwin*)
        echo "✅ Operating system: macOS"
        ;;
    *)
        echo "⚠️ Operating system: \$OS (not fully tested)"
        ;;
esac

# Check Docker
echo "\nChecking Docker installation..."
if command_exists docker; then
    DOCKER_VERSION=\$(docker --version | awk '{print \$3}' | sed 's/,//')
    echo "✅ Docker is installed (version \$DOCKER_VERSION)"
else
    echo "❌ Docker is not installed"
    echo "Please install Docker from https://www.docker.com/products/docker-desktop"
    exit 1
fi

# Check Docker Compose
echo "\nChecking Docker Compose installation..."
if command_exists docker-compose; then
    COMPOSE_VERSION=\$(docker-compose --version | awk '{print \$3}' | sed 's/,//')
    echo "✅ Docker Compose is installed (version \$COMPOSE_VERSION)"
else
    echo "❌ Docker Compose is not installed"
    echo "Please install Docker Compose from https://docs.docker.com/compose/install/"
    exit 1
fi

# Check Java
echo "\nChecking Java installation..."
if command_exists java; then
    JAVA_VERSION=\$(java -version 2>&1 | awk -F '"' '/version/ {print \$2}')
    echo "✅ Java is installed (version \$JAVA_VERSION)"
else
    echo "⚠️ Java is not installed (not required for Docker deployment)"
fi

# Check Python
echo "\nChecking Python installation..."
if command_exists python3; then
    PYTHON_VERSION=\$(python3 --version 2>&1 | awk '{print \$2}')
    echo "✅ Python is installed (version \$PYTHON_VERSION)"
else
    echo "❌ Python is not installed"
    echo "Please install Python from https://www.python.org/downloads/"
    exit 1
fi

# Check if ports are available
echo "\nChecking if required ports are available..."
if command_exists lsof; then
    if lsof -i :8080 > /dev/null 2>&1; then
        echo "⚠️ Port 8080 is already in use. You may need to change the FHIR_PORT in .env"
    else
        echo "✅ Port 8080 is available"
    fi

    if lsof -i :5432 > /dev/null 2>&1; then
        echo "⚠️ Port 5432 is already in use. You may need to change the POSTGRES_PORT in .env"
    else
        echo "✅ Port 5432 is available"
    fi
else
    echo "⚠️ Cannot check port availability (lsof not installed)"
fi

# Create .env file if it doesn't exist
echo "\nChecking for .env file..."
if [ ! -f ".env" ]; then
    echo "Creating .env file with default values"
    cat > .env << ENVEOF
# HAPI FHIR Server Configuration

# Server configuration
FHIR_PORT=8080
FHIR_VERSION=R4

# Authentication (uncomment to enable)
#FHIR_USERNAME=fhir_user
#FHIR_PASSWORD=secure_fhir_password

# PostgreSQL configuration (for future use)
#POSTGRES_DB=hapi
#POSTGRES_USER=admin
#POSTGRES_PASSWORD=secure_password
#POSTGRES_PORT=5432
ENVEOF
    echo "✅ Created .env file"
else
    echo "✅ .env file already exists"
fi

# Install Python dependencies
echo "\nInstalling Python dependencies..."
pip3 install requests > /dev/null 2>&1
echo "✅ Python dependencies installed"

echo "\n=== Setup Complete ==="
echo "You can now start the FHIR server with: docker-compose up -d"
EOL

chmod +x setup.sh
```

#### Step 2: Create a GitHub Repository

Prepare the project for GitHub:

```bash
# Initialize Git repository if not already done
git init

# Create .gitignore file
cat > .gitignore << EOL
# Environment variables
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Docker volumes
volumes/

# Logs
*.log

# Test outputs
patient_response.json

# IDE files
.idea/
.vscode/
*.swp
*.swo
EOL

# Create a basic README for GitHub
cat > README_GITHUB.md << EOL
# FHIR Server Development Environment

This repository contains a Docker-based setup for a local HAPI FHIR server environment.

## Quick Start

1. Clone this repository:
   \`\`\`bash
   git clone https://github.com/yourusername/fhir-server.git
   cd fhir-server
   \`\`\`

2. Run the setup script:
   \`\`\`bash
   ./setup.sh
   \`\`\`

3. Start the FHIR server:
   \`\`\`bash
   docker-compose up -d
   \`\`\`

4. Verify the server is running:
   \`\`\`bash
   curl http://localhost:8080/fhir/metadata
   \`\`\`

5. Run the automated tests:
   \`\`\`bash
   python3 test_fhir_server.py
   \`\`\`

## Components

- HAPI FHIR Server (R4)
- PostgreSQL database
- Postman collection for testing
- Automated test scripts

## Documentation

See the [FHIR_Server_Setup_Plan.md](FHIR_Server_Setup_Plan.md) file for detailed setup instructions.

## License

MIT
EOL
```

## Security Considerations

While this is a development environment, it's good practice to implement basic security measures:

### Basic Authentication for HAPI FHIR Server

To enable basic authentication on the HAPI FHIR server, update the `docker-compose.yml` file with these additional environment variables:

```yaml
environment:
  # ... existing variables ...
  - hapi.fhir.security.enabled=true
  - hapi.fhir.security.basic.enabled=true
  - hapi.fhir.security.basic.username=${FHIR_USERNAME}
  - hapi.fhir.security.basic.password=${FHIR_PASSWORD}
```

And add these variables to your `.env` file:

```
FHIR_USERNAME=fhir_user
FHIR_PASSWORD=secure_fhir_password
```

### Secure Database Credentials

For production environments, always use strong, unique passwords for database credentials. In the `.env` file, replace the default values with secure passwords:

```
POSTGRES_PASSWORD=a_strong_unique_password_here
```

### Volume Management for Data Persistence

For better data management and backup capabilities, consider using named volumes or bind mounts to specific directories:

```yaml
volumes:
  hapi-data:
    name: fhir-server-data
  hapi-postgres-data:
    name: fhir-postgres-data
```

## Future Work: Phase 2 Preview


Phase 2 will focus on setting up the OMOP CDM database and implementing the transformation pipeline from FHIR to OMOP. Key components will include:

1. **OMOP CDM Database Setup**:
   - Using OHDSI tools to create the OMOP CDM v5.4 schema
   - Configuring PostgreSQL for optimal performance
   - Loading vocabulary data

2. **ETL Pipeline Development**:
   - Implementing FHIR to OMOP mappings
   - Creating transformation logic
   - Developing validation and quality checks

3. **Integration**:
   - Connecting the FHIR server to the OMOP database
   - Implementing automated data flows
   - Setting up monitoring and logging

A detailed plan for Phase 2 will be developed after the successful completion of Phase 1.
