# PostgreSQL Migration Plan for FHIR Server

This document outlines the plan for migrating the HAPI FHIR server from H2 to PostgreSQL database.

## 1. Current State Analysis

### 1.1 Current FHIR Server Configuration

The current FHIR server uses H2 in file mode as configured in `servers/fhir-server/docker-compose.yml`:

```yaml
services:
  hapi-fhir-server:
    image: hapiproject/hapi:latest
    ports:
      - "${FHIR_PORT:-8080}:8080"
    environment:
      - hapi.fhir.default_encoding=json
      - hapi.fhir.bulk_export_enabled=true
      - hapi.fhir.fhir_version=${FHIR_VERSION:-R4}
      # H2 file-based database configuration
      - spring.datasource.url=jdbc:h2:file:/data/hapi/database
      - spring.datasource.username=sa
      - spring.datasource.password=
      - spring.datasource.driverClassName=org.h2.Driver
      - spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
    volumes:
      - hapi-data:/data/hapi
    restart: unless-stopped
```

## 2. PostgreSQL Implementation Plan

### 2.1 Create a New Docker Compose Configuration

Create a new `docker-compose-postgres.yml` file with PostgreSQL configuration:

```yaml
version: '3'

services:
  # HAPI FHIR Server with PostgreSQL
  # Based on official HAPI FHIR JPA Server Starter example:
  # https://github.com/hapifhir/hapi-fhir-jpaserver-starter
  hapi-fhir-server:
    image: hapiproject/hapi:latest
    ports:
      - "${FHIR_PORT:-8080}:8080"
    environment:
      - hapi.fhir.default_encoding=json
      - hapi.fhir.bulk_export_enabled=true
      - hapi.fhir.fhir_version=${FHIR_VERSION:-R4}
      # PostgreSQL configuration
      # Using recommended dialect from HAPI FHIR docs:
      # https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
      - spring.datasource.url=*****************************************
      - spring.datasource.username=admin
      - spring.datasource.password=admin
      - spring.datasource.driverClassName=org.postgresql.Driver
      - spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect
      # Disable Hibernate search as recommended for PostgreSQL
      - spring.jpa.properties.hibernate.search.enabled=false
    volumes:
      - hapi-data:/data/hapi
    restart: unless-stopped
    depends_on:
      - fhir-postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]
      interval: 30s
      timeout: 10s
      retries: 5

  # PostgreSQL database
  # Using PostgreSQL 14 as it's well-tested with HAPI FHIR
  # Note: PostgreSQL 15 and 16 have known compatibility issues with HAPI FHIR
  # See: https://groups.google.com/g/hapi-fhir/c/gQLLcRtlwpI (Error: "type clob does not exist")
  fhir-postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: hapi
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin
    volumes:
      - postgres-data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d hapi"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  hapi-data:
  postgres-data:
```

### 2.2 Create a Database Configuration Script

Create a Python script `setup_postgres_fhir.py` to verify and test the PostgreSQL configuration:

```python
#!/usr/bin/env python3
"""
PostgreSQL FHIR Server Configuration Verification Script

This script verifies that the HAPI FHIR server is correctly configured
with PostgreSQL and performs basic tests to ensure functionality.

References:
- HAPI FHIR Database Support: https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
- HAPI FHIR JPA Server Starter: https://github.com/hapifhir/hapi-fhir-jpaserver-starter
"""
import os
import sys
import time
import requests
from typing import Dict, Any, Optional, List, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
FHIR_PORT = os.getenv("FHIR_PORT", "8080")
FHIR_SERVER_URL = f"http://localhost:{FHIR_PORT}/fhir"
HEADERS = {"Content-Type": "application/fhir+json"}


def wait_for_server() -> bool:
    """Wait for the FHIR server to be available.

    Attempts to connect to the FHIR server's metadata endpoint
    multiple times until it succeeds or reaches the maximum number
    of attempts.

    Returns
    -------
    bool
        True if the server is available, False otherwise.
    """
    print("Checking if FHIR server is available...")
    max_attempts = 30
    for attempt in range(max_attempts):
        try:
            response = requests.get(f"{FHIR_SERVER_URL}/metadata")
            if response.status_code == 200:
                print("✅ FHIR server is available.")
                return True
        except requests.exceptions.RequestException:
            pass

        print(f"Waiting for server... ({attempt+1}/{max_attempts})")
        time.sleep(2)

    print("❌ Could not connect to FHIR server after multiple attempts.")
    return False


def create_test_patient() -> Optional[str]:
    """Create a test patient to verify database write operations.

    Creates a simple Patient resource in the FHIR server
    to test database write functionality.

    Returns
    -------
    Optional[str]
        The ID of the created patient, or None if creation failed.
    """
    # Test patient data based on FHIR R4 Patient resource structure
    # Reference: https://www.hl7.org/fhir/patient.html
    patient_data = {
        "resourceType": "Patient",
        "active": True,
        "name": [
            {
                "use": "official",
                "family": "PostgreSQL",
                "given": ["Test"]
            }
        ],
        "gender": "unknown",
        "birthDate": "2000-01-01"
    }

    try:
        response = requests.post(
            f"{FHIR_SERVER_URL}/Patient",
            json=patient_data,
            headers=HEADERS
        )

        if response.status_code in [200, 201]:
            patient_id = response.json().get("id")
            print(f"✅ Test patient created with ID: {patient_id}")
            return patient_id
        else:
            print(f"❌ Error creating test patient: {response.status_code}")
            print(response.text[:100])
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when creating patient: {e}")
        return None


def search_patients() -> Tuple[bool, int]:
    """Search for patients to verify database read operations.

    Performs a simple search query to test database read functionality.

    Returns
    -------
    Tuple[bool, int]
        A tuple containing (success status, count of patients)
    """
    try:
        # Using _summary=count parameter as per FHIR search specification
        # Reference: https://www.hl7.org/fhir/search.html#summary
        response = requests.get(f"{FHIR_SERVER_URL}/Patient?_summary=count")

        if response.status_code == 200:
            count = response.json().get("total", 0)
            print(f"✅ Found {count} patients in the database")
            return True, count
        else:
            print(f"❌ Error searching patients: {response.status_code}")
            return False, 0
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when searching patients: {e}")
        return False, 0


def load_sample_data(resource_type: str, file_path: str) -> Tuple[int, int]:
    """Load sample data from NDJSON file into the FHIR server.

    Parameters
    ----------
    resource_type : str
        The FHIR resource type (e.g., "Patient", "Observation")
    file_path : str
        Path to the NDJSON file containing resources

    Returns
    -------
    Tuple[int, int]
        A tuple containing (success count, error count)
    """
    print(f"\nLoading {resource_type} resources from {file_path}")

    success_count = 0
    error_count = 0

    try:
        with open(file_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    # Parse the JSON resource
                    import json
                    resource = json.loads(line)

                    # Post to the FHIR server
                    response = requests.post(
                        f"{FHIR_SERVER_URL}/{resource_type}",
                        json=resource,
                        headers=HEADERS
                    )

                    if response.status_code in [200, 201]:
                        success_count += 1
                    else:
                        error_count += 1
                        print(f"Error on line {line_num}: {response.status_code}")

                    # Show progress for every 10 resources
                    if line_num % 10 == 0:
                        print(f"Processed {line_num} resources...")

                except Exception as e:
                    error_count += 1
                    print(f"Exception on line {line_num}: {str(e)}")
    except FileNotFoundError:
        print(f"File not found: {file_path}")
        return 0, 0

    print(f"Completed loading {resource_type}: {success_count} successes, {error_count} errors")
    return success_count, error_count


def main() -> None:
    """Main function to verify PostgreSQL configuration and load sample data."""
    print("\n=== PostgreSQL FHIR Server Configuration Verification ===\n")

    # Step 1: Check if the server is running
    if not wait_for_server():
        sys.exit(1)

    # Step 2: Test database write operations
    patient_id = create_test_patient()
    if not patient_id:
        print("❌ Database write test failed. Check PostgreSQL configuration.")
        sys.exit(1)

    # Step 3: Test database read operations
    success, count = search_patients()
    if not success:
        print("❌ Database read test failed. Check PostgreSQL configuration.")
        sys.exit(1)

    # Step 4: Load sample data if available
    sample_data_dir = "../../data/sample_fhir/bulk-export"

    # Define resource types and their corresponding files
    # Order matters - load resources with fewer dependencies first
    resource_files = [
        ("Patient", f"{sample_data_dir}/Patient.ndjson"),
        ("Organization", f"{sample_data_dir}/Organization.ndjson"),
        ("Practitioner", f"{sample_data_dir}/Practitioner.ndjson"),
        ("Encounter", f"{sample_data_dir}/Encounter.ndjson"),
        ("Observation", f"{sample_data_dir}/Observation.ndjson")
    ]

    total_success = 0
    total_error = 0

    for resource_type, file_path in resource_files:
        success, error = load_sample_data(resource_type, file_path)
        total_success += success
        total_error += error

    print(f"\n=== Configuration Verification Complete ===")
    print(f"Total resources loaded: {total_success}")
    print(f"Total errors: {total_error}")

    print("\n✅ PostgreSQL FHIR server is configured correctly and operational.")
    print(f"You can access the FHIR server at: {FHIR_SERVER_URL}")


if __name__ == "__main__":
    main()
```

### 2.3 Create a Server Management Script

Create a shell script `manage-fhir-server.sh` to manage the FHIR server with different database configurations:

```bash
#!/bin/bash
# FHIR Server Management Script
#
# This script manages the FHIR server with different database configurations.
# It supports starting, stopping, and checking the status of the server with
# either H2 or PostgreSQL database.
#
# References:
# - HAPI FHIR Docker: https://hub.docker.com/r/hapiproject/hapi
# - HAPI FHIR JPA Server Starter: https://github.com/hapifhir/hapi-fhir-jpaserver-starter

# Determine which Docker Compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

# Default configuration
DB_TYPE=${2:-"postgres"}  # Default to PostgreSQL
ACTION=$1

# Function to display usage information
usage() {
    echo "Usage: $0 [start|stop|status|logs|restart] [h2|postgres]"
    echo ""
    echo "Actions:"
    echo "  start    - Start the FHIR server"
    echo "  stop     - Stop the FHIR server"
    echo "  status   - Check the status of the FHIR server"
    echo "  logs     - Show logs from the FHIR server"
    echo "  restart  - Restart the FHIR server"
    echo ""
    echo "Database Options:"
    echo "  h2       - Use H2 database (file-based)"
    echo "  postgres - Use PostgreSQL database (default)"
    echo ""
    echo "Examples:"
    echo "  $0 start postgres  - Start with PostgreSQL"
    echo "  $0 start h2        - Start with H2"
    echo "  $0 status          - Check server status"
    exit 1
}

# Function to determine the compose file based on database type
get_compose_file() {
    if [ "$DB_TYPE" == "h2" ]; then
        echo "docker-compose.yml"  # Original file with H2
    elif [ "$DB_TYPE" == "postgres" ]; then
        echo "docker-compose-postgres.yml"
    else
        echo "Invalid database type: $DB_TYPE"
        usage
    fi
}

# Check if action is provided
if [ -z "$ACTION" ]; then
    usage
fi

# Get the appropriate compose file
COMPOSE_FILE=$(get_compose_file)

# Execute the requested action
case $ACTION in
    start)
        echo "Starting FHIR server with $DB_TYPE database..."
        $COMPOSE_CMD -f $COMPOSE_FILE up -d
        echo "Server starting. Check status with: $0 status"
        ;;
    stop)
        echo "Stopping FHIR server..."
        $COMPOSE_CMD -f $COMPOSE_FILE down
        ;;
    status)
        echo "Checking FHIR server status..."
        $COMPOSE_CMD -f $COMPOSE_FILE ps
        ;;
    logs)
        echo "Showing FHIR server logs..."
        $COMPOSE_CMD -f $COMPOSE_FILE logs -f
        ;;
    restart)
        echo "Restarting FHIR server with $DB_TYPE database..."
        $COMPOSE_CMD -f $COMPOSE_FILE down
        $COMPOSE_CMD -f $COMPOSE_FILE up -d
        ;;
    *)
        echo "Invalid action: $ACTION"
        usage
        ;;
esac

exit 0
```

### 2.4 Update the Sample Data Loading Script

Update the `load-sample-data.sh` script to work with both database configurations:

```bash
#!/bin/bash
# Sample Data Loading Script for FHIR Server
#
# This script loads sample FHIR data from NDJSON files into the HAPI FHIR server.
# It works with both H2 and PostgreSQL database configurations.
#
# References:
# - FHIR Bulk Data Format: https://hl7.org/fhir/uv/bulkdata/
# - HAPI FHIR REST API: https://hapifhir.io/hapi-fhir/docs/server_plain/rest_operations.html

# Function to display usage information
usage() {
    echo "Usage: $0 [h2|postgres]"
    echo ""
    echo "Database Options:"
    echo "  h2       - Use H2 database configuration"
    echo "  postgres - Use PostgreSQL database configuration (default)"
    echo ""
    echo "This script loads sample FHIR data from data/sample_fhir/bulk-export into the HAPI FHIR server."
    echo "Make sure the FHIR server is running before executing this script."
    exit 1
}

# Check if help was requested
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    usage
fi

# Determine database type
DB_TYPE=${1:-"postgres"}  # Default to PostgreSQL

# Determine which Docker Compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

# Get the appropriate compose file
if [ "$DB_TYPE" == "h2" ]; then
    COMPOSE_FILE="docker-compose.yml"
elif [ "$DB_TYPE" == "postgres" ]; then
    COMPOSE_FILE="docker-compose-postgres.yml"
else
    echo "Invalid database type: $DB_TYPE"
    usage
fi

# Check if the FHIR server is running
echo "Checking if FHIR server is running..."
SERVER_STATUS=$($COMPOSE_CMD -f $COMPOSE_FILE ps hapi-fhir-server --format json 2>/dev/null)

if [ $? -ne 0 ]; then
    echo "Error: Could not check server status. Make sure Docker is running."
    exit 1
fi

if [[ "$SERVER_STATUS" == *"running"* ]]; then
    echo "✅ FHIR server is running."
else
    echo "❌ FHIR server is not running. Please start it first with:"
    echo "./manage-fhir-server.sh start $DB_TYPE"
    exit 1
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed or not in PATH."
    exit 1
fi

# Check if required Python packages are installed
echo "Checking required Python packages..."
MISSING_PACKAGES=0

# Function to check if a Python package is installed
check_package() {
    python3 -c "import $1" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo "❌ Python package '$1' is not installed."
        MISSING_PACKAGES=1
    fi
}

check_package requests
check_package dotenv

if [ $MISSING_PACKAGES -eq 1 ]; then
    echo "Please install missing packages with:"
    echo "pip install requests python-dotenv"
    exit 1
fi

# Execute the Python script to load sample data
echo "Starting data loading process..."
python3 scripts/setup_postgres_fhir.py

# Check if the script executed successfully
if [ $? -eq 0 ]; then
    echo "✅ Sample data loading process completed."
else
    echo "❌ Error occurred during data loading."
    exit 1
fi

exit 0
```

## 3. Implementation Steps

### 3.1 Preparation

1. **Create the PostgreSQL Docker Compose file**

   Create the file `docker-compose-postgres.yml` with the PostgreSQL configuration as shown in section 2.1.

2. **Create the verification script**

   Create the file `scripts/setup_postgres_fhir.py` with the verification code as shown in section 2.2.

3. **Create the server management script**

   Create the file `manage-fhir-server.sh` with the management code as shown in section 2.3.

4. **Update the sample data loading script**

   Update the file `load-sample-data.sh` with the code as shown in section 2.4.

5. **Make scripts executable**

   ```bash
   chmod +x manage-fhir-server.sh
   chmod +x load-sample-data.sh
   chmod +x scripts/setup_postgres_fhir.py
   ```

### 3.2 Starting the PostgreSQL FHIR Server

1. **Start the server with PostgreSQL**

   ```bash
   ./manage-fhir-server.sh start postgres
   ```

2. **Check server status**

   ```bash
   ./manage-fhir-server.sh status
   ```

3. **Load sample data**

   ```bash
   ./load-sample-data.sh postgres
   ```

### 3.3 Testing the PostgreSQL Configuration

1. **Verify server metadata**

   ```bash
   curl -s http://localhost:8080/fhir/metadata | head -20
   ```

2. **Verify PostgreSQL connection**

   ```bash
   docker exec -it fhir-postgres psql -U admin -d hapi -c "SELECT version();"
   ```

3. **Verify data loading**

   ```bash
   curl -s http://localhost:8080/fhir/Patient?_count=5 | jq
   ```

## 4. Documentation Updates

### 4.1 Update Server Documentation

Update the file `docs/guides/fhir/server/03-server_setup.md` to include PostgreSQL configuration:

```markdown
# FHIR Server Setup

## Database Options

The FHIR server can be configured to use different database backends:

### H2 Database (Development/Testing)

H2 is a lightweight, file-based database suitable for development and testing.

```yaml
spring.datasource.url=jdbc:h2:file:/data/hapi/database
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driverClassName=org.h2.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
```

### PostgreSQL Database (Recommended for Production)

PostgreSQL is a robust, scalable database recommended for production environments.

```yaml
spring.datasource.url=*****************************************
spring.datasource.username=admin
spring.datasource.password=admin
spring.datasource.driverClassName=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect
spring.jpa.properties.hibernate.search.enabled=false
```

## Server Management

Use the `manage-fhir-server.sh` script to manage the FHIR server:

```bash
# Start with PostgreSQL (recommended)
./manage-fhir-server.sh start postgres

# Start with H2 (for development/testing)
./manage-fhir-server.sh start h2

# Check server status
./manage-fhir-server.sh status

# View server logs
./manage-fhir-server.sh logs

# Stop the server
./manage-fhir-server.sh stop
```

## Loading Sample Data

Use the `load-sample-data.sh` script to load sample data:

```bash
# Load sample data into PostgreSQL server
./load-sample-data.sh postgres

# Load sample data into H2 server
./load-sample-data.sh h2
```
```

## 5. References

1. HAPI FHIR Database Support: https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
2. HAPI FHIR JPA Server Starter: https://github.com/hapifhir/hapi-fhir-jpaserver-starter
3. PostgreSQL Documentation: https://www.postgresql.org/docs/13/index.html
4. FHIR Bulk Data Format: https://hl7.org/fhir/uv/bulkdata/
5. HAPI FHIR REST API: https://hapifhir.io/hapi-fhir/docs/server_plain/rest_operations.html
