# FHIR Server Setup with PostgreSQL

This document provides instructions for setting up the HAPI FHIR server with PostgreSQL database.

## Table of Contents

- [FHIR Server Setup with PostgreSQL](#fhir-server-setup-with-postgresql)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
  - [Prerequisites](#prerequisites)
  - [Setup Process](#setup-process)
    - [Step 1: Create Project Directory Structure](#step-1-create-project-directory-structure)
    - [Step 2: Create Environment Configuration File](#step-2-create-environment-configuration-file)
    - [Step 3: Create Docker Compose File for HAPI FHIR with PostgreSQL](#step-3-create-docker-compose-file-for-hapi-fhir-with-postgresql)
    - [Step 4: Create Management Script](#step-4-create-management-script)
    - [Step 5: Start the HAPI FHIR Server](#step-5-start-the-hapi-fhir-server)
    - [Step 6: Verify HAPI FHIR Server is Running](#step-6-verify-hapi-fhir-server-is-running)
  - [Loading Data](#loading-data)
  - [Next Steps](#next-steps)

## Overview

This setup creates a local development environment with a HAPI FHIR server connected to a PostgreSQL database. It's designed to be modular, scalable, and suitable for clinical research in a reproducible, isolated setup.

## Prerequisites

- Docker and Docker Compose
- Git
- Python 3.8+

## Setup Process

### Step 1: Create Project Directory Structure

```bash
# Create a directory for the FHIR server project
mkdir -p servers/fhir-server
cd servers/fhir-server
```

### Step 2: Create Environment Configuration File

Create a `.env` file for environment variables:

```bash
cat > .env << EOL
# HAPI FHIR Server Configuration

# Server configuration
FHIR_PORT=8080
FHIR_VERSION=R4

# Authentication (uncomment to enable)
#FHIR_USERNAME=fhir_user
#FHIR_PASSWORD=secure_fhir_password

# PostgreSQL configuration
POSTGRES_DB=hapi
POSTGRES_USER=admin
POSTGRES_PASSWORD=secure_password
POSTGRES_PORT=5432
EOL
```

### Step 3: Create Docker Compose File for HAPI FHIR with PostgreSQL

Create a `docker-compose-postgres.yml` file:

```bash
cat > docker-compose-postgres.yml << EOL
services:
  # HAPI FHIR Server with PostgreSQL
  # Based on the official HAPI FHIR JPA Server Starter example:
  # https://github.com/hapifhir/hapi-fhir-jpaserver-starter
  hapi-fhir-server:
    image: hapiproject/hapi:latest                  # Official HAPI FHIR server image
    ports:
      - "\${FHIR_PORT:-8080}:8080"                   # Exposes container port 8080 to the host port defined by FHIR_PORT or defaults to 8080
    environment:
      # Basic FHIR server settings
      - hapi.fhir.default_encoding=json             # Sets default encoding to JSON
      - hapi.fhir.fhir_version=\${FHIR_VERSION:-R4}  # Sets the FHIR version to use

      # Bulk operations configuration
      - hapi.fhir.bulk_export_enabled=true          # Enables FHIR bulk export feature
      - hapi.fhir.bulk_import_enabled=true          # Enables FHIR bulk import feature
      - hapi.fhir.client_id_strategy=ANY            # Allows any client ID for bulk operations

      # FHIR Server Data Validation Configuration
      # By default, the server enforces data integrity and validation
      # For bulk data loading, uncomment the following lines to disable validation and referential integrity checks
      # After completing data import, comment these lines again to restore default security and integrity settings
      # -----------------------------------------------------------------------------------------------------
      # - hapi.fhir.validation.enabled=false                        # Disables validation
      # - hapi.fhir.validation.request_validator.mode=NONE          # Disables request validation
      # - hapi.fhir.auto_create_placeholder_reference_targets=true  # Creates placeholder resources for missing references
      # - hapi.fhir.enforce_referential_integrity_on_write=false    # Disables referential integrity checks on write
      # - hapi.fhir.enforce_referential_integrity_on_delete=false   # Disables referential integrity checks on delete

      # PostgreSQL configuration
      # Using the recommended dialect from the HAPI FHIR documentation:
      # https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
      - spring.datasource.url=*******************************:\${POSTGRES_PORT}/\${POSTGRES_DB}  # JDBC connection URL to PostgreSQL database
      - spring.datasource.username=\${POSTGRES_USER}                                            # Database username
      - spring.datasource.password=\${POSTGRES_PASSWORD}                                        # Database password
      - spring.datasource.driverClassName=org.postgresql.Driver                                 # JDBC driver for PostgreSQL
      - spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect  # Optimized dialect for HAPI FHIR and PostgreSQL
      - spring.jpa.properties.hibernate.search.enabled=false                                   # Disables advanced search (not required for most PostgreSQL use cases)
    volumes:
      - hapi-data:/data/hapi                        # Persistent volume for FHIR server data
    restart: unless-stopped                         # Restart container unless stopped manually
    depends_on:
      - fhir-postgres                               # Waits for the database to be ready before starting
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]  # Checks if the metadata endpoint is available
      interval: 30s                                 # Interval between health checks
      timeout: 10s                                  # Maximum wait time for a response
      retries: 5                                    # Number of retries before marking as unhealthy

  # PostgreSQL database service
  # PostgreSQL 14 is used for compatibility and stability with HAPI FHIR
  # Note: PostgreSQL 15 and 16 have known compatibility issues with HAPI FHIR
  # See: https://groups.google.com/g/hapi-fhir/c/gQLLcRtlwpI
  fhir-postgres:
    image: postgres:14                              # Official PostgreSQL version 14 image
    environment:
      POSTGRES_DB: \${POSTGRES_DB}                  # Name of the database to create
      POSTGRES_USER: \${POSTGRES_USER}              # Database admin user
      POSTGRES_PASSWORD: \${POSTGRES_PASSWORD}      # Admin user password
    volumes:
      - postgres-data:/var/lib/postgresql/data    # Persistent volume for PostgreSQL data
    restart: unless-stopped                       # Restart container unless stopped manually
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U \${POSTGRES_USER} -d \${POSTGRES_DB}"]  # Checks if the database is ready
      interval: 10s                               # Interval between health checks
      timeout: 5s                                 # Maximum wait time for a response
      retries: 5                                  # Number of retries before marking as unhealthy

volumes:
  hapi-data:                                      # Persistent volume for FHIR server data
  postgres-data:                                  # Persistent volume for PostgreSQL data
EOL
```

### Step 4: Create Management Script

Create a `manage-fhir-server.sh` script:

```bash
cat > manage-fhir-server.sh << EOL
#!/bin/bash

# HAPI FHIR Server Management Script

# Function to display usage information
usage() {
    echo "Usage: \$0 [start|stop|restart|status|logs]"
    echo "  start   - Start the FHIR server"
    echo "  stop    - Stop the FHIR server"
    echo "  restart - Restart the FHIR server"
    echo "  status  - Check the status of the FHIR server"
    echo "  logs    - View the logs of the FHIR server"
    exit 1
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Error: Docker is not installed or not in PATH"
    exit 1
fi

# Determine which Docker Compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

# Check if an argument was provided
if [ \$# -ne 1 ]; then
    usage
fi

# Process the command
case "\$1" in
    start)
        echo "Starting HAPI FHIR server with PostgreSQL..."
        \$COMPOSE_CMD -f docker-compose-postgres.yml up -d
        echo "HAPI FHIR server started. Access at http://localhost:\${FHIR_PORT:-8080}/fhir/metadata"
        ;;
    stop)
        echo "Stopping HAPI FHIR server..."
        \$COMPOSE_CMD -f docker-compose-postgres.yml down
        echo "HAPI FHIR server stopped"
        ;;
    restart)
        echo "Restarting HAPI FHIR server..."
        \$COMPOSE_CMD -f docker-compose-postgres.yml down
        \$COMPOSE_CMD -f docker-compose-postgres.yml up -d
        echo "HAPI FHIR server restarted. Access at http://localhost:\${FHIR_PORT:-8080}/fhir/metadata"
        ;;
    status)
        echo "Checking HAPI FHIR server status..."
        \$COMPOSE_CMD -f docker-compose-postgres.yml ps
        ;;
    logs)
        echo "Showing HAPI FHIR server logs..."
        \$COMPOSE_CMD -f docker-compose-postgres.yml logs -f
        ;;
    *)
        usage
        ;;
esac

exit 0
EOL

chmod +x manage-fhir-server.sh
```

### Step 5: Start the HAPI FHIR Server

```bash
# Start the HAPI FHIR server
./manage-fhir-server.sh start

# Check if containers are running
./manage-fhir-server.sh status

# View logs to monitor startup
./manage-fhir-server.sh logs
```

### Step 6: Verify HAPI FHIR Server is Running

Open a web browser and navigate to:
```
http://localhost:8080/
```

You should see the HAPI FHIR server welcome page. To check the FHIR capabilities, navigate to:
```
http://localhost:8080/fhir/metadata
```

This will display the FHIR CapabilityStatement in JSON format, showing which FHIR resources and operations are supported.

## Loading Data

For detailed information on loading data into the FHIR server, see the [Data Loading Quick Reference](../data-loading/quick-reference.md).

## Next Steps

Now that you have set up the HAPI FHIR server with PostgreSQL, you can:

1. [Create and interact with FHIR resources](09-data_interaction.md)
2. [Load sample data](../data-loading/quick-reference.md)
3. [Explore security considerations](07-security.md)
4. [Learn about future work](08-future_work.md)
