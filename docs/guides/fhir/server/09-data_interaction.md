# FHIR Server Data Interaction and Loading

This guide covers various methods for interacting with the HAPI FHIR server and loading data into it. Understanding these methods is essential for effectively working with FHIR resources during development, testing, and production.

## Overview of Interaction Methods

There are several ways to interact with a FHIR server, each with its own advantages and use cases:

```mermaid
flowchart TD
    A[FHIR Server Interaction Methods] --> B[REST API]
    A --> C[HAPI FHIR CLI Tool]
    A --> D[Custom Scripts]
    A --> E[Web Interface]
    A --> F[Postman/API Tools]

    B --> B1[Direct HTTP Requests]
    B --> B2[FHIR Client Libraries]

    C --> C1[Upload Examples]
    C --> C2[Upload Terminology]

    D --> D1[Python Scripts]
    D --> D2[Java Applications]

    E --> E1[HAPI FHIR Web UI]

    F --> F1[Postman Collections]
    F --> F2[Insomnia]

    style A fill:#f9f9f9,stroke:#333,stroke-width:2px
    style B fill:#e6f3ff,stroke:#333,stroke-width:1px
    style C fill:#e6f3ff,stroke:#333,stroke-width:1px
    style D fill:#e6f3ff,stroke:#333,stroke-width:1px
    style E fill:#e6f3ff,stroke:#333,stroke-width:1px
    style F fill:#e6f3ff,stroke:#333,stroke-width:1px
```

## 1. REST API Direct Interaction

The most fundamental way to interact with a FHIR server is through its REST API using standard HTTP methods.

### Basic CRUD Operations

| Operation | HTTP Method | URL Pattern | Description |
|-----------|-------------|-------------|-------------|
| Create | POST | `[base]/[resource]` | Create a new resource |
| Read | GET | `[base]/[resource]/[id]` | Read a specific resource |
| Update | PUT | `[base]/[resource]/[id]` | Update an existing resource |
| Delete | DELETE | `[base]/[resource]/[id]` | Delete a resource |
| Search | GET | `[base]/[resource]?[parameters]` | Search for resources |

### Example: Creating a Patient Resource with cURL

```bash
curl -X POST \
  -H "Content-Type: application/fhir+json" \
  -d '{
    "resourceType": "Patient",
    "name": [
      {
        "use": "official",
        "family": "Smith",
        "given": ["John"]
      }
    ],
    "gender": "male",
    "birthDate": "1970-01-01"
  }' \
  http://localhost:8080/fhir/Patient
```

### Example: Searching for Patients with cURL

```bash
curl -X GET "http://localhost:8080/fhir/Patient?family=Smith&_format=json"
```

## 2. Transaction Bundle Methods

We provide two primary methods for loading FHIR data, each with different characteristics:

### Method 1: Direct Transaction Bundles

This method is best for bulk loading with permissive server configuration:

```bash
# 1. Configure server for import mode (edit docker-compose-postgres.yml)
# Uncomment these lines in the environment section:
# - hapi.fhir.validation.enabled=false
# - hapi.fhir.validation.request_validator.mode=NONE
# - hapi.fhir.auto_create_placeholder_reference_targets=true
# - hapi.fhir.enforce_referential_integrity_on_write=false
# - hapi.fhir.enforce_referential_integrity_on_delete=false

# 2. Restart the server
./manage-fhir-server.sh restart postgres

# 3. Convert NDJSON to bundles
python servers/fhir-server/scripts/transaction_bundles/ndjson_to_bundle.py \
  --input-file data/sample_fhir/bulk-export/Patient.000.ndjson \
  --output-file data/generated_bundles/Patient_bundle.json

# 4. Send bundles to server
python servers/fhir-server/scripts/transaction_bundles/send_bundle.py \
  --input-file data/generated_bundles/Patient_bundle.json \
  --server-url http://localhost:8080/fhir
```

### Method 2: Selective Loading

This method works with default server configuration but loads only resources with satisfied references:

```bash
# Load data with selective loading
python servers/fhir-server/scripts/transaction_bundles/selective_loader.py \
  --data-dir data/sample_fhir/bulk-export \
  --server-url http://localhost:8080/fhir \
  --verify
```

For detailed instructions on data loading methods, see the [FHIR Data Loading Documentation](../../fhir/data-loading/quick-reference.md).

## 3. Custom Scripts

Custom scripts provide the most flexibility for loading data and interacting with the FHIR server.

### Python Example: Using Our Transaction Bundle Scripts

Our recommended approach is to use the transaction bundle scripts in the `servers/fhir-server/scripts/transaction_bundles` directory:

```python
#!/usr/bin/env python3
import argparse
import os
import subprocess
import sys

def main():
    """Load FHIR data using transaction bundles."""
    parser = argparse.ArgumentParser(description="Load FHIR data using transaction bundles")
    parser.add_argument("--data-dir", default="data/sample_fhir/bulk-export",
                        help="Directory containing NDJSON files")
    parser.add_argument("--server-url", default="http://localhost:8080/fhir",
                        help="FHIR server URL")
    parser.add_argument("--method", choices=["direct", "selective"], default="selective",
                        help="Loading method: direct (all resources) or selective (reference integrity)")
    args = parser.parse_args()

    # Create output directory for bundles
    output_dir = "data/generated_bundles"
    os.makedirs(output_dir, exist_ok=True)

    if args.method == "direct":
        # Direct transaction bundle method
        print(f"Loading data from {args.data_dir} using direct transaction bundles")

        # Find all NDJSON files
        ndjson_files = [f for f in os.listdir(args.data_dir) if f.endswith('.ndjson')]

        for ndjson_file in ndjson_files:
            resource_type = ndjson_file.split('.')[0]
            input_file = os.path.join(args.data_dir, ndjson_file)
            output_file = os.path.join(output_dir, f"{resource_type}_bundle.json")

            # Convert NDJSON to bundle
            print(f"Converting {ndjson_file} to transaction bundle...")
            subprocess.run([
                "python", "servers/fhir-server/scripts/transaction_bundles/ndjson_to_bundle.py",
                "--input-file", input_file,
                "--output-file", output_file
            ])

            # Send bundle to server
            print(f"Sending {resource_type} bundle to server...")
            subprocess.run([
                "python", "servers/fhir-server/scripts/transaction_bundles/send_bundle.py",
                "--input-file", output_file,
                "--server-url", args.server_url
            ])
    else:
        # Selective loading method
        print(f"Loading data from {args.data_dir} using selective loading")
        subprocess.run([
            "python", "servers/fhir-server/scripts/transaction_bundles/selective_loader.py",
            "--data-dir", args.data_dir,
            "--server-url", args.server_url,
            "--verify"
        ])

    print("Data loading complete")

if __name__ == "__main__":
    main()
```

This script provides a unified interface to both of our recommended loading methods:

1. **Direct Transaction Bundles**: Loads all resources regardless of references (requires permissive server configuration)
2. **Selective Loading**: Loads only resources with satisfied references (works with default server configuration)

### Java Example: Using the HAPI FHIR Client Library

```java
import ca.uhn.fhir.context.FhirContext;
import ca.uhn.fhir.rest.client.api.IGenericClient;
import org.hl7.fhir.r4.model.Patient;
import org.hl7.fhir.r4.model.HumanName;
import org.hl7.fhir.r4.model.Enumerations.AdministrativeGender;

public class FhirClientExample {
    public static void main(String[] args) {
        // Create a FHIR context
        FhirContext ctx = FhirContext.forR4();

        // Create a client
        IGenericClient client = ctx.newRestfulGenericClient("http://localhost:8080/fhir");

        // Create a patient
        Patient patient = new Patient();
        patient.addName(new HumanName().setFamily("Smith").addGiven("John"));
        patient.setGender(AdministrativeGender.MALE);
        patient.setBirthDateElement(new DateType("1970-01-01"));

        // Create the patient on the server
        MethodOutcome outcome = client.create().resource(patient).execute();

        // Print the ID of the newly created patient
        System.out.println("Patient created with ID: " + outcome.getId().getValue());
    }
}
```

## 4. Web Interface

The HAPI FHIR server includes a web interface that can be used to interact with the server and manage resources.

### Accessing the Web Interface

Open your browser and navigate to:
```
http://localhost:8080/
```

### Features of the Web Interface

- **Resource Browser**: View and search for resources
- **Resource Creator**: Create new resources using a form
- **Server Information**: View server capabilities and configuration
- **Testing Tools**: Test FHIR operations directly from the browser

## 5. Postman and API Tools

[Postman](https://www.postman.com/) and similar API tools provide a user-friendly interface for interacting with FHIR servers.

### Using Postman with FHIR

1. **Set the request URL**: `http://localhost:8080/fhir/Patient`
2. **Set the HTTP method**: POST for creating resources, GET for retrieving, etc.
3. **Set headers**: `Content-Type: application/fhir+json`
4. **Add request body**: JSON representation of the FHIR resource
5. **Send the request**: Click the Send button to execute the request

### Postman Collection

We provide a Postman collection for common FHIR operations in the `servers/fhir-server/postman` directory. See [Postman Setup](04-postman_setup.md) for detailed instructions.

## Working with Sample Data

Our project includes sample FHIR data in the `data/sample_fhir/bulk-export` directory. This data is in NDJSON format, which is the standard format for FHIR bulk data exports.

### Structure of Sample Data

The sample data includes various FHIR resources:
- `Patient.000.ndjson`: Patient resources
- `Observation.000.ndjson`: Observation resources
- `Condition.000.ndjson`: Condition resources
- And more...

### Loading Sample Data

You can load the sample data using our recommended methods:

```bash
# Method 1: Direct Transaction Bundles (requires server configuration)
python servers/fhir-server/scripts/transaction_bundles/ndjson_to_bundle.py \
  --input-file data/sample_fhir/bulk-export/Patient.000.ndjson \
  --output-file data/generated_bundles/Patient_bundle.json

python servers/fhir-server/scripts/transaction_bundles/send_bundle.py \
  --input-file data/generated_bundles/Patient_bundle.json \
  --server-url http://localhost:8080/fhir

# Method 2: Selective Loading (works with default server configuration)
python servers/fhir-server/scripts/transaction_bundles/selective_loader.py \
  --data-dir data/sample_fhir/bulk-export \
  --server-url http://localhost:8080/fhir \
  --verify
```

## Best Practices for Data Loading

1. **Choose the right loading method**:
   - **Direct Transaction Bundles**: When you need to load all resources regardless of references
   - **Selective Loading**: When you need to maintain reference integrity with default server configuration

2. **Configure the server appropriately**:
   - For Direct Transaction Bundles, configure the server for import mode
   - For Selective Loading, use the default server configuration

3. **Preserve resource IDs**: Both our methods preserve original resource IDs to maintain references between resources.

4. **Handle large datasets efficiently**:
   - Split large NDJSON files into smaller batches
   - Monitor server performance during loading
   - Consider using the `--batch-size` parameter to control bundle size

5. **Verify loaded resources**: Always verify that resources were loaded correctly by using the `--verify` flag with selective loader or checking resource counts after loading.

6. **Restore default configuration**: After using import mode for bulk loading, restore the default server configuration for normal operation.

For more details, consult our complete guide: [Data Loading Quick Reference](../../fhir/data-loading/quick-reference.md)

## References

- [FHIR Transaction Bundle](https://www.hl7.org/fhir/bundle.html#transaction)
- [HAPI FHIR Server Configuration](https://hapifhir.io/hapi-fhir/docs/server_jpa/configuration.html)
- [FHIR RESTful API Specification](https://www.hl7.org/fhir/http.html)
- [FHIR Bulk Data Export](https://hl7.org/fhir/uv/bulkdata/)
