# Documentation and Testing

This section covers the creation of documentation for the FHIR server and testing to ensure it's working correctly.

## Stage 7: Create Documentation

**Objective**: Create comprehensive documentation for the FHIR server setup and usage.

### Step 1: Create a README.md File

Create a README.md file in the project directory with detailed instructions for:
- Setting up the FHIR server
- Using <PERSON><PERSON> to interact with FHIR resources
- Common operations (create, read, update, search)
- Troubleshooting common issues

```bash
cat > README.md << EOL
# HAPI FHIR Server

This project provides a Docker-based setup for a local HAPI FHIR server environment.

## Quick Start

1. Ensure Docker and Docker Compose are installed
2. Clone this repository
3. Run the setup script:
   \`\`\`bash
   ./setup.sh
   \`\`\`
4. Start the FHIR server:
   \`\`\`bash
   ./start-fhir-server.sh start
   \`\`\`
5. Access the FHIR server at http://localhost:8080/fhir/metadata

## Components

- HAPI FHIR Server (R4)
- PostgreSQL database for robust data storage
- <PERSON><PERSON> collection for testing
- Automated test scripts

## Usage

### Starting and Stopping the Server

- Start: \`./start-fhir-server.sh start\`
- Stop: \`./start-fhir-server.sh stop\`
- Restart: \`./start-fhir-server.sh restart\`
- Check status: \`./start-fhir-server.sh status\`
- View logs: \`./start-fhir-server.sh logs\`

### Testing the Server

Run the automated tests:
\`\`\`bash
python3 test_fhir_server.py
\`\`\`

### Using Postman

1. Import the Postman collection: \`HAPI_FHIR_Server.postman_collection.json\`
2. Import the Postman environment: \`HAPI_FHIR_Local.postman_environment.json\`
3. Use the collection to interact with the FHIR server

## Troubleshooting

### Server Not Starting

- Check if Docker is running
- Check if ports 8080 is available
- Check the logs: \`./start-fhir-server.sh logs\`

### Cannot Connect to Server

- Verify the server is running: \`./start-fhir-server.sh status\`
- Check if you can access http://localhost:8080/
- Check your firewall settings

### Authentication Issues

If you've enabled authentication:
- Verify the username and password in the .env file
- Ensure the authentication headers are set correctly in your requests

## License

MIT
EOL
```

### Step 2: Export Postman Collection

1. In Postman, click on the "HAPI FHIR Server" collection
2. Click the "..." (more actions) button
3. Select "Export"
4. Choose "Collection v2.1" format
5. Save the file as "HAPI_FHIR_Server.postman_collection.json" in the project directory

### Step 3: Export Postman Environment

1. In Postman, click on "Environments" in the sidebar
2. Click the "..." (more actions) button next to the "HAPI FHIR Local" environment
3. Select "Export"
4. Save the file as "HAPI_FHIR_Local.postman_environment.json" in the project directory

## Stage 8: Test and Validate

**Objective**: Ensure the FHIR server is working correctly and the documentation is accurate.

### Step 1: Test FHIR Server Functionality
```bash
# Restart the FHIR server to ensure a clean state
docker-compose down
docker-compose up -d

# Wait for the server to start
sleep 30

# Test the server using curl
curl -X GET http://localhost:8080/fhir/metadata -H "Accept: application/fhir+json"

# Verify the response contains a CapabilityStatement
curl -X GET http://localhost:8080/fhir/metadata -H "Accept: application/fhir+json" | grep -q "CapabilityStatement" && echo "FHIR Server is running correctly" || echo "FHIR Server validation failed"

# Test creating a patient
curl -X POST http://localhost:8080/fhir/Patient \
  -H "Content-Type: application/fhir+json" \
  -d '{"resourceType":"Patient","name":[{"family":"Test","given":["Patient"]}]}' \
  -o patient_response.json

# Extract the patient ID for further testing
PATIENT_ID=$(grep -o '"id":"[^"]*"' patient_response.json | cut -d '"' -f 4)
echo "Created test patient with ID: $PATIENT_ID"

# Test retrieving the patient
curl -X GET http://localhost:8080/fhir/Patient/$PATIENT_ID -H "Accept: application/fhir+json"
```

### Step 2: Using the Automated Test Script

The project includes a Python script (`test_fhir_server.py`) for automated testing of the FHIR server. This script performs the following tests:

1. Checks if the FHIR server is running and accessible
2. Creates a test patient resource
3. Retrieves the created patient resource

The script is designed to work with the PostgreSQL database configuration and provides clear feedback on the test results.

#### Running the Tests

To run the automated tests:

```bash
# Make sure the FHIR server is running
./start-fhir-server.sh status

# Install required Python dependencies if not already installed
pip install requests python-dotenv

# Run the test script
python test_fhir_server.py
```

#### Understanding the Test Script

The test script uses the Python `requests` library to interact with the FHIR server's REST API. It performs the following operations:

1. **Server Status Check**: Verifies that the server is running by requesting the `/metadata` endpoint
2. **Create Patient**: Creates a test patient using a POST request to the `/Patient` endpoint
3. **Retrieve Patient**: Retrieves the created patient using a GET request to the `/Patient/{id}` endpoint

If all tests pass, you'll see a success message confirming that the FHIR server is working correctly.

#### Extending the Tests

You can extend the test script to test additional functionality, such as:

- Creating and retrieving other resource types (Observation, Condition, etc.)
- Testing search functionality
- Testing update and delete operations

For more advanced testing scenarios, consider using the [Data Interaction and Loading](09-data_interaction.md) guide as a reference.

#### Reference Implementation

Below is the implementation of the test script for reference:

```bash
cat > test_fhir_server.py << EOL
#!/usr/bin/env python3
"""
Script to test connectivity and functionality of the HAPI FHIR server.
"""
import os
import sys
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# FHIR server configuration
FHIR_PORT = os.getenv("FHIR_PORT", "8080")
FHIR_SERVER_URL = f"http://localhost:{FHIR_PORT}/fhir"
FHIR_USERNAME = os.getenv("FHIR_USERNAME", "")
FHIR_PASSWORD = os.getenv("FHIR_PASSWORD", "")

# Authentication configuration
auth = None
if FHIR_USERNAME and FHIR_PASSWORD:
    auth = (FHIR_USERNAME, FHIR_PASSWORD)

def check_server_status():
    """Check if the FHIR server is running."""
    try:
        response = requests.get(f"{FHIR_SERVER_URL}/metadata", auth=auth)
        if response.status_code == 200:
            print("✅ FHIR server is running.")
            return True
        else:
            print(f"❌ FHIR server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Could not connect to FHIR server: {e}")
        return False

def create_test_patient():
    """Create a test patient in the FHIR server."""
    patient_data = {
        "resourceType": "Patient",
        "active": True,
        "name": [
            {
                "use": "official",
                "family": "Test",
                "given": ["Patient"]
            }
        ],
        "gender": "male",
        "birthDate": "1970-01-01"
    }

    headers = {"Content-Type": "application/fhir+json"}

    try:
        response = requests.post(
            f"{FHIR_SERVER_URL}/Patient",
            json=patient_data,
            headers=headers,
            auth=auth
        )

        if response.status_code in [200, 201]:
            patient_id = response.json().get("id")
            print(f"✅ Test patient created with ID: {patient_id}")
            return patient_id
        else:
            print(f"❌ Error creating test patient: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when creating patient: {e}")
        return None

def get_patient(patient_id):
    """Get a patient by ID."""
    try:
        response = requests.get(
            f"{FHIR_SERVER_URL}/Patient/{patient_id}",
            auth=auth
        )

        if response.status_code == 200:
            print(f"✅ Patient retrieved successfully:")
            patient_data = response.json()
            print(f"  - ID: {patient_data.get('id')}")
            name = patient_data.get('name', [{}])[0]
            full_name = f"{' '.join(name.get('given', []))} {name.get('family', '')}"
            print(f"  - Name: {full_name}")
            print(f"  - Gender: {patient_data.get('gender', 'not specified')}")
            print(f"  - Birth date: {patient_data.get('birthDate', 'not specified')}")
            return True
        else:
            print(f"❌ Error retrieving patient: {response.status_code}")
            print(response.text)
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when retrieving patient: {e}")
        return False

def run_tests():
    """Run all tests."""
    print("\\n=== HAPI FHIR Server Test ===\\n")

    # Check server status
    if not check_server_status():
        print("\\n❌ The FHIR server is not available. Make sure it is running.")
        sys.exit(1)

    # Create test patient
    print("\\n--- Creating test patient ---")
    patient_id = create_test_patient()
    if not patient_id:
        print("\\n❌ Could not create the test patient.")
        sys.exit(1)

    # Retrieve patient
    print("\\n--- Retrieving patient ---")
    if not get_patient(patient_id):
        print("\\n❌ Could not retrieve the patient.")
        sys.exit(1)

    print("\\n✅ All tests completed successfully.")
    print(f"The HAPI FHIR server is working correctly at: {FHIR_SERVER_URL}")

if __name__ == "__main__":
    run_tests()
EOL

chmod +x test_fhir_server.py
```

For more information on testing and interacting with the FHIR server, see the [Data Interaction and Loading](09-data_interaction.md) guide.

### Step 3: Validate Postman Collection
1. Import the exported collection into a new Postman instance
2. Import the exported environment into a new Postman instance
3. Run through each request to ensure they work as expected

### Step 4: Review Documentation
1. Review the README.md file for accuracy
2. Ensure all commands and instructions are correct
3. Check for any missing information or steps

## Next Steps

Now that you've documented and tested your FHIR server, you can proceed to the [Deployment and Exportability](06-deployment.md) section to learn how to automate the setup process and make the project easily exportable.
