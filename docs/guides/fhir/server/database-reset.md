# FHIR Server Database Reset Guide

This guide explains how to reset the HAPI FHIR server database to a clean state, which is useful for development environments when you need to remove test data or fix issues like duplicate resources.

## When to Reset the Database

You might want to reset the FHIR server database in the following scenarios:

1. **Duplicate Resources**: When you have duplicate resources due to script errors or testing
2. **Data Corruption**: When the database contains corrupted or invalid data
3. **Fresh Start**: When you want to start with a clean database for testing
4. **Schema Changes**: After significant schema changes that require a fresh database

## Prerequisites

- Docker and Docker Compose installed
- Access to the project's scripts directory
- The FHIR server configured with PostgreSQL

## Step-by-Step Reset Process

### 1. Stop the FHIR Server

First, stop all running containers:

```bash
cd servers/fhir-server
./manage-fhir-server.sh stop postgres
```

This command will stop and remove all containers related to the FHIR server.

### 2. Remove the PostgreSQL Volume

Identify and remove the PostgreSQL data volume:

```bash
# List volumes to identify the PostgreSQL volume
docker volume ls | grep postgres

# Remove the volume
docker volume rm fhir-server_postgres-data
```

This step permanently deletes all data in the PostgreSQL database, so make sure you have backups if needed.

### 3. Restart the FHIR Server

Start the FHIR server with a fresh database:

```bash
./manage-fhir-server.sh start postgres
```

This command will:
- Create a new PostgreSQL data volume
- Start the PostgreSQL container
- Start the HAPI FHIR server container

### 4. Wait for Server Initialization

The FHIR server needs some time to initialize. You can check the status with:

```bash
./manage-fhir-server.sh status
```

Wait until both containers show as "healthy" or verify that the server is responding:

```bash
curl -s "http://localhost:8080/fhir/metadata" > /dev/null && echo "Server is responding" || echo "Server is not responding yet"
```

### 5. Load Sample Data

Once the server is running, you can load sample data using the updated scripts that prevent duplicates:

```bash
python scripts/setup_postgres_fhir.py
```

Alternatively, you can use the `load_sample_data.py` script if you need more control over the loading process:

```bash
python scripts/load_sample_data.py [--data-dir PATH]
```

### 6. Verify Clean Database

To verify that the database is clean and contains no duplicates:

```bash
# Get all patients with their identifiers
curl -s "http://localhost:8080/fhir/Patient?_elements=identifier&_count=100" > patient_identifiers.json

# Analyze for duplicates (using Python)
python -c "
import json
import collections
from collections import defaultdict

# Load patient data
with open('patient_identifiers.json', 'r') as f:
    data = json.load(f)

# Extract business identifiers
patients_by_business_id = defaultdict(list)
for entry in data.get('entry', []):
    resource = entry.get('resource', {})
    logical_id = resource.get('id', 'unknown')
    identifiers = resource.get('identifier', [])
    
    if identifiers:
        system = identifiers[0].get('system', 'unknown')
        value = identifiers[0].get('value', 'unknown')
        business_id = f'{system}|{value}'
        patients_by_business_id[business_id].append(logical_id)

# Check for duplicates
duplicates_found = False
for business_id, ids in patients_by_business_id.items():
    if len(ids) > 1:
        duplicates_found = True
        print(f'Duplicate found: {business_id} in patients with IDs: {ids}')

if not duplicates_found:
    print('No duplicates found. The database is clean.')
"
```

## Understanding the Reset Process

### What Happens During Reset

1. **Container Removal**: All FHIR server containers are stopped and removed
2. **Volume Deletion**: The PostgreSQL data volume is deleted, removing all database data
3. **Fresh Start**: New containers and volumes are created when restarting
4. **Schema Creation**: HAPI FHIR automatically creates the necessary database schema
5. **Data Loading**: Sample data is loaded using scripts with duplicate prevention

### Benefits of Using Updated Scripts

The updated scripts (`setup_postgres_fhir.py` and `load_sample_data.py`) include:

1. **Conditional Operations**: Check if resources with the same business identifiers already exist
2. **Update Instead of Duplicate**: Update existing resources instead of creating duplicates
3. **Detailed Reporting**: Show statistics about created vs. updated resources

## Best Practices

1. **Development Environment Only**: This reset process is intended for development environments only
2. **Backup Important Data**: Always backup any important data before resetting
3. **Use Conditional Operations**: Always use conditional operations when loading data
4. **Verify After Reset**: Always verify that the database is clean after reset
5. **Document Custom Configurations**: If you have custom configurations, document them before reset

## Troubleshooting

### Server Doesn't Start After Reset

If the server doesn't start after reset:

1. Check Docker logs: `docker logs hapi-fhir-server`
2. Verify PostgreSQL is running: `docker logs fhir-postgres`
3. Ensure ports are available: `netstat -an | grep 8080`

### Data Loading Errors

If you encounter errors during data loading:

1. Check that the FHIR server is fully initialized before loading data
2. Verify the data files exist and are in the correct format
3. Check for errors in the script output

## Related Documentation

- [FHIR Server Setup Guide](./03-server_setup.md)
- [PostgreSQL Migration Guide](./postgresql-migration.md)
- [FHIR Resource Identifiers Guide](../resources/identifiers.md)
