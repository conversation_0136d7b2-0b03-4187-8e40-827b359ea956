# FHIR API Documentation

This section contains documentation related to the FHIR API, tools for interacting with FHIR servers, and terminology services.

## Available Documents

### [FHIR Query Guide: Building Effective FHIR Queries](fhir_query_guide.md)

A comprehensive guide on building effective FHIR queries based on our implementation experience. Includes:

- FHIR query syntax and structure
- Common query parameters and patterns
- Resource-specific query examples (Patient, Observation, Condition)
- Advanced query techniques
- Troubleshooting common issues
- Implementation details from our application

### [State of the Art: Tools for Interacting with FHIR Servers (2025)](fhir_tools_2025.md)

A comprehensive analysis of tools available in 2025 for interacting with FHIR servers, focusing on their applicability for ETL projects transforming FHIR to OMOP and additional analytics. Includes:

- FHIR client libraries for different programming languages
- ETL tools for FHIR to OMOP transformation
- Analytics and visualization tools
- Compatibility with HAPI FHIR Server 8.0.0
- Recommendations for our project

### [FHIR Terminology Services](terminology_services.md)

Examples and guides for using FHIR terminology services, including:

- CodeSystem operations ($lookup, $validate-code, $subsumes)
- ValueSet operations ($expand, $validate-code)
- ConceptMap operations ($translate)
- Python examples for interacting with terminology services
- Integration with FHIR to OMOP transformations

## Additional Resources

- [Official FHIR Documentation](https://www.hl7.org/fhir/)
- [HAPI FHIR Documentation](https://hapifhir.io/hapi-fhir/docs/)
- [FHIR RESTful API](https://www.hl7.org/fhir/http.html)
