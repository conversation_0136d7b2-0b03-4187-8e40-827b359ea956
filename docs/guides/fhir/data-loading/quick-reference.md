# FHIR Data Loading Quick Reference

A concise guide for loading FHIR data using transaction bundles and selective loading approaches.

## Table of Contents

- [Overview](#overview)
- [Server Configuration](#server-configuration)
- [Loading Methods](#loading-methods)
- [Scripts](#scripts)
- [Command Reference](#command-reference)
- [Common Issues](#common-issues)

## Overview

We offer two primary methods for loading FHIR data, each with different characteristics:

| Method | Description | Best For | Limitations |
|--------|-------------|----------|------------|
| **Direct Transaction Bundles** | Loads all resources regardless of references | Bulk loading, initial data population | Requires permissive server configuration |
| **Selective Loading** | Loads only resources with satisfied references | Incremental loading, reference integrity | Loads fewer resources |

## Server Configuration

**Critical**: Server configuration dramatically affects data loading success.

### Configuration Comparison

| Setting | Default | Import Mode | Effect |
|---------|---------|-------------|--------|
| `validation.enabled` | `true` | `false` | With default: Rejects invalid resources<br>With import mode: Accepts all resources |
| `enforce_referential_integrity_on_write` | `true` | `false` | With default: Rejects unresolved references<br>With import mode: Accepts all references |
| `auto_create_placeholder_reference_targets` | `false` | `true` | With default: No placeholders<br>With import mode: Creates placeholders for missing references |

### Enabling Import Mode

1. Edit `servers/fhir-server/docker-compose-postgres.yml`
2. Uncomment the import configuration lines:
   ```yaml
   - hapi.fhir.validation.enabled=false
   - hapi.fhir.validation.request_validator.mode=NONE
   - hapi.fhir.auto_create_placeholder_reference_targets=true
   - hapi.fhir.enforce_referential_integrity_on_write=false
   - hapi.fhir.enforce_referential_integrity_on_delete=false
   ```
3. Restart the server: `./manage-fhir-server.sh restart postgres`
4. **Important**: Restore default settings after import

## Loading Methods

### Method 1: Direct Transaction Bundles

Best for bulk loading with permissive server configuration.

```bash
# 1. Configure server for import mode (see above)
# 2. Convert NDJSON to bundles
python servers/fhir-server/scripts/transaction_bundles/ndjson_to_bundle.py \
  --input-file data/sample_fhir/bulk-export/Patient.000.ndjson \
  --output-file data/generated_bundles/Patient/Patient_bundle.json

# 3. Load all bundles at once
cd servers/fhir-server
python scripts/transaction_bundles/load_all_bundles.py \
  --bundle-dir ../../data/generated_bundles

# 4. Verify loading
curl -s "http://localhost:8080/fhir/Patient?_summary=count"
```

### Method 2: Selective Loading

Best for maintaining reference integrity with default server configuration.

```bash
# Run selective loader
python servers/fhir-server/scripts/transaction_bundles/selective_loader.py \
  --data-dir data/sample_fhir/bulk-export \
  --server-url http://localhost:8080/fhir \
  --verify
```

## Scripts

| Script | Purpose | Key Features |
|--------|---------|-------------|
| `ndjson_to_bundle.py` | Converts NDJSON to transaction bundles | Preserves resource IDs, handles batching |
| `send_bundle.py` | Sends bundles to FHIR server | Error handling, response parsing |
| `load_all_bundles.py` | Loads all bundles from a directory | Recursive search, progress tracking |
| `selective_loader.py` | Selective loading with dependency resolution | Incremental loading, reference validation |
| `reference_analyzer.py` | Analyzes references between resources | Dependency graph, loading order |
| `verify_loaded_resources.py` | Verifies loaded resources | Reference validation, sampling |
| `test_fhir_server.py` | Tests FHIR server connectivity | Creates test patient, verifies retrieval |

## Command Reference

### NDJSON to Bundle Converter

```bash
# Process a single file
python servers/fhir-server/scripts/transaction_bundles/ndjson_to_bundle.py \
  --input-file INPUT_FILE \
  --output-file OUTPUT_FILE \
  --batch-size BATCH_SIZE

# Process an entire directory
python servers/fhir-server/scripts/transaction_bundles/ndjson_to_bundle.py \
  --input-dir INPUT_DIR \
  --output-dir OUTPUT_DIR \
  --batch-size BATCH_SIZE
```

| Option | Default | Description |
|--------|---------|-------------|
| `--input-file` | - | NDJSON file to convert |
| `--input-dir` | - | Directory containing NDJSON files |
| `--output-file` | - | Output bundle file (for single file) |
| `--output-dir` | `data/generated_bundles` | Directory to save output files |
| `--batch-size` | `500` | Max resources per bundle |

### Bundle Sender

```bash
python servers/fhir-server/scripts/transaction_bundles/send_bundle.py \
  --input-file INPUT_FILE \
  --server-url SERVER_URL
```

| Option | Default | Description |
|--------|---------|-------------|
| `--input-file` | - | Bundle file to send |
| `--server-url` | `http://localhost:8080/fhir` | FHIR server URL |

### Batch Bundle Loader with Performance Metrics

```bash
python servers/fhir-server/scripts/transaction_bundles/load_all_bundles.py \
  --bundle-dir BUNDLE_DIR \
  --server-url SERVER_URL \
  --pattern PATTERN \
  --export-performance \
  --test-id TEST_ID
```

| Option | Default | Description |
|--------|---------|-------------|
| `--bundle-dir` | `data/generated_bundles` | Directory containing bundle files |
| `--server-url` | `http://localhost:8080/fhir` | FHIR server URL |
| `--pattern` | `*.json` | File pattern to match |
| `--test-id` | timestamp | Identifier for this test run |
| `--export-performance` | - | Export performance data to JSON |
| `--output-dir` | `./performance_reports` | Directory for performance reports |
| `--sampling-interval` | `5` | Seconds between resource samples |

For detailed information about performance metrics and testing, see the [Performance Metrics Guide](performance-metrics.md).

### Selective Loader

```bash
python servers/fhir-server/scripts/transaction_bundles/selective_loader.py \
  --data-dir DATA_DIR \
  --server-url SERVER_URL \
  --verify
```

| Option | Default | Description |
|--------|---------|-------------|
| `--data-dir` | `data/sample_fhir/bulk-export` | Directory with NDJSON files |
| `--server-url` | `http://localhost:8080/fhir` | FHIR server URL |
| `--verify` | - | Verify loaded resources |

### Server Test Script

```bash
python servers/fhir-server/scripts/test_fhir_server.py \
  --server-url SERVER_URL
```

| Option | Default | Description |
|--------|---------|-------------|
| `--server-url` | From env or `http://localhost:8080/fhir` | FHIR server URL |

## Common Issues

| Issue | Solution |
|-------|----------|
| **With Default Configuration** | |
| Resources rejected with reference errors | Use selective loader or enable import mode |
| Only Patient/Device resources load | Normal with default settings and selective loader |
| **With Import Mode Configuration** | |
| All resources load but with placeholder references | Expected behavior, useful for initial loading |
| Server performance degradation | Reduce batch size or use selective loader |
| **General Issues** | |
| Connection errors | Check if FHIR server is running and accessible |
| Timeout errors | Reduce batch size or split into multiple runs |
| Conflict errors (409) | Resources already exist, clean server first |
