# FHIR Data Loading Methods: CLI Approach (Deprecated)

> **Important Notice**: This document describes methods using the HAPI FHIR Command Line Interface (CLI) tool that we have **deprecated** in our project. We now recommend using our established methods documented in the [Quick Reference Guide](quick-reference.md).

## Why We Deprecated CLI Methods

After extensive testing, we found that the HAPI FHIR CLI methods had several limitations and reliability issues:

1. **Connection Problems**: The CLI's bulk-import command had persistent network configuration issues
2. **Inconsistent Implementation**: The $import operation implementation didn't follow the FHIR specification
3. **Reference Handling**: CLI methods didn't reliably preserve references between resources
4. **Complexity**: Required additional setup and configuration steps

## Recommended Alternatives

We now recommend two established methods for loading FHIR data:

1. **Direct Transaction Bundles**: For bulk loading with permissive server configuration
   - See [Direct Transaction Tutorial](direct-transaction-tutorial.md)

2. **Selective Loading**: For incremental loading with reference integrity
   - See [Selective Loading Guide](selective-loading.md)

Both methods are documented in our [Quick Reference Guide](quick-reference.md).

## Historical Testing Results

We tested the HAPI FHIR CLI with version 8.0.0 and encountered several issues:

```
HTTP 500 : Job is in FAILED state with 4 error count. Last error: Too many errors: 4. Last error msg was HAPI-2054: Connect to localhost:9091 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused
```

The CLI attempted to create a local HTTP server to serve NDJSON files but had connection issues due to network configuration, Docker networking limitations, firewall settings, and port conflicts.

We also tested the $import operation directly using curl, but the server expected a Bundle when the specification requires a Parameters resource:

```json
{
  "resourceType": "OperationOutcome",
  "issue": [
    {
      "severity": "error",
      "code": "processing",
      "diagnostics": "Failed to parse request body as Bundle resource"
    }
  ]
}
```

## References

- [FHIR Bulk Data Import Operation](https://hl7.org/fhir/uv/bulkdata/import/index.html)
- [FHIR Transaction Bundle](https://www.hl7.org/fhir/bundle.html#transaction)
- [Our Quick Reference Guide](quick-reference.md)


