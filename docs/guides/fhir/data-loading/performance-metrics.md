# FHIR Bundle Loading Performance Metrics

This document describes the performance metrics features added to the FHIR bundle loading scripts in the fhir-omop project.

## Overview

The enhanced scripts collect and report detailed performance metrics during the FHIR bundle loading process, enabling systematic performance testing and optimization. These metrics provide valuable insights for hardware sizing, cost optimization, and scalability planning.

### Key Metrics Collected

| Metric Category | Specific Metrics | Purpose |
|----------------|------------------|---------|
| System Resources | CPU usage (%, cores) | Identify CPU bottlenecks |
| | Memory usage (GB, %) | Identify memory constraints |
| | Disk I/O (MB read/written) | Detect storage bottlenecks |
| | Network I/O (MB sent/received) | Identify network limitations |
| Throughput | Resources per second | Measure processing efficiency |
| | Bundles per second | Compare batch processing rates |
| | Average resource size | Understand data characteristics |
| Processing | Success/failure rates | Ensure data quality |
| | Processing times (total, average) | Measure overall performance |
| | Resource type breakdown | Identify type-specific issues |
| Hardware | CPU cores (physical/logical) | Document test environment |
| | Total system memory | Document test environment |
| | Platform information | Enable cross-platform comparison |

## Requirements

The performance metrics features are integrated into the project's conda environment:

```bash
# Create and activate the conda environment
conda env create -f environment.yml
conda activate fhir-omop
```

## Usage

The performance metrics functionality is integrated into the `load_all_bundles.py` script, which is part of the FHIR data loading workflow.

### Basic Usage

To load bundles with performance metrics:

```bash
cd servers/fhir-server
python scripts/transaction_bundles/load_all_bundles.py --bundle-dir data/generated_bundles/bulk_export_bundles --server-url http://localhost:8080/fhir
```

This will display the enhanced summary report with performance metrics at the end of the loading process.

### Command Line Arguments

| Argument | Description | Example |
|----------|-------------|---------|
| `--bundle-dir` | Directory containing FHIR transaction bundles | `--bundle-dir data/generated_bundles` |
| `--server-url` | URL of the FHIR server | `--server-url http://localhost:8080/fhir` |
| `--pattern` | File pattern to match | `--pattern "Patient_*.json"` |
| `--test-id` | Identifier for this test run | `--test-id "8cpu_16gb_test1"` |
| `--export-performance` | Export performance data to JSON | `--export-performance` |
| `--output-dir` | Directory for performance reports | `--output-dir ./performance_reports` |
| `--sampling-interval` | Seconds between resource samples | `--sampling-interval 10` |

### Common Usage Scenarios

#### Exporting Performance Data

To export performance data for later analysis:

```bash
python scripts/transaction_bundles/load_all_bundles.py \
  --bundle-dir data/generated_bundles/bulk_export_bundles \
  --server-url http://localhost:8080/fhir \
  --export-performance \
  --output-dir ./performance_reports
```

This saves a JSON file with detailed performance data to the specified directory.

#### Comparing Hardware Configurations

To compare different hardware configurations:

```bash
# Test on configuration 1
python scripts/transaction_bundles/load_all_bundles.py \
  --bundle-dir data/generated_bundles/bulk_export_bundles \
  --server-url http://localhost:8080/fhir \
  --test-id "8cpu_16gb" \
  --export-performance

# Test on configuration 2
python scripts/transaction_bundles/load_all_bundles.py \
  --bundle-dir data/generated_bundles/bulk_export_bundles \
  --server-url http://localhost:8080/fhir \
  --test-id "16cpu_32gb" \
  --export-performance
```

#### Adjusting Sampling Frequency

To change how frequently resource usage is sampled:

```bash
python scripts/transaction_bundles/load_all_bundles.py \
  --bundle-dir data/generated_bundles/bulk_export_bundles \
  --server-url http://localhost:8080/fhir \
  --sampling-interval 10
```

A higher sampling interval (in seconds) reduces overhead but provides less granular data. For short tests, use 1-5 seconds; for longer tests, 10-30 seconds is appropriate.

## Output Formats

### Console Report

The script generates a comprehensive summary report in the console with the following sections:

```
================================================================================
                       FHIR BUNDLE LOADING SUMMARY REPORT
================================================================================

RESOURCE TYPE BREAKDOWN:
--------------------------------------------------------------------------------
Resource Type        Bundles              Resources            Time            Success Rate
--------------------------------------------------------------------------------
Observation          20/20                9878/9878            5.86s           100.0%
DiagnosticReport     5/5                  2101/2101            3.56s           100.0%
...

OVERALL STATISTICS:
--------------------------------------------------------------------------------
Total Processing Time:      00:00:40 (HH:MM:SS)
Average Time Per Bundle:    0.37 seconds
Total Bundles Processed:    46/46 (100.0%)
Total Resources Processed:  18966/18966 (100.0%)

PERFORMANCE METRICS:
--------------------------------------------------------------------------------
Resources Per Second:       467.03
Bundles Per Second:         1.13
Average Resource Size:      412.30 resources/bundle

SYSTEM RESOURCE USAGE:
--------------------------------------------------------------------------------
CPU Cores:                  14 physical, 14 logical
Total System Memory:        36.0 GB
Peak CPU Usage:             37.9%
Peak Memory Usage:          13.25 GB (76.5%)
Average CPU Usage:          20.4%
Average Memory Usage:       12.92 GB

TOP RESOURCE TYPES BY VOLUME:
--------------------------------------------------------------------------------
1. Observation: 9878 resources (52.1% of total)
2. DiagnosticReport: 2101 resources (11.1% of total)
...
```

### Exported JSON Data

When using the `--export-performance` flag, the script exports a JSON file with detailed performance data:

```json
{
  "test_id": "20250514_104951",
  "timestamp": "20250514_105031",
  "system_info": {
    "platform": "Darwin",
    "cpu_count_logical": 14,
    "cpu_count_physical": 14,
    "total_memory_gb": 36.0,
    ...
  },
  "overall_stats": {
    "total_bundles": 46,
    "resources_per_second": 467.03,
    "peak_cpu_percent": 37.9,
    ...
  },
  "resource_type_stats": {
    "Observation": {
      "bundles_processed": 20,
      "resources_processed": 9878,
      ...
    },
    ...
  },
  "resource_usage_samples": [
    {
      "timestamp": **********.17,
      "elapsed_time": 0,
      "cpu_percent": 12.5,
      "memory_used_gb": 12.45,
      ...
    },
    ...
  ]
}
```

## Performance Testing Methodology

### Test Design Principles

For meaningful performance testing, follow these principles:

1. **Consistency**: Use identical datasets across all test runs
2. **Isolation**: Vary only one parameter at a time (CPU, memory, etc.)
3. **Repetition**: Run multiple tests for each configuration to account for variability
4. **Documentation**: Record hardware specifications and environment details
5. **Progression**: Follow a structured testing approach:

### Recommended Testing Progression

```mermaid
graph TD
    A[Baseline Test] --> B[Hardware Scaling Tests]
    B --> C[Cost-Efficiency Analysis]
    C --> D[Volume Scaling Tests]
    D --> E[Bottleneck Identification]
    E --> F[Optimization Implementation]
    F --> G[Validation Tests]

    style A fill:#d770ad,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style D fill:#5d8aa8,stroke:#333,stroke-width:2px,color:#fff
    style E fill:#6b8e23,stroke:#333,stroke-width:2px,color:#fff
    style F fill:#6b8e23,stroke:#333,stroke-width:2px,color:#fff
    style G fill:#b87333,stroke:#333,stroke-width:2px,color:#fff
```

### Data Analysis Techniques

The exported JSON files can be analyzed using:

1. **Python with Pandas/Matplotlib**: For detailed statistical analysis and visualization
2. **Jupyter Notebooks**: For interactive exploration and documentation
3. **Excel with Power Query**: For quick analysis without programming
4. **Tableau/PowerBI**: For creating dashboards and sharing insights

Key metrics to compare include:

| Metric | Calculation | Significance |
|--------|-------------|-------------|
| Resources per second | `total_resources / elapsed_time` | Overall throughput |
| Cost-efficiency | `resources_per_second / hardware_cost` | Value for money |
| CPU efficiency | `resources_per_second / cpu_cores` | Processing efficiency |
| Memory efficiency | `resources_per_second / memory_gb` | RAM utilization |
| Scaling factor | `throughput_increase / resource_increase` | Scaling efficiency |

## Integration with Other Tools

The performance metrics functionality integrates with other components of the fhir-omop project:

- **FHIR Server Management**: Use with `manage-fhir-server.sh` to test different server configurations
- **ETL Pipeline**: Measure performance at different stages of the ETL process
- **Data Visualization**: Export data for visualization with Plotly or other tools
- **CI/CD Pipeline**: Incorporate performance testing into automated workflows
