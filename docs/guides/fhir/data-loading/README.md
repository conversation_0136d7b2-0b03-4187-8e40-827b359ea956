# FHIR Data Loading Methods

This directory contains documentation for loading data into the HAPI FHIR server. We provide two primary methods for loading FHIR data, each with different characteristics:

## Method 1: Direct Transaction Bundles

This method is best for bulk loading with permissive server configuration:

- **Description**: Converts NDJSON files to transaction bundles and sends them directly to the FHIR server
- **Server Configuration**: Requires permissive server configuration (validation and referential integrity checks disabled)
- **Advantages**: Loads all resources regardless of references, preserves original resource IDs
- **Use Case**: Initial data loading, bulk loading
- **Documentation**: [Direct Transaction Tutorial](direct-transaction-tutorial.md)

## Method 2: Selective Loading

This method works with default server configuration but loads only resources with satisfied references:

- **Description**: Analyzes references between resources and loads only those with satisfied dependencies
- **Server Configuration**: Works with default server configuration
- **Advantages**: Maintains reference integrity, no placeholder resources
- **Use Case**: Incremental loading, reference-preserving loading
- **Documentation**: [Selective Loading Guide](selective-loading.md)

## Quick Reference

For a concise overview of both methods, see the [Quick Reference Guide](quick-reference.md).

## Deprecated Methods

We previously explored using the HAPI FHIR CLI for data loading but found it had limitations and reliability issues. For historical context, see [HAPI CLI Methods (Deprecated)](hapi-cli-methods.md).

## Scripts

The implementation of these methods can be found in the `servers/fhir-server/scripts/transaction_bundles` directory:

- `ndjson_to_bundle.py`: Converts NDJSON files to transaction bundles
- `send_bundle.py`: Sends transaction bundles to the FHIR server
- `load_all_bundles.py`: Loads all transaction bundles from a directory with performance metrics
- `selective_loader.py`: Implements the selective loading approach
- `stats.py`: Provides statistics tracking and performance metrics for bundle processing

For detailed information about performance metrics and testing, see the [Performance Metrics Guide](../performance-metrics/README.md).

## Server Configuration

For the Direct Transaction Bundles method, you need to configure the server to accept resources with unresolved references. This is done by uncommenting these lines in `docker-compose-postgres.yml`:

```yaml
# - hapi.fhir.validation.enabled=false                        # Disables validation
# - hapi.fhir.validation.request_validator.mode=NONE          # Disables request validation
# - hapi.fhir.auto_create_placeholder_reference_targets=true  # Creates placeholder resources for missing references
# - hapi.fhir.enforce_referential_integrity_on_write=false    # Disables referential integrity checks on write
# - hapi.fhir.enforce_referential_integrity_on_delete=false   # Disables referential integrity checks on delete
```

After loading data, comment these lines again to restore default security and integrity settings.

## Workflow Recommendation

1. **Before Data Import**:
   - Uncomment the data import configuration lines
   - Restart the FHIR server: `./manage-fhir-server.sh restart`

2. **After Data Import**:
   - Comment out the data import configuration lines
   - Restart the FHIR server to restore default settings
   - Verify data integrity with appropriate queries

This approach allows you to temporarily relax constraints for data loading while maintaining proper security and integrity for normal operation.
