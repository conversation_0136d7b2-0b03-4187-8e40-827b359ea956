# FHIR Resource Identifiers Guide

This guide explains the different types of identifiers used in FHIR resources and how they are handled in our implementation.

## Types of Identifiers in FHIR

FHIR uses several types of identifiers for different purposes:

| Identifier Type | Location | Purpose | Assigned By | Example |
|-----------------|----------|---------|-------------|---------|
| Logical ID | `Resource.id` | Identifies a resource within a server | FHIR Server | `"id": "52"` |
| Version ID | `Resource.meta.versionId` | Identifies a specific version of a resource | FHIR Server | `"versionId": "1"` |
| Business Identifier | `Resource.identifier` | Identifies the real-world entity | Client Application | `"identifier": [{"system": "http://hospital.example.org", "value": "12345"}]` |
| Resource References | Various fields | Links resources together | Client Application | `"subject": {"reference": "Patient/52"}` |

## Logical IDs (Resource.id)

- **Definition**: The primary identifier assigned by the server to each resource
- **Characteristics**:
  - Unique within the resource type on the server
  - Immutable once assigned
  - Used in URLs to access the resource
  - Limited to 64 characters (letters, numbers, "-" and ".")
- **Example**: `"id": "52"`
- **Usage**: `GET [base]/Patient/52`

## Version IDs (Resource.meta.versionId)

- **Definition**: Identifies a specific version of a resource
- **Characteristics**:
  - Changes with each update to the resource
  - Used for concurrency control and accessing historical versions
  - Automatically managed by the FHIR server
  - Increments only when updating the same logical resource (same Resource.id)
  - Does not increment when creating new resources, even with identical business identifiers
- **Example**: `"meta": {"versionId": "2"}`
- **Usage**: `GET [base]/Patient/52/_history/2`

### How Versioning Works in HAPI FHIR

1. **Initial Creation (POST)**:
   - Server assigns a new logical ID and initial version (typically "1")
   - Example: `"id": "52", "meta": {"versionId": "1"}`

2. **Update (PUT)**:
   - When updating the same resource (using its logical ID), version increments
   - Example: `"id": "52", "meta": {"versionId": "2"}`

3. **Conditional Update (PUT with search parameters)**:
   - If resource exists: Updates it and increments version
   - If resource doesn't exist: Creates new resource with version "1"
   - Example: `PUT [base]/Patient?identifier=http://example.org|12345`

4. **Important Note on Duplicates**:
   - Creating multiple resources with identical business identifiers but without conditional operations results in separate resources with different logical IDs, each with their own version "1"
   - These are considered different resources by the server despite representing the same real-world entity

## Business Identifiers (Resource.identifier)

- **Definition**: External identifiers that represent the real-world entity
- **Characteristics**:
  - Remain constant even if the resource moves between servers
  - Have a system (namespace) and a value
  - Can be used for searching and conditional operations
  - Not managed by the FHIR server
- **Example**:
  ```json
  "identifier": [
    {
      "system": "http://hospital.example.org/mrn",
      "value": "12345"
    }
  ]
  ```
- **Usage**: `GET [base]/Patient?identifier=http://hospital.example.org/mrn|12345`

## Resource References

- **Definition**: Links between resources
- **Types**:
  - **Logical references**: `Patient/52`
  - **Absolute references**: `http://server.example.org/fhir/Patient/52`
  - **Conditional references**: `Patient?identifier=http://hospital.example.org/mrn|12345`
  - **Versioned references**: `Patient/52/_history/2`
- **Example**:
  ```json
  "subject": {
    "reference": "Patient/52",
    "display": "John Smith"
  }
  ```

## Best Practices for Identifier Management

### 1. Always Include Business Identifiers

Every resource should have at least one business identifier with a proper system URI:

```json
"identifier": [
  {
    "system": "http://your-organization.org/identifiers",
    "value": "unique-value"
  }
]
```

### 2. Use Conditional Operations to Prevent Duplicates

When creating or updating resources, use conditional operations based on business identifiers:

```
PUT [base]/Patient?identifier=http://your-organization.org/identifiers|unique-value
```

This approach:
- Prevents creation of duplicate resources with the same business identifiers
- Updates existing resources instead of creating new ones
- Properly increments version numbers when resources are updated
- Maintains a single logical resource per real-world entity

### 3. Maintain Reference Integrity

When creating references between resources:
- Use logical IDs for internal references
- Use conditional references for external systems
- Consider versioned references only when specific versions are required

### 4. Handle Version Conflicts

When updating resources, use ETags to prevent concurrent modification issues:

```
PUT [base]/Patient/52
If-Match: W/"2"
```

## Implementation in Our Project

Our implementation follows these principles:

1. **Test Patient Creation**: Includes a proper business identifier with a system URI
2. **Sample Data Loading**: Uses conditional updates based on business identifiers to prevent duplicates
3. **Resource References**: Maintains proper references between resources

For implementation details, see:
- `servers/fhir-server/scripts/setup_postgres_fhir.py`

## References

### Official HAPI FHIR Documentation
- [HAPI FHIR Resource References](https://hapifhir.io/hapi-fhir/docs/model/references.html) - Details on how HAPI FHIR handles resource references
- [HAPI FHIR REST Operations](https://hapifhir.io/hapi-fhir/docs/server_plain/rest_operations.html) - Information on CRUD operations including conditional updates
- [HAPI FHIR REST Operations: Search](https://hapifhir.io/hapi-fhir/docs/server_plain/rest_operations_search.html) - Details on searching resources by identifiers
- [HAPI FHIR JPA Server Configuration](https://hapifhir.io/hapi-fhir/docs/server_jpa/configuration.html) - Server configuration options including identifier handling

### Official FHIR Specification
- [FHIR Resource Identification](https://build.fhir.org/resource.html#identification) - Core concepts of resource identification
- [FHIR RESTful API](https://build.fhir.org/http.html) - RESTful API operations
- [FHIR Conditional Operations](https://build.fhir.org/http.html#cond-update) - Specification for conditional updates
- [FHIR References](https://build.fhir.org/references.html) - Detailed explanation of reference types and usage
