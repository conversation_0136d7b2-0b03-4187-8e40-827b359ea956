# Introduction to OMOP CDM

The Observational Medical Outcomes Partnership (OMOP) Common Data Model (CDM) is a standardized data model designed to facilitate the systematic analysis of disparate observational databases. By standardizing the structure, format, and content of observational data, the OMOP CDM enables efficient and consistent analysis across different data sources.

## Purpose of OMOP CDM in This Project

In this project, the OMOP CDM serves as the target data model for transforming FHIR (Fast Healthcare Interoperability Resources) data. This transformation enables:

1. **Standardized Analytics**: Leveraging the extensive analytics tools developed by the OHDSI (Observational Health Data Sciences and Informatics) community
2. **Consistent Terminology**: Mapping diverse healthcare terminologies to standard concepts
3. **Research Readiness**: Preparing data for observational research and population health studies
4. **Interoperability**: Facilitating data sharing and collaboration across institutions

## OMOP CDM Version

This project implements **OMOP CDM v5.4.2**, which is the current stable version recommended by the OHDSI community. This version includes:

- 37 tables organized in 6 categories
- Support for both clinical data and standardized vocabularies
- Enhanced support for various data domains (conditions, drugs, measurements, etc.)

> **Note**: CDM v6.0 exists but is marked as "Pre-release" and is NOT supported by OHDSI tools and methods. Always use CDM v5.4.2 for production implementations.

## Key Concepts

### Standardized Vocabularies

OMOP CDM uses standardized vocabularies to normalize different coding systems:

- **Standard Concepts**: Preferred concepts for analysis
- **Source Concepts**: Original concepts from source data
- **Concept Relationships**: Mappings between concepts
- **Domains**: Categories for concepts (Condition, Drug, Measurement, etc.)

### Person-Centric Model

The model is centered around the Person table, with all clinical events linked to a person:

- **Person**: Demographic information
- **Clinical Events**: Visits, conditions, drugs, procedures, measurements, etc.
- **Eras**: Derived periods of continuous exposure or condition

### Observation Period

The model tracks when a person is observed in the healthcare system:

- **Observation Period**: Time range when a person is actively observed
- **Visit Occurrence**: Encounters with healthcare providers

## OMOP CDM Structure

The OMOP CDM is organized into the following categories:

1. **Clinical Data Tables**: Store patient-level clinical events
   - Person, Visit_Occurrence, Condition_Occurrence, Drug_Exposure, etc.

2. **Vocabulary Tables**: Define standardized concepts and relationships
   - Concept, Vocabulary, Concept_Relationship, etc.

3. **Metadata Tables**: Provide information about the data itself
   - Metadata, CDM_Source, etc.

4. **Health System Data Tables**: Represent the healthcare delivery structure
   - Care_Site, Provider, etc.

5. **Derived Tables**: Contain derived elements for analysis
   - Drug_Era, Condition_Era, Dose_Era, etc.

6. **Cost Tables**: Capture financial information
   - Cost, Payer_Plan_Period, etc.

## Benefits of OMOP CDM

- **Standardization**: Common structure and semantics across disparate data sources
- **Efficiency**: Pre-defined analytical methods can be applied consistently
- **Transparency**: Clear documentation and open community development
- **Scalability**: Designed to handle large-scale observational data
- **Extensibility**: Can be extended for specific use cases while maintaining core compatibility

## FHIR to OMOP Transformation

Transforming FHIR data to OMOP CDM involves:

1. **Structural Mapping**: Mapping FHIR resources to OMOP tables
2. **Semantic Mapping**: Translating FHIR terminologies to OMOP standard concepts
3. **Data Transformation**: Converting data formats and units
4. **Relationship Preservation**: Maintaining relationships between clinical entities

This project follows the HL7 Vulcan FHIR-to-OMOP Implementation Guide, which provides standardized mappings and best practices for this transformation.

## Next Steps

After understanding the basics of OMOP CDM, you can:

1. Review the [Architecture Overview](architecture.md) to understand how the OMOP module fits into the overall project
2. Explore the [Implementation Roadmap](roadmap.md) for the phased approach to implementing the OMOP module
3. Set up an OMOP database using either [PostgreSQL](database/postgresql_setup.md) or [SQLite](database/sqlite_setup.md)

## References

1. [OHDSI Common Data Model](https://github.com/OHDSI/CommonDataModel)
2. [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
3. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
4. [HL7 Vulcan FHIR-to-OMOP Implementation Guide](https://build.fhir.org/ig/HL7/fhir-omop-ig/)
