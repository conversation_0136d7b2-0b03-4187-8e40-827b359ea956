# PostgreSQL OMOP CDM Database Setup Guide

This step-by-step guide explains how to set up a PostgreSQL database for the OMOP Common Data Model (CDM) v5.4. This approach is recommended for production environments and follows the official OHDSI guidelines.

> **Note for macOS users**: This guide uses the short `psql` command. If you encounter "command not found" errors, refer to the installation section for PATH configuration instructions.

> **Commands**: All `psql` commands use `PGPASSWORD` for secure authentication without interactive prompts.

## Official Resources

- [OHDSI CommonDataModel Repository](https://github.com/OHDSI/CommonDataModel)
- [OMOP CDM Documentation](https://ohdsi.github.io/CommonDataModel/)
- [DDL Files for CDM v5.4](https://github.com/OHDSI/CommonDataModel/tree/v5.4.0/inst/ddl/5.4/postgresql)

## Prerequisites

Before starting, ensure you have:

- PostgreSQL 13.0 or higher installed **with `psql` command available in PATH**
- Admin access to PostgreSQL
- At least 50GB of free disk space (more if loading full vocabularies)
- Command-line access to run scripts
- `wget` or `curl` to download files

## Step 1: Install PostgreSQL (if not already installed)

### On Ubuntu/Debian:
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
```

### On macOS (using Homebrew):
```bash
brew install postgresql
brew services start postgresql
```

### Verify PostgreSQL Installation and PATH

After installation, verify that `psql` is available in your PATH:

```bash
# Check if psql is available
which psql

# Check PostgreSQL version
psql --version
```

**Expected output:**
```
/opt/homebrew/bin/psql
psql (PostgreSQL) 14.x
```

**If `psql` command is not found:**

1. **Check your current shell**:
   ```bash
   echo $SHELL  # Should show /bin/zsh
   ps -p $$ -o comm=  # Should show zsh, not bash
   ```

2. **If using bash instead of zsh**, switch to zsh:
   ```bash
   exec zsh
   ```

3. **Add Homebrew to your PATH** (for Apple Silicon Macs):
   ```bash
   # Add to your .zshrc file
   echo 'export PATH="/opt/homebrew/bin:/opt/homebrew/sbin:$PATH"' >> ~/.zshrc

   # Reload your shell configuration
   source ~/.zshrc
   ```

4. **Verify the fix**:
   ```bash
   which psql  # Should show /opt/homebrew/bin/psql
   which brew  # Should show /opt/homebrew/bin/brew
   ```

> **Important**: VS Code terminals sometimes default to bash. Ensure you're using zsh for proper PATH configuration.

### On Windows:
Download and install from [PostgreSQL official website](https://www.postgresql.org/download/windows/)

## Step 2: Create Database and User

### Connect to PostgreSQL

```bash
# For macOS with Homebrew
psql -U $(whoami) -d postgres

# For Linux systems
sudo -u postgres psql
```

### Create Database and User

Execute the following SQL commands in order:

```sql
-- 1. Create user first (allows setting as owner immediately)
CREATE USER omop WITH PASSWORD 'omop_secure_2024';

-- 2. Create database with omop as owner
CREATE DATABASE omop_cdm OWNER omop;

-- 3. Grant explicit privileges (redundant but good practice)
GRANT ALL PRIVILEGES ON DATABASE omop_cdm TO omop;

-- 4. Verify creation
\l

-- 5. Exit PostgreSQL
\q
```

### Verify Setup

Test connectivity with the new user:

```bash
# Test connection (replace with your PostgreSQL path if needed)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT current_database(), current_user;"
```

Expected output:
```
 current_database | current_user
------------------+--------------
 omop_cdm         | omop
```

### Notes

- **Verify PATH**: Ensure `psql` is available by running `which psql`
- **If PATH missing**: Add `/opt/homebrew/bin` to your PATH (see installation section above)
- **Service management**: Ensure PostgreSQL is running: `brew services start postgresql`
- **Connection testing**: Use `PGPASSWORD` to avoid password prompts in scripts

## Step 3: Download OMOP CDM DDL Scripts

### Script Architecture and Execution Order

The OMOP CDM database creation follows a specific sequence with 4 official scripts:

| Script | Purpose | Content | Execution Order |
|--------|---------|---------|-----------------|
| **ddl.sql** | Create all 37 CDM tables | Table definitions, data types, required fields | **1st** |
| **primary_keys.sql** | Define primary keys | Uniqueness constraints for identifiers | **2nd** |
| **indices.sql** | Optimize performance | Indexes on frequently queried fields | **3rd** |
| **constraints.sql** | Define relationships | Foreign keys and referential integrity | **4th** |

**Why this order matters:**
- Tables must exist before adding keys
- Primary keys must exist before foreign keys
- Indexes improve performance after basic structure
- Constraints validate integrity as the final step

### Download Scripts

```bash
# Create directory for DDL scripts
mkdir -p scripts/ddl/postgresql

# Download all 4 scripts from official OHDSI repository
curl -L https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_ddl.sql -o scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_ddl.sql
curl -L https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_primary_keys.sql -o scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_primary_keys.sql
curl -L https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_indices.sql -o scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_indices.sql
curl -L https://github.com/OHDSI/CommonDataModel/raw/v5.4.0/inst/ddl/5.4/postgresql/OMOPCDM_postgresql_5.4_constraints.sql -o scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_constraints.sql
```

## Step 4: Modify the Scripts to Replace Schema Placeholder

The DDL scripts contain a placeholder `@cdmDatabaseSchema` that needs to be replaced with your actual schema name (typically 'public' for PostgreSQL):

```bash
# Replace schema placeholder in all scripts
# For public schema:
sed -i 's/@cdmDatabaseSchema/public/g' scripts/ddl/postgresql/*.sql

# For custom schema (if you created one):
# sed -i 's/@cdmDatabaseSchema/omop/g' scripts/ddl/postgresql/*.sql
```

> **Note for macOS users**: The `sed` command is slightly different. Use:
> ```bash
> sed -i '' 's/@cdmDatabaseSchema/public/g' scripts/ddl/postgresql/*.sql
> ```

## Step 5: Execute the Scripts in the Correct Order

Execute the scripts in the following order using the password `omop_secure_2024`:

```bash
# 1. Create tables (39 tables should be created)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_ddl.sql

# 2. Add primary keys (28 ALTER TABLE commands)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_primary_keys.sql

# 3. Create indices (multiple CREATE INDEX and CLUSTER commands)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_indices.sql

# 4. Add constraints (foreign keys - may show one error, this is normal)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -f scripts/ddl/postgresql/OMOPCDM_postgresql_5.4_constraints.sql
```

### Expected Results

- **Script 1**: Should show 39 "CREATE TABLE" messages
- **Script 2**: Should show 28 "ALTER TABLE" messages
- **Script 3**: Should show multiple "CREATE INDEX" and "CLUSTER" messages
- **Script 4**: May show one error about "cohort" table - this is a known issue and not critical

### Known Issues

**Error in constraints script:**
```
ERROR: there is no unique constraint matching given keys for referenced table "cohort"
```
This error is expected and does not affect the core OMOP CDM functionality.

## Step 6: Verify the Installation

### Basic Verification

Check that all tables were created:

```bash
# Count total tables (should be 39)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT COUNT(*) as total_tables FROM information_schema.tables WHERE table_schema = 'public';"

# List all tables
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "\dt"
```

### Complete Verification

Test key tables to ensure they're accessible:

```bash
# Test key OMOP tables (should all return 0 rows - empty tables)
PGPASSWORD='omop_secure_2024' psql -U omop -d omop_cdm -c "SELECT 'PERSON' as table_name, COUNT(*) as row_count FROM person UNION ALL SELECT 'CONCEPT', COUNT(*) FROM concept UNION ALL SELECT 'VOCABULARY', COUNT(*) FROM vocabulary;"
```

Expected output:
```
 table_name | row_count
------------+-----------
 PERSON     |         0
 CONCEPT    |         0
 VOCABULARY |         0
```

### Success Criteria

✅ **39 tables created**
✅ **All key tables accessible**
✅ **Tables are empty (0 records)**
✅ **No connection errors**

## Step 7: Configure Environment Variables

Update your `.env` file to use the new OMOP database configuration:

```bash
# OMOP Database Configuration
# Production OMOP CDM v5.4 database with dedicated user
OMOP_DB_HOST=localhost
OMOP_DB_PORT=5432
OMOP_DB_NAME=omop_cdm
OMOP_DB_SCHEMA=public
OMOP_DB_USERNAME=omop
OMOP_DB_PASSWORD=omop_secure_2024
```

### Test Configuration

Verify that applications can connect using the environment variables:

```python
# Create test file: test_connection.py
import os
from dotenv import load_dotenv
import psycopg2

load_dotenv()
conn = psycopg2.connect(
    host=os.getenv('OMOP_DB_HOST'),
    port=os.getenv('OMOP_DB_PORT'),
    database=os.getenv('OMOP_DB_NAME'),
    user=os.getenv('OMOP_DB_USERNAME'),
    password=os.getenv('OMOP_DB_PASSWORD')
)
print("✅ Connection successful!")
conn.close()
```

### Troubleshooting Environment Variables

**If connection fails with old credentials:**

1. Check for conflicting system environment variables:
   ```bash
   env | grep OMOP
   ```

2. Clear conflicting variables:
   ```bash
   unset OMOP_DB_USERNAME OMOP_DB_PASSWORD
   ```

3. Restart your terminal or IDE to reload `.env` file

### Database Schema Visualization

Your OMOP CDM database now contains 37 tables organized according to the official OHDSI specification. To understand the relationships between these tables, refer to the **official Entity Relationship Diagram (ERD)**:

**📊 [View OMOP CDM v5.4 Entity Relationship Diagram](https://ohdsi.github.io/CommonDataModel/cdm54erd.html)**

The diagram shows:
- **Clinical Data Tables** (blue): Person, Visit, Condition, Drug, etc.
- **Vocabulary Tables** (green): Concept, Vocabulary, Domain, etc.  
- **Metadata Tables** (gray): CDM Source, Metadata, etc.
- **Derived Tables** (yellow): Cohort, Note NLP, etc.

> **Tip**: Keep this diagram handy when designing your FHIR-to-OMOP mappings and ETL processes.

## Step 8: Download and Prepare OMOP Vocabularies

The OMOP CDM requires standardized vocabularies to function properly. Your database structure is now complete, but without vocabularies, it cannot map healthcare concepts.

> **📖 Complete Vocabulary Guide**: For detailed vocabulary setup, see [OMOP Vocabulary Documentation](../vocabulary/README.md)

### Prerequisites for Vocabulary Setup

Before downloading vocabularies, you need:

1. **UMLS Account**: Register at [UMLS Terminology Services](https://uts.nlm.nih.gov/uts/)
2. **UMLS API Key**: Obtain from your UMLS profile for CPT4 reconstitution
3. **Java Runtime**: Required for CPT4 processing (Java 8 or higher)

### Step 8.1: Configure UMLS API Key

Add your UMLS API key to the `.env` file for secure storage:

```bash
# UMLS API Configuration for CPT4 reconstitution
UMLS_API_KEY=your-umls-api-key-here

# Vocabulary path (updated with correct date)
VOCABULARY_PATH=./data/vocabulary/omop_v5_20250630
```

> **Security Note**: The `.env` file is protected by `.gitignore` and will not be committed to version control.

### Step 8.2: Download Vocabularies from Athena

1. **Visit [OHDSI Athena](https://athena.ohdsi.org/)** and log in
2. **Select vocabularies** for your use case (recommended minimum):
   - SNOMED CT (clinical conditions and procedures)
   - LOINC (laboratory tests and measurements)
   - RxNorm (medications)
   - CPT4 (procedures - requires UMLS license)
   - ICD10CM (diagnoses)
   - NDC (drug codes)
   - Gender, Race, Ethnicity (demographic concepts)

3. **Download the vocabulary bundle** (typically 500MB-1.5GB)
4. **Save the ZIP file** to `data/vocabulary/athena_download/`

### Step 8.3: Extract and Prepare Vocabularies

```bash
# Navigate to vocabulary directory
cd data/vocabulary

# Create directory with current date (format: omop_v5_YYYYMMDD)
mkdir omop_v5_$(date +%Y%m%d)

# Extract vocabularies from downloaded ZIP
unzip -q athena_download/vocabulary_download_v5_*.zip -d omop_v5_$(date +%Y%m%d)/

# Navigate to extracted directory
cd omop_v5_$(date +%Y%m%d)
```

### Step 8.4: Reconstitute CPT4 (Critical Step)

CPT4 requires special processing due to licensing restrictions:

```bash
# Verify Java is available
java -version

# Make CPT4 script executable
chmod +x cpt.sh

# Run CPT4 reconstitution with your UMLS API key
./cpt.sh YOUR_UMLS_API_KEY

# Expected output: "All cpt4 concepts are processed."
```

> **Important**: This step downloads CPT4 descriptions from UMLS and merges them with the vocabulary. Internet connection is required.

### Step 8.5: Verify Vocabulary Integrity

Check that all vocabulary files are present and complete:

```bash
# List all CSV files
ls -1 *.csv

# Check file sizes (should be substantial)
ls -lh *.csv

# Verify CPT4 concepts were added
grep -c "CPT4" CONCEPT.csv
# Expected: ~17,000+ CPT4 concepts

# Check vocabulary distribution
tail -n +2 CONCEPT.csv | cut -f4 | sort | uniq -c | sort -nr | head -10
```

Expected major vocabularies and approximate concept counts:
- **NDC**: ~1.25M (drug codes)
- **SNOMED**: ~1.09M (clinical concepts)
- **RxNorm**: ~311K (medications)
- **LOINC**: ~275K (lab tests)
- **ICD10CM**: ~99K (diagnoses)
- **CPT4**: ~17K (procedures)

## Step 9: Load Vocabularies into PostgreSQL

With vocabularies prepared and CPT4 reconstituted, the final step is loading them into your OMOP database.

> **📖 Complete Loading Guide**: For detailed vocabulary loading instructions, see [OMOP Vocabulary Loading Guide](../vocabulary/loading.md)

### Quick Summary

The vocabulary loading process involves:

1. **Loading vocabulary files** in dependency order (reference tables first)
2. **Verifying successful loading** with concept counts and distribution checks
3. **Performance optimization** for large vocabulary tables

### Expected Results After Loading

- **Total concepts**: ~3,280,000
- **Total relationships**: ~15,800,000
- **Major vocabularies**: SNOMED (~1.09M), NDC (~1.25M), RxNorm (~311K), LOINC (~275K)
- **CPT4 concepts**: ~17,750 (confirms successful reconstitution)
- **Loading time**: 30-60 minutes depending on hardware

> ## **Next Step**: Follow the [Vocabulary Loading Guide](../vocabulary/loading.md) to complete the vocabulary loading process.

## Troubleshooting

### PATH and Command Issues

**Problem**: `psql: command not found`
```bash
# Solution 1: Add to PATH permanently
echo 'export PATH="/opt/homebrew/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc

# Solution 2: Verify Homebrew installation
brew --prefix postgresql

# Solution 3: Use full path temporarily
/opt/homebrew/bin/psql --version
```

**Problem**: `psql` available but connection fails
```bash
# Check if PostgreSQL service is running
brew services list | grep postgresql

# Start PostgreSQL if not running
brew services start postgresql
```

### Connection Issues
- Verify that PostgreSQL is running: `sudo systemctl status postgresql`
- Check that you can connect with the omop user: `psql -U omop -d omop_cdm`
- Ensure your firewall allows connections to port 5432

### Permission Errors
- Verify that the omop user has the necessary permissions: 
  ```sql
  \c omop_cdm
  \dn+
  ```

### Schema Issues
- If you're using a custom schema, make sure it exists and the user has permissions:
  ```sql
  \dn
  ```

### Out of Disk Space
- Check available disk space: `df -h`
- Vocabulary files can be several GB in size

### SQL Errors
- Check the PostgreSQL logs: `sudo tail -f /var/log/postgresql/postgresql-13-main.log`

## Performance Optimization (Optional)

For better performance, especially with large datasets, consider these PostgreSQL optimizations:

```sql
-- Adjust memory parameters
ALTER SYSTEM SET shared_buffers = '1GB';  -- 25% of RAM for dedicated server
ALTER SYSTEM SET work_mem = '64MB';       -- Increase for complex queries
ALTER SYSTEM SET maintenance_work_mem = '256MB';  -- For maintenance operations

-- Write-Ahead Log settings
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;

-- Autovacuum settings
ALTER SYSTEM SET autovacuum_vacuum_scale_factor = 0.05;
ALTER SYSTEM SET autovacuum_analyze_scale_factor = 0.025;

-- Apply changes
SELECT pg_reload_conf();
```

## Next Steps

After setting up the database:

1. Configure your ETL process to transform FHIR data to OMOP
2. Consider running data quality checks using the [OHDSI Data Quality Dashboard](https://github.com/OHDSI/DataQualityDashboard)
3. Explore your data with [ATLAS](https://github.com/OHDSI/Atlas) or [ACHILLES](https://github.com/OHDSI/Achilles)

## References

1. [OHDSI Common Data Model](https://ohdsi.github.io/CommonDataModel/)
2. [The Book of OHDSI](https://ohdsi.github.io/TheBookOfOhdsi/)
3. [OHDSI GitHub Repository](https://github.com/OHDSI/CommonDataModel)
4. [PostgreSQL Documentation](https://www.postgresql.org/docs/)
