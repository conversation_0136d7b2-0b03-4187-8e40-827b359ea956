# R-based Official OMOP CDM Setup

This guide covers setting up OMOP CDM databases using the **official OHDSI R-based method**. This approach uses the canonical implementation from the [OHDSI CommonDataModel repository](https://github.com/OHDSI/CommonDataModel) for maximum compatibility and standard compliance.

## Overview

The official OHDSI method provides:
- ✅ **Canonical implementation** - Uses the exact same code as OHDSI
- ✅ **Maximum compatibility** - Ensures standards compliance
- ✅ **Regular updates** - Manual control over when to adopt new versions
- ✅ **Multi-database support** - PostgreSQL, SQL Server, SQLite, etc.

## Prerequisites

### 1. Environment Setup

Ensure your Conda environment includes R dependencies:

```bash
# Install/update environment with R support
conda env update -f environment.yml
conda activate fhir-omop
```

### 2. Download Official Code

Add the CommonDataModel repository as a git submodule:

```bash
# From project root
git submodule add https://github.com/OHDSI/CommonDataModel.git external/CommonDataModel
git submodule update --init
```

### 3. Install R Dependencies

```bash
# Install required R packages
Rscript scripts/r_official/install_r_deps.R
```

## Quick Start

### For PostgreSQL

```bash
Rscript scripts/r_official/setup_omop_r.R \
  --dbms postgresql \
  --user omop_user \
  --password your_password \
  --server localhost \
  --port 5432 \
  --database omop_cdm \
  --schema omop_cdm
```

### For SQLite

```bash
Rscript scripts/r_official/setup_omop_r.R \
  --dbms sqlite \
  --database_file data/omop_cdm.db
```

## Manual Updates

### Updating the Official Code

To update to a newer version of the CommonDataModel:

```bash
# Navigate to submodule
cd external/CommonDataModel

# Check current version
git describe --tags

# List available versions
git tag --sort=-version:refname | head -10

# Update to specific version (e.g., v5.4.2)
git checkout v5.4.2

# Or update to latest
git checkout main
git pull origin main

# Return to project root and commit the update
cd ../..
git add external/CommonDataModel
git commit -m "Update CommonDataModel to v5.4.2"
```

⚠️ **Important**: Always test database creation after updating the submodule.

## Advanced Configuration

### Database-Specific Settings

The official method supports various database management systems:

| DBMS | Example Connection |
|------|-------------------|
| PostgreSQL | `--dbms postgresql --server localhost --port 5432` |
| SQL Server | `--dbms sql server --server localhost --port 1433` |
| SQLite | `--dbms sqlite --database_file /path/to/db.sqlite` |
| Oracle | `--dbms oracle --server localhost --port 1521` |

### Custom Schema Names

```bash
# Use custom schema name
Rscript scripts/r_official/setup_omop_r.R \
  --schema my_custom_omop \
  --database my_database
```

### Different CDM Versions

```bash
# Specify CDM version (if supported by the submodule version)
Rscript scripts/r_official/setup_omop_r.R \
  --cdm_version 5.3 \
  --database omop_v53
```

## Validation

After setup, validate the database structure:

```sql
-- Check tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'omop_cdm'
ORDER BY table_name;

-- Verify key tables exist
SELECT EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_schema = 'omop_cdm' 
  AND table_name = 'person'
) as person_table_exists;
```

## Troubleshooting

### Common Issues

1. **Submodule not found**
   ```bash
   git submodule update --init external/CommonDataModel
   ```

2. **R package installation fails**
   ```bash
   # Install system dependencies (macOS)
   brew install java
   
   # Install system dependencies (Ubuntu)
   sudo apt-get install default-jdk r-base-dev
   ```

3. **Database connection issues**
   - Verify database server is running
   - Check credentials and permissions
   - Ensure network connectivity

### Getting Help

- Check the [OHDSI CommonDataModel documentation](https://github.com/OHDSI/CommonDataModel)
- Review database-specific setup guides in `docs/guides/omop/database/`
- Compare with the Python implementation: `docs/guides/omop/database/postgresql_setup.md`

## Integration with Python Workflow

This R-based setup creates the same database structure as our Python implementation. After setup, you can:

1. **Load vocabularies** using existing Python scripts: `scripts/load_vocabularies.py`
2. **Run ETL processes** using the main Python pipeline: `src/fhir_omop/main.py`
3. **Validate data** using Python analysis tools

The official R method and Python ETL complement each other perfectly.
