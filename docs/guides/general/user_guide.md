# FHIR to OMOP CDM Transformation Pipeline - User Guide

This guide provides detailed instructions on how to use the FHIR to OMOP CDM Transformation Pipeline developed by AIO (Artificial Intelligence Orchestrator).

## Table of Contents

1. [Introduction](#introduction)
2. [Configuration](#configuration)
3. [Running the Pipeline](#running-the-pipeline)
4. [Customizing Transformations](#customizing-transformations)
5. [Troubleshooting](#troubleshooting)

## Introduction

The FHIR to OMOP CDM Transformation Pipeline is an internal tool developed by AIO (Artificial Intelligence Orchestrator) for transforming healthcare data from the HL7 FHIR format to the OMOP Common Data Model (CDM). This guide will help you get started with using the pipeline.

## Configuration

### Environment Setup

1. Create a `.env` file based on the provided `.env.example`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file to configure your FHIR server and OMOP database connections:
   ```
   # FHIR Server Configuration
   FHIR_SERVER_BASE_URL=http://your-fhir-server.com/fhir
   FHIR_SERVER_USERNAME=your_username
   FHIR_SERVER_PASSWORD=your_password

   # OMOP Database Configuration
   OMOP_DB_HOST=localhost
   OMOP_DB_PORT=5432
   OMOP_DB_NAME=omop_cdm
   OMOP_DB_SCHEMA=public
   OMOP_DB_USERNAME=postgres
   OMOP_DB_PASSWORD=your_password
   ```

### Vocabulary Setup

The pipeline requires OMOP vocabulary files to perform mappings. You can download these from the OHDSI Athena service:

1. Download vocabulary files from [OHDSI Athena](https://athena.ohdsi.org/)
2. Extract the files to the `data/vocabulary` directory
3. Update the `VOCABULARY_PATH` in your `.env` file

## Running the Pipeline

### Basic Usage

To run the pipeline with default settings:

```bash
python scripts/run_pipeline.py
```

### Advanced Usage

You can customize the pipeline execution with command-line arguments:

```bash
python scripts/run_pipeline.py --batch-size 200 --log-level DEBUG
```

Available options:
- `--batch-size`: Number of resources to process per batch (default: 100)
- `--config`: Path to configuration file (default: .env)
- `--log-level`: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)

## Customizing Transformations

### Adding New Resource Mappers

To add support for a new FHIR resource type:

1. Create a new mapper file in `src/fhir_omop/mappers/`
2. Implement the mapper class extending `BaseMapper`
3. Register the mapper in the ETL pipeline

Example of a custom mapper:

```python
from fhir_omop.mappers.base_mapper import BaseMapper

class CustomResourceMapper(BaseMapper):
    def map(self, fhir_resource):
        # Implement mapping logic
        pass
```

### Customizing Existing Mappers

You can customize existing mappers by extending them and overriding specific methods:

```python
from fhir_omop.mappers.patient_mapper import PatientMapper

class CustomPatientMapper(PatientMapper):
    def map_demographics(self, fhir_patient):
        # Custom demographics mapping
        pass
```

## Troubleshooting

### Common Issues

1. **Connection Errors**:
   - Verify FHIR server and database connection settings in `.env`
   - Check network connectivity and firewall settings

2. **Mapping Errors**:
   - Ensure vocabulary files are correctly installed
   - Check for missing concept mappings in the logs

3. **Performance Issues**:
   - Adjust batch size for optimal performance
   - Consider database indexing strategies

### Logging

The pipeline generates detailed logs that can help diagnose issues:

- Console output shows high-level progress
- Detailed logs are written to `etl.log`
- Set `LOG_LEVEL=DEBUG` in `.env` for verbose logging

### Getting Help

If you encounter issues not covered in this guide:

1. Contact the AIO development team via the internal ticketing system
2. Review the [Implementation Case Studies](../research/implementation_case_studies.md) for insights
3. Consult the [OHDSI Forums](https://forums.ohdsi.org/) for OMOP-specific questions (without sharing proprietary information)
