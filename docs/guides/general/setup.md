# FHIR to OMOP Setup Guide

This guide documents the complete setup process for the FHIR to OMOP transformation project, including external platform registrations, downloads, and configurations.

## Setup Process Overview

```mermaid
graph TD
    A[Start] --> B[Register on Athena]
    B --> C[Download OMOP Vocabularies]
    C --> D[Register on UMLS]
    D --> E[Reconstitute CPT-4]
    E --> F[Configure Vocabularies]
    F --> G[Setup Database]
    G --> H[Configure Environment]
    H --> I[End]
```

## Prerequisites

Before starting, you'll need:

- [OHDSI Athena](https://athena.ohdsi.org/) developer account
- [UMLS Terminology Services](https://uts.nlm.nih.gov/uts/) account
- [Miniconda](https://docs.conda.io/en/latest/miniconda.html) or [Anaconda](https://www.anaconda.com/products/distribution)
- PostgreSQL (optional, SQLite can also be used)

## Setup Steps

### 1. Register on Athena for OMOP Vocabularies

Athena is the OHDSI vocabulary repository. Registration is required to download vocabularies.

[Detailed tutorial: Athena Registration](../tutorials/athena_registration.md)

### 2. Download OMOP Vocabularies

After registering on Athena, download the essential vocabularies:

- SNOMED CT (for conditions and clinical concepts)
- LOINC (for observations and lab measurements)
- RxNorm (for medications)
- CPT-4 (for procedures)
- ICD10CM (for diagnoses)
- ATC (for medication classification)

[Detailed tutorial: Vocabulary Download](../tutorials/vocabulary_download.md)

### 3. Register on UMLS for CPT-4

To reconstitute the CPT-4 vocabulary, you need a UMLS API key:

[Detailed tutorial: UMLS Registration](../tutorials/umls_registration.md)

### 4. Reconstitute CPT-4

With your UMLS API key, reconstitute the CPT-4 vocabulary:

```bash
cd data/vocabulary/downloads/athena_basic_20250227/
java -Dumls-apikey=YOUR_UMLS_API_KEY -jar cpt4.jar 5
```

### 5. Configure Vocabularies

Follow the instructions to combine and configure vocabularies:

[Detailed guide: Vocabulary Configuration](../../data/vocabulary/README.md)

### 6. Setup Database

Configure the OMOP CDM database:

- [Database Setup Overview](database_setup.md)
- [PostgreSQL Setup Guide](database_postgresql_setup.md) (recommended for production)
- [SQLite Setup Guide](database_sqlite_setup.md) (recommended for development)

### 7. Configure Environment

Set up the development environment:

```bash
# Clone repository
git clone https://github.com/your-username/fhir-omop.git
cd fhir-omop

# Create and activate Conda environment
conda env create -f environment.yml
conda activate fhir-omop_env

# Set up Jupyter kernel for this project
python -m ipykernel install --user --name fhir-omop_env --display-name "Python (FHIR-OMOP)"

# Configure .env file
cp .env.example .env
# Edit .env with your configurations
```

## Verify Setup

To verify that everything is correctly configured:

```bash
python scripts/check_setup.py
```

## Troubleshooting

If you encounter issues during setup, consult:

[Troubleshooting Guide](troubleshooting.md)

## References

- [OHDSI Documentation](https://ohdsi.github.io/TheBookOfOhdsi/)
- [FHIR Specification](https://www.hl7.org/fhir/)
- [OMOP CDM](https://ohdsi.github.io/CommonDataModel/)
