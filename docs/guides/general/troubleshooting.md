# Troubleshooting Guide

This guide addresses common issues encountered during the setup and operation of the FHIR to OMOP transformation project.

## Vocabulary Setup Issues

### CPT-4 Reconstitution Fails

**Symptoms:**
- Error when running `java -Dumls-apikey=YOUR_API_KEY -jar cpt4.jar 5`
- Missing CPT-4 descriptions in CONCEPT.csv

**Solutions:**
1. Verify your UMLS API key is valid and active
2. Ensure Java is installed and in your PATH
3. Run the command from the directory containing the cpt4.jar file
4. Check internet connectivity (the tool needs to download data)

### Missing Vocabulary Files

**Symptoms:**
- Missing .csv files in vocabulary directory
- Errors about missing vocabulary tables

**Solutions:**
1. Verify you've downloaded all required vocabularies from Athena
2. Check that files were extracted to the correct location
3. Ensure file permissions allow reading the files

### Vocabulary Path Configuration

**Symptoms:**
- "Vocabulary not found" errors
- Application can't locate vocabulary files

**Solutions:**
1. Check the `VOCABULARY_PATH` in your `.env` file
2. Ensure the path is correct and accessible
3. Use absolute paths if relative paths aren't working

## Database Issues

### Connection Errors

**Symptoms:**
- "Could not connect to database" errors
- Connection timeout or refused

**Solutions:**
1. Verify database credentials in `.env`
2. Ensure database server is running
3. Check network connectivity to database server
4. Verify firewall settings allow connections

### Missing Tables

**Symptoms:**
- "Table does not exist" errors
- Missing OMOP CDM tables

**Solutions:**
1. Run the DDL scripts to create tables
2. Verify the correct schema is being used
3. Check database user permissions

### Out of Memory/Disk Space

**Symptoms:**
- Process crashes during vocabulary loading
- "Disk full" errors

**Solutions:**
1. Ensure sufficient disk space (vocabularies require several GB)
2. Increase database server memory allocation
3. For SQLite: Use a filesystem with sufficient space

## FHIR to OMOP Transformation Issues

### Import Errors

**Symptoms:**
- ModuleNotFoundError or ImportError
- "Cannot import name" errors

**Solutions:**
1. Verify the project structure is correct
2. Check that all dependencies are installed
3. Ensure Python path includes the project root

### Mapping Errors

**Symptoms:**
- "No mapping found" errors
- Missing concept IDs in transformed data

**Solutions:**
1. Verify vocabularies are correctly loaded
2. Check that source codes exist in the vocabulary
3. Review mapping logic in the corresponding mapper

### Performance Issues

**Symptoms:**
- Transformation process is very slow
- High memory usage

**Solutions:**
1. Adjust batch size in `.env` (try smaller batches)
2. Reduce number of parallel workers
3. Optimize database queries
4. Consider using a more powerful machine

## Environment Setup Issues

### Python Environment

**Symptoms:**
- ModuleNotFoundError
- Version compatibility issues

**Solutions:**
1. Verify virtual environment is activated
2. Install dependencies: `pip install -r requirements.txt`
3. Check Python version (3.8+ required)

### Configuration Issues

**Symptoms:**
- "Configuration not found" errors
- Missing environment variables

**Solutions:**
1. Ensure `.env` file exists and is properly formatted
2. Check that all required variables are defined
3. Verify file paths are correct for your system

## Getting Help

If you encounter issues not covered in this guide:

1. Check the project documentation
2. Search for similar issues in the project repository
3. Consult the OHDSI community forums
4. Create a detailed issue report with:
   - Error messages
   - Steps to reproduce
   - Environment details
