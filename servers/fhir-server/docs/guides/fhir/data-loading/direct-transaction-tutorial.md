# Direct Transaction Tutorial

This tutorial explains how to load FHIR data using transaction bundles for maximum performance.

## Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Step 1: Clean the Server](#step-1-clean-the-server)
- [Step 2: Configure the Server (Optional)](#step-2-configure-the-server-optional)
- [Step 3: Convert NDJSON to Transaction Bundles](#step-3-convert-ndjson-to-transaction-bundles)
- [Step 4: Load Transaction Bundles](#step-4-load-transaction-bundles)
- [Step 5: Verify Loaded Resources](#step-5-verify-loaded-resources)
- [Step 6: Restore Default Configuration](#step-6-restore-default-configuration)
- [Performance Considerations](#performance-considerations)
- [Troubleshooting](#troubleshooting)

## Overview

The direct transaction approach converts NDJSON files to transaction bundles and loads them directly into the FHIR server. This method is optimized for performance and is ideal for loading large datasets.

## Prerequisites

- FHIR server running
- NDJSON files containing FHIR resources
- Python environment with required dependencies

## Step 1: Clean the Server

For best results, start with a clean server:

```bash
# Navigate to the FHIR server directory
cd servers/fhir-server

# Stop the server
./manage-fhir-server.sh stop postgres

# Remove the PostgreSQL volume
docker volume rm fhir-server_postgres-data

# Start the server with a clean database
./manage-fhir-server.sh start postgres

# Wait for the server to start
sleep 30
./manage-fhir-server.sh status
```

## Step 2: Configure the Server (Optional)

For large datasets or when referential integrity is not critical, configure the server for import mode:

1. Edit `docker-compose-postgres.yml`:

```yaml
environment:
  # Add these lines
  - hapi.fhir.validation.enabled=false
  - hapi.fhir.validation.request_validator.mode=NONE
  - hapi.fhir.auto_create_placeholder_reference_targets=true
  - hapi.fhir.enforce_referential_integrity_on_write=false
  - hapi.fhir.enforce_referential_integrity_on_delete=false
```

2. Restart the server:

```bash
./manage-fhir-server.sh restart postgres
```

## Step 3: Convert NDJSON to Transaction Bundles

Navigate to the scripts directory:

```bash
cd scripts
```

Convert NDJSON files to transaction bundles:

```bash
python data_loading/ndjson_to_bundle.py --input-dir ../../data/sample_fhir/bulk-export --output-dir ../../data/generated_bundles
```

This will:
1. Read all NDJSON files in the input directory
2. Convert resources to transaction bundles
3. Save the bundles to the output directory, organized by resource type

### Command-Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--input-dir` | Directory containing NDJSON files | Required |
| `--input-file` | Single NDJSON file to process | Alternative to `--input-dir` |
| `--output-dir` | Directory to save generated bundles | `data/generated_bundles` |
| `--batch-size` | Maximum number of resources per bundle | `500` |

## Step 4: Load Transaction Bundles

Load the transaction bundles:

```bash
python data_loading/load_all_bundles.py --bundle-dir ../../data/generated_bundles --server-url http://localhost:8080/fhir --export-performance
```

This will:
1. Find all bundle files in the specified directory (including subdirectories)
2. Load each bundle to the FHIR server
3. Track performance metrics
4. Generate a summary report

### Command-Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--bundle-dir` | Directory containing transaction bundles | Required |
| `--server-url` | URL of the FHIR server | `http://localhost:8080/fhir` |
| `--pattern` | File pattern to match | `*.json` |
| `--export-performance` | Export performance data to JSON | `False` |

## Step 5: Verify Loaded Resources

Verify that resources were loaded correctly:

```bash
# Check the number of Patient resources
curl -s "http://localhost:8080/fhir/Patient?_summary=count" | python -m json.tool

# Check the number of AllergyIntolerance resources
curl -s "http://localhost:8080/fhir/AllergyIntolerance?_summary=count" | python -m json.tool

# Check the number of Device resources
curl -s "http://localhost:8080/fhir/Device?_summary=count" | python -m json.tool
```

## Step 6: Restore Default Configuration

If you configured the server for import mode, restore the default configuration:

1. Edit `docker-compose-postgres.yml` to remove or comment out the import mode settings
2. Restart the server:

```bash
./manage-fhir-server.sh restart postgres
```

## Performance Considerations

### Batch Size

The batch size affects performance:
- Larger batches (500-1000 resources) for faster loading
- Smaller batches (50-100 resources) for better error handling

### Server Configuration

Server configuration significantly impacts performance:
- Disabling validation improves performance by 2-3x
- Disabling referential integrity checks improves performance by 1.5-2x

### Hardware Resources

Allocate sufficient resources to the FHIR server:
- Memory: At least 4GB, 8GB+ recommended for large datasets
- CPU: At least 2 cores, 4+ recommended for large datasets
- Disk: SSD storage for the database

## Troubleshooting

### Common Issues

1. **409 Conflict Errors**:
   - Cause: Resources with the same ID already exist
   - Solution: Clean the server before loading

2. **Reference Integrity Errors**:
   - Cause: Referenced resources don't exist
   - Solution: Configure server for import mode or use selective loading

3. **Out of Memory Errors**:
   - Cause: Insufficient memory allocated to the FHIR server
   - Solution: Increase memory allocation or reduce batch size

For more information, see the [Quick Reference](quick-reference.md) guide.
