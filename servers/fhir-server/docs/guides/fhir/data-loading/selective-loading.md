# Selective Loading Guide

This guide explains how to use the selective loading approach to load FHIR resources while preserving references between them.

## Table of Contents

- [Overview](#overview)
- [How It Works](#how-it-works)
- [Prerequisites](#prerequisites)
- [Step-by-Step Guide](#step-by-step-guide)
- [Command-Line Options](#command-line-options)
- [Example Output](#example-output)
- [Advanced Usage](#advanced-usage)
- [Troubleshooting](#troubleshooting)

## Overview

The selective loading approach analyzes references between FHIR resources and loads them in the correct order to maintain referential integrity. This method works with the default server configuration and doesn't require disabling validation or referential integrity checks.

## How It Works

The selective loading process follows these steps:

1. **Scan Resources**: Read all NDJSON files and identify available resources
2. **Extract References**: Analyze references between resources
3. **Determine Loading Order**: Create an optimal loading order based on dependencies
4. **Load Resources Iteratively**:
   - Identify resources with all references satisfied
   - Convert them to transaction bundles
   - Load the bundles
   - Track loaded resources
   - Repeat until all resources are loaded or no more can be loaded
5. **Verify Resources**: Optionally verify that resources were loaded correctly

## Prerequisites

- FHIR server running with default configuration
- NDJSON files containing FHIR resources
- Python environment with required dependencies

## Step-by-Step Guide

### 1. Prepare Your Environment

Ensure your FHIR server is running:

```bash
cd servers/fhir-server
./manage-fhir-server.sh status
```

If needed, clean the server:

```bash
./manage-fhir-server.sh stop postgres
docker volume rm fhir-server_postgres-data
./manage-fhir-server.sh start postgres
```

### 2. Run the Selective Loader

Navigate to the scripts directory:

```bash
cd scripts
```

Run the selective loader:

```bash
python data_loading/selective_loader.py --data-dir ../../data/sample_fhir/bulk-export --server-url http://localhost:8080/fhir --verify
```

### 3. Monitor the Loading Process

The script will output detailed information about:
- Resources being scanned
- References being extracted
- Loading order determination
- Resources being loaded
- Verification results

### 4. Verify the Results

Check that resources were loaded correctly:

```bash
curl -s "http://localhost:8080/fhir/Patient?_summary=count" | python -m json.tool
curl -s "http://localhost:8080/fhir/AllergyIntolerance?_summary=count" | python -m json.tool
```

## Command-Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--data-dir` | Directory containing NDJSON files | `data/sample_fhir/bulk-export` |
| `--output-dir` | Directory to save generated bundles | `data/generated_bundles` |
| `--server-url` | URL of the FHIR server | `http://localhost:8080/fhir` |
| `--batch-size` | Maximum number of resources per bundle | `500` |
| `--verify` | Verify that resources were loaded correctly | `False` |

## Example Output

```
2025-05-14 13:44:23 - INFO - Found 3 NDJSON files in ../../data/sample_fhir/bulk-export
2025-05-14 13:44:23 - INFO - Scanning resources in ../../data/sample_fhir/bulk-export
2025-05-14 13:44:23 - INFO - Scanned 3 resource types
2025-05-14 13:44:23 - INFO -   - Patient: 13 resources
2025-05-14 13:44:23 - INFO -   - AllergyIntolerance: 11 resources
2025-05-14 13:44:23 - INFO -   - Device: 16 resources
2025-05-14 13:44:23 - INFO - Extracting references
2025-05-14 13:44:23 - INFO - Extracted references from 40 resources
2025-05-14 13:44:23 - INFO - Determined loading order: AllergyIntolerance, Device, Patient
...
2025-05-14 13:44:24 - INFO - Loading summary:
2025-05-14 13:44:24 - INFO -   - Resource types loaded: 3
2025-05-14 13:44:24 - INFO -   - Total resources loaded: 40 out of 40 (100.0%)
```

## Advanced Usage

### Handling Circular References

The selective loader can detect and handle circular references between resource types. When circular references are detected, the loader will:

1. Identify resource types involved in circular dependencies
2. Determine a loading order that minimizes broken references
3. Load resources in multiple passes until all resources are loaded

### Custom Loading Order

If you need to customize the loading order, you can modify the `determine_loading_order` method in the `ReferenceAnalyzer` class.

## Troubleshooting

### Common Issues

1. **No loadable resources found**:
   - Cause: All resources have unresolved references
   - Solution: Check for missing resource types or use transaction bundles with import mode

2. **Circular dependency detected**:
   - Cause: Resources reference each other in a circular pattern
   - Solution: This is normal and the loader will handle it automatically

3. **Failed to load bundle**:
   - Cause: Server error or network issue
   - Solution: Check server logs and try again with smaller batch size

For more information, see the [Quick Reference](quick-reference.md) guide.
