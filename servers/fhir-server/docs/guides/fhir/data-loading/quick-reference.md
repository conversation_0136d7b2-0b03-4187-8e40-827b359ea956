# FHIR Data Loading Quick Reference

This guide provides a quick reference for loading FHIR data into the HAPI FHIR server using different methods.

## Table of Contents

- [Overview](#overview)
- [Comparison of Loading Methods](#comparison-of-loading-methods)
- [Method 1: Transaction Bundles](#method-1-transaction-bundles)
- [Method 2: Selective Loading](#method-2-selective-loading)
- [Performance Monitoring](#performance-monitoring)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Overview

There are two main methods for loading FHIR data into the HAPI FHIR server:

1. **Transaction Bundles**: Convert NDJSON files to transaction bundles and load them directly
2. **Selective Loading**: Analyze references between resources and load them in the correct order

## Comparison of Loading Methods

| Feature | Transaction Bundles | Selective Loading |
|---------|---------------------|-------------------|
| **Speed** | Faster | Slightly slower |
| **Reference Integrity** | Requires server configuration | Preserved by default |
| **Server Configuration** | Needs validation disabled | Works with default settings |
| **Complexity** | Simpler | More complex |
| **Use Case** | Large datasets, performance-critical | Reference integrity critical |
| **Error Handling** | Less robust | More robust |

## Method 1: Transaction Bundles

### Step 1: Convert NDJSON to Transaction Bundles

```bash
python data_loading/ndjson_to_bundle.py --input-dir data/sample_fhir/bulk-export --output-dir data/generated_bundles
```

### Step 2: Configure Server for Import Mode (Optional)

For large datasets or when referential integrity is not critical, configure the server for import mode by editing `docker-compose-postgres.yml`:

```yaml
environment:
  # Add these lines
  - hapi.fhir.validation.enabled=false
  - hapi.fhir.validation.request_validator.mode=NONE
  - hapi.fhir.auto_create_placeholder_reference_targets=true
  - hapi.fhir.enforce_referential_integrity_on_write=false
  - hapi.fhir.enforce_referential_integrity_on_delete=false
```

Then restart the server:

```bash
./manage-fhir-server.sh restart postgres
```

### Step 3: Load Transaction Bundles

```bash
python data_loading/load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir --export-performance
```

## Method 2: Selective Loading

### Step 1: Prepare NDJSON Files

Ensure your NDJSON files are in the correct directory:

```bash
ls -la data/sample_fhir/bulk-export
```

### Step 2: Run Selective Loader

```bash
python data_loading/selective_loader.py --data-dir data/sample_fhir/bulk-export --server-url http://localhost:8080/fhir --verify
```

The selective loader will:
1. Analyze references between resources
2. Determine the optimal loading order
3. Load resources in the correct order
4. Verify that resources were loaded correctly

## Performance Monitoring

The `load_all_bundles.py` script includes performance monitoring capabilities:

```bash
python data_loading/load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir --export-performance
```

Performance reports are saved to the `performance_reports` directory and include:
- Processing time
- Resources per second
- System resource usage (CPU, memory)
- Success rates by resource type

## Best Practices

1. **Clean the server before loading data**:
   ```bash
   # Stop the server
   ./manage-fhir-server.sh stop postgres
   
   # Remove the PostgreSQL volume
   docker volume rm fhir-server_postgres-data
   
   # Start the server with a clean database
   ./manage-fhir-server.sh start postgres
   ```

2. **Start with small datasets** before moving to larger ones

3. **Verify loaded resources** after loading

4. **Restore default configuration** after using import mode

## Troubleshooting

### Common Issues

1. **409 Conflict Errors**:
   - Cause: Resources with the same ID already exist
   - Solution: Clean the server before loading

2. **Reference Integrity Errors**:
   - Cause: Referenced resources don't exist
   - Solution: Use selective loading or configure server for import mode

3. **Performance Issues**:
   - Cause: Large datasets or server configuration
   - Solution: Use transaction bundles with import mode configuration

For more detailed instructions, see:
- [Direct Transaction Tutorial](direct-transaction-tutorial.md)
- [Selective Loading Guide](selective-loading.md)
- [Performance Metrics Guide](performance-metrics.md)
