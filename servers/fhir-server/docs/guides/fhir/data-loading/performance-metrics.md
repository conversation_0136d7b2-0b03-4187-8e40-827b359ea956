# Performance Metrics Guide

This guide explains how to use the performance monitoring capabilities of the FHIR data loading scripts.

## Table of Contents

- [Overview](#overview)
- [Collecting Performance Metrics](#collecting-performance-metrics)
- [Understanding the Metrics](#understanding-the-metrics)
- [Performance Reports](#performance-reports)
- [Optimizing Performance](#optimizing-performance)
- [Comparative Analysis](#comparative-analysis)
- [Advanced Monitoring](#advanced-monitoring)

## Overview

The FHIR data loading scripts include performance monitoring capabilities that track:

- Processing time
- Resources per second
- System resource usage (CPU, memory)
- Success rates by resource type

These metrics help you optimize your data loading process and compare different loading methods.

## Collecting Performance Metrics

### Using load_all_bundles.py

To collect performance metrics when loading transaction bundles:

```bash
python data_loading/load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir --export-performance
```

The `--export-performance` flag enables detailed performance monitoring and exports the results to a JSON file.

### Using selective_loader.py

The selective loader automatically tracks basic performance metrics:

```bash
python data_loading/selective_loader.py --data-dir data/sample_fhir/bulk-export --server-url http://localhost:8080/fhir --verify
```

## Understanding the Metrics

### Key Performance Indicators

| Metric | Description | Ideal Value |
|--------|-------------|-------------|
| **Resources Per Second** | Number of resources loaded per second | Higher is better |
| **Average Time Per Bundle** | Average time to process each bundle | Lower is better |
| **Success Rate** | Percentage of resources loaded successfully | 100% |
| **Peak CPU Usage** | Maximum CPU usage during loading | Depends on hardware |
| **Peak Memory Usage** | Maximum memory usage during loading | Depends on hardware |

### Example Output

```
================================================================================
                       FHIR BUNDLE LOADING SUMMARY REPORT
================================================================================

RESOURCE TYPE BREAKDOWN:
--------------------------------------------------------------------------------
Resource Type        Bundles              Resources            Time            Success Rate
--------------------------------------------------------------------------------
Patient              1/1                  13/13                0.27s           100.0%    

OVERALL STATISTICS:
--------------------------------------------------------------------------------
Total Processing Time:      00:00:00 (HH:MM:SS)
Average Time Per Bundle:    0.27 seconds
Total Bundles Processed:    1/1 (100.0%)
Total Resources Processed:  13/13 (100.0%)

PERFORMANCE METRICS:
--------------------------------------------------------------------------------
Resources Per Second:       34.77
Bundles Per Second:         2.67
Average Resource Size:      13.00 resources/bundle

SYSTEM RESOURCE USAGE:
--------------------------------------------------------------------------------
CPU Cores:                  14 physical, 14 logical
Total System Memory:        36.0 GB
Peak CPU Usage:             18.9%
Peak Memory Usage:          13.00 GB (77.6%)

TOP RESOURCE TYPES BY VOLUME:
--------------------------------------------------------------------------------
1. Patient: 13 resources (100.0% of total)
================================================================================
```

## Performance Reports

Performance reports are saved to the `performance_reports` directory with filenames that include timestamps:

```
performance_data_20250514_134159_20250514_134159.json
```

### Report Structure

The JSON report includes:

- **Metadata**: Timestamp, server URL, command-line options
- **Overall Statistics**: Total time, resources processed, success rate
- **Resource Type Breakdown**: Statistics by resource type
- **System Resource Usage**: CPU, memory, disk I/O
- **Error Details**: Information about any failures

## Optimizing Performance

### Server Configuration

For maximum performance:

1. Configure the server for import mode:
   - Disable validation
   - Disable referential integrity checks
   - Enable placeholder reference targets

2. Adjust batch size:
   - Larger batches (500-1000 resources) for faster loading
   - Smaller batches (50-100 resources) for better error handling

3. Hardware considerations:
   - Allocate more memory to the FHIR server
   - Use SSD storage for the database
   - Increase CPU allocation

### Script Configuration

Optimize the loading scripts:

1. Adjust batch size with `--batch-size`
2. Process multiple directories in parallel
3. Use transaction bundles for maximum throughput

## Comparative Analysis

To compare different loading methods or configurations:

1. Run the loading process with different parameters
2. Collect performance reports for each run
3. Compare key metrics:
   - Resources per second
   - Success rate
   - System resource usage

Example comparison:

| Method | Resources/Second | Success Rate | Peak Memory |
|--------|------------------|--------------|-------------|
| Transaction Bundles | 82.26 | 100% | 12.59 GB |
| Selective Loading | 50.00 | 100% | 13.00 GB |

## Advanced Monitoring

For more detailed monitoring:

1. Enable system monitoring tools:
   - `htop` for CPU and memory
   - `iotop` for disk I/O
   - `nethogs` for network usage

2. Monitor the FHIR server logs:
   ```bash
   docker logs -f fhir-server-hapi-fhir-server-1
   ```

3. Monitor the database:
   ```bash
   docker logs -f fhir-server-fhir-postgres-1
   ```

For more information, see the [Quick Reference](quick-reference.md) guide.
