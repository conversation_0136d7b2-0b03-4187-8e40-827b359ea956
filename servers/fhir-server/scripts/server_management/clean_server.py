#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to clean a HAPI FHIR server by deleting all resources.

This script:
1. Gets a list of all resource types on the server
2. For each resource type, gets all resources and deletes them
3. Reports the number of resources deleted

References:
- FHIR RESTful API: https://www.hl7.org/fhir/http.html#delete
- HAPI FHIR Server: https://hapifhir.io/
"""

import argparse
import sys
import time
from typing import Dict, List, Any, Set, Tuple
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

import requests
from requests.exceptions import RequestException

from core.logging import logger
import core.config as config

def get_resource_types(server_url: str) -> List[str]:
    """
    Get a list of all resource types on the FHIR server.

    Args:
        server_url: URL of the FHIR server

    Returns:
        List of resource types
    """
    try:
        # Get the server's capability statement
        response = requests.get(
            f"{server_url}/metadata",
            headers={"Accept": "application/fhir+json"},
            timeout=10
        )

        if response.status_code != 200:
            logger.error(f"Failed to get metadata from server: {response.status_code} - {response.text}")
            return []

        # Extract resource types from the capability statement
        capability = response.json()
        resource_types = []

        for rest in capability.get("rest", []):
            for resource in rest.get("resource", []):
                resource_type = resource.get("type")
                if resource_type:
                    resource_types.append(resource_type)

        return resource_types

    except Exception as e:
        logger.error(f"Error getting resource types: {e}")
        return []

def delete_resources(server_url: str, resource_type: str) -> int:
    """
    Delete all resources of a specific type from the FHIR server.

    Args:
        server_url: URL of the FHIR server
        resource_type: Type of resources to delete

    Returns:
        Number of resources deleted
    """
    deleted_count = 0

    try:
        # Get all resources of this type
        response = requests.get(
            f"{server_url}/{resource_type}?_count=100",
            headers={"Accept": "application/fhir+json"},
            timeout=30
        )

        if response.status_code != 200:
            logger.warning(f"Failed to get resources of type {resource_type}: {response.status_code} - {response.text}")
            return 0

        bundle = response.json()

        # Delete each resource
        for entry in bundle.get("entry", []):
            resource = entry.get("resource", {})
            resource_id = resource.get("id")

            if not resource_id:
                continue

            try:
                delete_response = requests.delete(
                    f"{server_url}/{resource_type}/{resource_id}",
                    timeout=10
                )

                if delete_response.status_code in (200, 204):
                    deleted_count += 1
                else:
                    logger.warning(f"Failed to delete {resource_type}/{resource_id}: {delete_response.status_code}")

            except RequestException as e:
                logger.warning(f"Error deleting {resource_type}/{resource_id}: {e}")

        # Check if there are more resources to delete
        total = bundle.get("total", 0)
        if total > deleted_count:
            logger.info(f"Deleted {deleted_count} out of {total} {resource_type} resources")

            # Get the next page of resources
            next_link = None
            for link in bundle.get("link", []):
                if link.get("relation") == "next":
                    next_link = link.get("url")
                    break

            if next_link:
                # Make a recursive call to delete the next page
                deleted_count += delete_resources(next_link, resource_type)

        return deleted_count

    except Exception as e:
        logger.error(f"Error deleting resources of type {resource_type}: {e}")
        return deleted_count

def clean_server(server_url: str) -> bool:
    """
    Clean a FHIR server by deleting all resources.

    Args:
        server_url: URL of the FHIR server

    Returns:
        True if successful, False otherwise
    """
    logger.info(f"Cleaning FHIR server at {server_url}")

    # Get all resource types
    resource_types = get_resource_types(server_url)
    if not resource_types:
        logger.error("Failed to get resource types from server")
        return False

    logger.info(f"Found {len(resource_types)} resource types on server")

    # Delete resources in reverse order of dependencies
    total_deleted = 0

    # First, delete resources that are likely to have references to other resources
    for resource_type in reversed(config.RESOURCE_ORDER):
        if resource_type in resource_types:
            logger.info(f"Deleting resources of type {resource_type}")
            deleted = delete_resources(server_url, resource_type)
            total_deleted += deleted
            logger.info(f"Deleted {deleted} {resource_type} resources")

    # Then, delete any remaining resource types
    for resource_type in resource_types:
        if resource_type not in config.RESOURCE_ORDER:
            logger.info(f"Deleting resources of type {resource_type}")
            deleted = delete_resources(server_url, resource_type)
            total_deleted += deleted
            logger.info(f"Deleted {deleted} {resource_type} resources")

    logger.info(f"Cleaning completed: deleted {total_deleted} resources")
    return True

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Clean a HAPI FHIR server by deleting all resources."
    )
    parser.add_argument(
        "--server-url",
        default=config.DEFAULT_SERVER_URL,
        help=f"URL of the FHIR server (default: {config.DEFAULT_SERVER_URL})"
    )
    parser.add_argument(
        "--confirm",
        action="store_true",
        help="Confirm that you want to delete all resources (required)"
    )
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    if not args.confirm:
        logger.error("You must confirm that you want to delete all resources by using the --confirm flag")
        return 1

    # Clean the server
    start_time = time.time()
    success = clean_server(args.server_url)
    end_time = time.time()

    # Display summary
    elapsed_time = end_time - start_time
    if success:
        logger.info(f"Server cleaning completed successfully in {elapsed_time:.1f} seconds")
        return 0
    else:
        logger.error(f"Server cleaning completed with errors in {elapsed_time:.1f} seconds")
        return 1

if __name__ == "__main__":
    sys.exit(main())
