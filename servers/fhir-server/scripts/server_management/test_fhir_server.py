#!/usr/bin/env python3
"""
Script to test connectivity and functionality of the HAPI FHIR server.

This script performs basic tests to verify that the HAPI FHIR server
is running correctly and can handle basic CRUD operations. It creates
a test patient with proper business identifiers and verifies that the
patient can be retrieved.

Usage:
    python test_fhir_server.py [--server-url URL]

Options:
    --server-url URL    URL of the FHIR server (default: from env or http://localhost:8080/fhir)

Note: Run with the project's Conda environment: `conda activate fhir-omop`

References:
- HAPI FHIR REST Operations: https://hapifhir.io/hapi-fhir/docs/server_plain/rest_operations.html
- HAPI FHIR Resource References: https://hapifhir.io/hapi-fhir/docs/model/references.html
- FHIR Resource Identification: https://build.fhir.org/resource.html#identification
"""
import os
import sys
import argparse
from typing import Optional
import requests
from dotenv import load_dotenv
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logging import logger

# Load environment variables
load_dotenv()

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Test FHIR server connectivity and functionality"
    )
    parser.add_argument(
        "--server-url",
        default=os.getenv("FHIR_SERVER_URL", None),
        help="URL of the FHIR server (default: from environment or http://localhost:PORT/fhir)"
    )
    return parser.parse_args()

# Parse command line arguments
args = parse_args()

# FHIR server configuration
FHIR_PORT = os.getenv("FHIR_PORT", "8080")
# Use server URL from command line, environment variable, or construct from port
FHIR_SERVER_URL = args.server_url if args.server_url else f"http://localhost:{FHIR_PORT}/fhir"
FHIR_USERNAME = os.getenv("FHIR_USERNAME", "")
FHIR_PASSWORD = os.getenv("FHIR_PASSWORD", "")

# Authentication configuration
auth = None
if FHIR_USERNAME and FHIR_PASSWORD:
    auth = (FHIR_USERNAME, FHIR_PASSWORD)


def check_server_status() -> bool:
    """Check if the FHIR server is running.

    Attempts to connect to the FHIR server's metadata endpoint
    to verify that the server is running and accessible.

    Returns
    -------
    bool
        True if the server is running, False otherwise.
    """
    try:
        response = requests.get(f"{FHIR_SERVER_URL}/metadata", auth=auth)
        if response.status_code == 200:
            logger.info("✅ FHIR server is running.")
            return True
        else:
            print(f"❌ FHIR server responded with status code: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Could not connect to FHIR server: {e}")
        return False


def create_test_patient() -> Optional[str]:
    """Create a test patient in the FHIR server.

    Creates a simple Patient resource in the FHIR server
    to test the create functionality. The patient includes
    a proper business identifier to enable conditional operations
    and prevent duplicates in future test runs.

    Returns
    -------
    Optional[str]
        The ID of the created patient, or None if creation failed.
    """
    # Generate a unique business identifier for the test patient
    import uuid
    test_id = str(uuid.uuid4())

    patient_data = {
        "resourceType": "Patient",
        "active": True,
        # Business identifier - crucial for conditional operations and duplicate prevention
        "identifier": [
            {
                "use": "official",
                "system": "http://fhir-server-test.example.org",
                "value": test_id
            }
        ],
        "name": [
            {
                "use": "official",
                "family": "Test",
                "given": ["Patient"]
            }
        ],
        "gender": "male",
        "birthDate": "1970-01-01"
    }

    headers = {"Content-Type": "application/fhir+json"}

    try:
        response = requests.post(
            f"{FHIR_SERVER_URL}/Patient",
            json=patient_data,
            headers=headers,
            auth=auth
        )

        if response.status_code in [200, 201]:
            patient_id = response.json().get("id")
            print(f"✅ Test patient created with ID: {patient_id}")
            return patient_id
        else:
            print(f"❌ Error creating test patient: {response.status_code}")
            print(response.text)
            return None
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when creating patient: {e}")
        return None


def get_patient(patient_id: str) -> bool:
    """Get a patient by ID.

    Retrieves a patient from the FHIR server by ID
    to test the read functionality. Also displays the
    business identifier of the patient to demonstrate
    the importance of identifiers in FHIR.

    Parameters
    ----------
    patient_id : str
        The ID of the patient to retrieve.

    Returns
    -------
    bool
        True if the patient was retrieved successfully, False otherwise.
    """
    try:
        response = requests.get(
            f"{FHIR_SERVER_URL}/Patient/{patient_id}",
            auth=auth
        )

        if response.status_code == 200:
            print(f"✅ Patient retrieved successfully:")
            patient_data = response.json()

            # Display logical ID
            print(f"  - Logical ID: {patient_data.get('id')}")

            # Display business identifier if available
            if 'identifier' in patient_data and patient_data['identifier']:
                identifier = patient_data['identifier'][0]
                if 'system' in identifier and 'value' in identifier:
                    print(f"  - Business identifier: {identifier['system']}|{identifier['value']}")

            # Display version ID if available
            if 'meta' in patient_data and 'versionId' in patient_data['meta']:
                print(f"  - Version ID: {patient_data['meta']['versionId']}")

            # Display demographic information
            name = patient_data.get('name', [{}])[0]
            full_name = f"{' '.join(name.get('given', []))} {name.get('family', '')}"
            print(f"  - Name: {full_name}")
            print(f"  - Gender: {patient_data.get('gender', 'not specified')}")
            print(f"  - Birth date: {patient_data.get('birthDate', 'not specified')}")
            return True
        else:
            print(f"❌ Error retrieving patient: {response.status_code}")
            print(response.text)
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Connection error when retrieving patient: {e}")
        return False


def run_tests() -> None:
    """Run all tests.

    Executes a series of tests to verify that the FHIR server
    is functioning correctly. The tests include checking server
    availability, creating a test patient with proper business
    identifiers, and retrieving the patient.
    """
    print("\n=== HAPI FHIR Server Test ===\n")
    print(f"Testing FHIR server at: {FHIR_SERVER_URL}")

    # Check server status
    if not check_server_status():
        print("\n❌ The FHIR server is not available. Make sure it is running.")
        sys.exit(1)

    # Create test patient
    print("\n--- Creating test patient ---")
    patient_id = create_test_patient()
    if not patient_id:
        print("\n❌ Could not create the test patient.")
        sys.exit(1)

    # Retrieve patient
    print("\n--- Retrieving patient ---")
    if not get_patient(patient_id):
        print("\n❌ Could not retrieve the patient.")
        sys.exit(1)

    print("\n✅ All tests completed successfully.")
    print(f"The HAPI FHIR server is working correctly at: {FHIR_SERVER_URL}")
    print("\nNote: The test patient includes a proper business identifier to enable")
    print("conditional operations and prevent duplicates in future test runs.")


def main():
    """Main function for the FHIR server test script."""
    run_tests()
    return 0

if __name__ == "__main__":
    sys.exit(main())
