#!/bin/bash
# Script to verify the FHIR server database configuration
# This script checks if the FHIR server is running and can perform basic operations
#
# References:
# - HAPI FHIR Database Support: https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
# - HAPI FHIR REST Operations: https://hapifhir.io/hapi-fhir/docs/server_plain/rest_operations.html

# Configuration
DB_TYPE="postgres"  # Only PostgreSQL is supported
FHIR_PORT=${FHIR_PORT:-"8080"}
FHIR_SERVER_URL="http://localhost:${FHIR_PORT}/fhir"

# Function to display usage information
show_help() {
    echo "Usage: $0"
    echo ""
    echo "Environment Variables:"
    echo "  FHIR_PORT        Port number for the FHIR server (default: 8080)"
    echo ""
    echo "Example:"
    echo "  $0"
    echo "  FHIR_PORT=9090 $0"
    exit 0
}

# Process command line arguments
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_help
fi

# Display configuration
echo "=== FHIR Server Database Verification ==="
echo "Database type: $DB_TYPE"
echo "FHIR server URL: $FHIR_SERVER_URL"
echo ""

# Check if the FHIR server is available
echo "Checking if FHIR server is available..."
if ! curl -s "$FHIR_SERVER_URL/metadata" > /dev/null; then
    echo "❌ Error: FHIR server not available at $FHIR_SERVER_URL"
    echo "Please make sure the server is running and accessible."
    exit 1
fi
echo "✅ FHIR server is available"

# Create a test patient to verify database write operations
echo "Creating test patient to verify database write operations..."
TEST_PATIENT='{
    "resourceType": "Patient",
    "active": true,
    "name": [
        {
            "family": "TestDB",
            "given": ["'$DB_TYPE'"]
        }
    ],
    "gender": "unknown",
    "birthDate": "2023-01-01"
}'

# Send the request to create the patient
RESPONSE=$(curl -s -X POST -H "Content-Type: application/fhir+json" -d "$TEST_PATIENT" "$FHIR_SERVER_URL/Patient")

# Check if the response contains a Patient resource with an ID
if echo "$RESPONSE" | grep -q "\"resourceType\"[[:space:]]*:[[:space:]]*\"Patient\"" && echo "$RESPONSE" | grep -q "\"id\"[[:space:]]*:"; then
    # Extract the patient ID
    PATIENT_ID=$(echo "$RESPONSE" | grep -o '"id"[[:space:]]*:[[:space:]]*"[^"]*"' | sed 's/.*"id"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/')
    echo "✅ Test patient created successfully with ID: $PATIENT_ID"
else
    echo "❌ Failed to create test patient. Database write operation failed."
    echo "Response: $RESPONSE"
    exit 1
fi

# Verify database read operations by searching for the patient
echo "Searching for the test patient to verify database read operations..."
SEARCH_RESPONSE=$(curl -s "$FHIR_SERVER_URL/Patient?family=TestDB")

# Check if the search was successful
if echo "$SEARCH_RESPONSE" | grep -q "\"resourceType\"[[:space:]]*:[[:space:]]*\"Bundle\""; then
    # The search was successful, but we need to check if any patients were found
    if echo "$SEARCH_RESPONSE" | grep -q "\"entry\"[[:space:]]*:"; then
        # Extract the total count if available
        if echo "$SEARCH_RESPONSE" | grep -q "\"total\"[[:space:]]*:"; then
            TOTAL_COUNT=$(echo "$SEARCH_RESPONSE" | grep -o '"total"[[:space:]]*:[[:space:]]*[0-9]*' | sed 's/.*"total"[[:space:]]*:[[:space:]]*\([0-9]*\).*/\1/')
            echo "✅ Found $TOTAL_COUNT patient(s) matching the search criteria. Database read operation successful."
        else
            echo "✅ Found patient(s) matching the search criteria. Database read operation successful."
        fi
    else
        # No entries found, but the search was successful
        echo "⚠️ No patients found matching the search criteria, but the search operation was successful."
    fi
else
    echo "❌ Failed to search for patients. Database read operation failed."
    echo "Response: $SEARCH_RESPONSE"
    exit 1
fi

# Display database information
echo ""
echo "PostgreSQL Database Information:"
echo "- Using PostgreSQL with HAPI FHIR"
echo "- Recommended dialect: ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect"
echo "- For more information, see: https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html"

echo ""
echo "✅ Database verification completed successfully."
echo "The FHIR server with $DB_TYPE database is properly configured and operational."
exit 0
