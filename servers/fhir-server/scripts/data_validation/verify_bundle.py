#!/usr/bin/env python3
"""
Script to verify that a FHIR transaction bundle was created correctly.

This script:
1. Compares the original NDJSON file with the generated bundle
2. Verifies that all resources are included in the bundle
3. Checks that bundle entries have the correct structure
4. Reports any discrepancies

References:
- FHIR Bundle Resource: https://www.hl7.org/fhir/bundle.html
- FHIR RESTful API: https://www.hl7.org/fhir/http.html#transaction
"""

import argparse
import json
import sys
from typing import Dict, List, Any, Set, Tuple
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.utils import read_ndjson_file
from core.logging import logger
import core.config as config

def load_bundle_from_file(file_path: str) -> Dict[str, Any]:
    """
    Load a FHIR Bundle from a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        FHIR Bundle as a dictionary
    """
    try:
        with open(file_path, 'r') as f:
            bundle = json.load(f)

        # Verify that this is a FHIR Bundle
        if bundle.get("resourceType") != "Bundle" or bundle.get("type") != "transaction":
            logger.error(f"File {file_path} does not contain a valid FHIR transaction bundle")
            return {}

        return bundle
    except Exception as e:
        logger.error(f"Error loading bundle from {file_path}: {e}")
        return {}

def extract_resource_ids(resources: List[Dict[str, Any]]) -> Dict[str, Set[str]]:
    """
    Extract resource IDs from a list of resources, grouped by resource type.

    Args:
        resources: List of FHIR resources

    Returns:
        Dictionary mapping resource types to sets of resource IDs
    """
    resource_ids = {}

    for resource in resources:
        resource_type = resource.get("resourceType")
        resource_id = resource.get("id")

        if not resource_type or not resource_id:
            continue

        if resource_type not in resource_ids:
            resource_ids[resource_type] = set()

        resource_ids[resource_type].add(resource_id)

    return resource_ids

def extract_bundle_resource_ids(bundle: Dict[str, Any]) -> Dict[str, Set[str]]:
    """
    Extract resource IDs from a FHIR Bundle, grouped by resource type.

    Args:
        bundle: FHIR Bundle as a dictionary

    Returns:
        Dictionary mapping resource types to sets of resource IDs
    """
    resource_ids = {}

    for entry in bundle.get("entry", []):
        resource = entry.get("resource", {})
        resource_type = resource.get("resourceType")
        resource_id = resource.get("id")

        if not resource_type or not resource_id:
            continue

        if resource_type not in resource_ids:
            resource_ids[resource_type] = set()

        resource_ids[resource_type].add(resource_id)

    return resource_ids

def verify_bundle_structure(bundle: Dict[str, Any]) -> List[str]:
    """
    Verify that a FHIR Bundle has the correct structure.

    Args:
        bundle: FHIR Bundle as a dictionary

    Returns:
        List of error messages, empty if no errors
    """
    errors = []

    # Check bundle type
    if bundle.get("resourceType") != "Bundle":
        errors.append("Bundle resourceType is not 'Bundle'")

    if bundle.get("type") != "transaction":
        errors.append("Bundle type is not 'transaction'")

    # Check entries
    entries = bundle.get("entry", [])
    if not entries:
        errors.append("Bundle has no entries")

    for i, entry in enumerate(entries):
        # Check resource
        if "resource" not in entry:
            errors.append(f"Entry {i+1} has no resource")
            continue

        resource = entry.get("resource", {})
        if "resourceType" not in resource:
            errors.append(f"Resource in entry {i+1} has no resourceType")

        if "id" not in resource:
            errors.append(f"Resource in entry {i+1} has no id")

        # Check request
        if "request" not in entry:
            errors.append(f"Entry {i+1} has no request")
            continue

        request = entry.get("request", {})
        if request.get("method") != "PUT":
            errors.append(f"Request method in entry {i+1} is not 'PUT'")

        if "url" not in request:
            errors.append(f"Request in entry {i+1} has no url")
        else:
            url = request.get("url", "")
            resource_type = resource.get("resourceType", "")
            resource_id = resource.get("id", "")
            expected_url = f"{resource_type}/{resource_id}"

            if url != expected_url:
                errors.append(f"Request url in entry {i+1} is '{url}', expected '{expected_url}'")

    return errors

def compare_resources_and_bundle(ndjson_file: str, bundle_file: str) -> Tuple[bool, List[str]]:
    """
    Compare resources in an NDJSON file with a FHIR Bundle.

    Args:
        ndjson_file: Path to the NDJSON file
        bundle_file: Path to the bundle file

    Returns:
        Tuple of (success flag, list of error messages)
    """
    # Read resources from NDJSON file
    resources = read_ndjson_file(ndjson_file)
    if not resources:
        return False, ["Failed to read resources from NDJSON file"]

    # Load bundle from file
    bundle = load_bundle_from_file(bundle_file)
    if not bundle:
        return False, ["Failed to load bundle from file"]

    # Verify bundle structure
    structure_errors = verify_bundle_structure(bundle)
    if structure_errors:
        return False, structure_errors

    # Extract resource IDs from NDJSON and bundle
    ndjson_resource_ids = extract_resource_ids(resources)
    bundle_resource_ids = extract_bundle_resource_ids(bundle)

    # Compare resource counts
    errors = []
    for resource_type, ids in ndjson_resource_ids.items():
        if resource_type not in bundle_resource_ids:
            errors.append(f"Resource type {resource_type} is missing from bundle")
            continue

        bundle_ids = bundle_resource_ids[resource_type]

        if len(ids) != len(bundle_ids):
            errors.append(f"Resource count mismatch for {resource_type}: {len(ids)} in NDJSON, {len(bundle_ids)} in bundle")

        # Check for missing IDs
        missing_ids = ids - bundle_ids
        if missing_ids:
            errors.append(f"{len(missing_ids)} {resource_type} resources are missing from bundle")
            if len(missing_ids) <= 5:
                for missing_id in missing_ids:
                    errors.append(f"  - Missing {resource_type}/{missing_id}")

    # Check for extra resource types in bundle
    for resource_type in bundle_resource_ids:
        if resource_type not in ndjson_resource_ids:
            errors.append(f"Extra resource type {resource_type} in bundle")

    if errors:
        return False, errors
    else:
        return True, []

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Verify that a FHIR transaction bundle was created correctly."
    )
    parser.add_argument(
        "--ndjson-file",
        required=True,
        help="Path to the original NDJSON file"
    )
    parser.add_argument(
        "--bundle-file",
        required=True,
        help="Path to the generated bundle file"
    )
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    success, errors = compare_resources_and_bundle(args.ndjson_file, args.bundle_file)

    if success:
        logger.info("Bundle verification completed successfully")
        return 0
    else:
        logger.error("Bundle verification failed")
        for error in errors:
            logger.error(f"  - {error}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
