#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify that FHIR resources were loaded correctly into a HAPI FHIR server.

This script:
1. Checks that resources exist on the server with their original IDs
2. Verifies that references between resources are maintained
3. Reports any discrepancies

References:
- FHIR RESTful API: https://www.hl7.org/fhir/http.html
- HAPI FHIR Server: https://hapifhir.io/
"""

import argparse
import json
import sys
import time
from typing import Dict, List, Any, Set, Tuple, Optional
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

import requests
from requests.exceptions import RequestException

from core.utils import read_ndjson_file
from core.logging import logger
import core.config as config

def get_resource_from_server(server_url: str, resource_type: str, resource_id: str) -> Optional[Dict[str, Any]]:
    """
    Get a resource from the FHIR server.

    Args:
        server_url: URL of the FHIR server
        resource_type: Type of the resource
        resource_id: ID of the resource

    Returns:
        Resource as a dictionary, or None if not found
    """
    try:
        response = requests.get(
            f"{server_url}/{resource_type}/{resource_id}",
            headers={"Accept": "application/fhir+json"},
            timeout=10
        )

        if response.status_code == 200:
            return response.json()
        else:
            logger.warning(f"Resource {resource_type}/{resource_id} not found on server (status code: {response.status_code})")
            return None

    except RequestException as e:
        logger.error(f"Error getting resource {resource_type}/{resource_id}: {e}")
        return None

def extract_references(resource: Dict[str, Any]) -> List[Tuple[str, str]]:
    """
    Extract references from a FHIR resource.

    Args:
        resource: FHIR resource as a dictionary

    Returns:
        List of (resource_type, resource_id) tuples
    """
    references = []
    resource_json = json.dumps(resource)

    # Look for direct references in the format "reference":"ResourceType/id"
    import re
    ref_pattern = r'"reference"\s*:\s*"([^/]+)/([^"]+)"'
    for match in re.finditer(ref_pattern, resource_json):
        resource_type, resource_id = match.groups()
        references.append((resource_type, resource_id))

    return references

def verify_resource_exists(server_url: str, resource_type: str, resource_id: str) -> bool:
    """
    Verify that a resource exists on the FHIR server.

    Args:
        server_url: URL of the FHIR server
        resource_type: Type of the resource
        resource_id: ID of the resource

    Returns:
        True if the resource exists, False otherwise
    """
    resource = get_resource_from_server(server_url, resource_type, resource_id)
    return resource is not None

def verify_references(server_url: str, resource: Dict[str, Any]) -> Tuple[int, int]:
    """
    Verify that references in a resource exist on the server.

    Args:
        server_url: URL of the FHIR server
        resource: FHIR resource as a dictionary

    Returns:
        Tuple of (valid reference count, invalid reference count)
    """
    references = extract_references(resource)
    valid_count = 0
    invalid_count = 0

    for resource_type, resource_id in references:
        if verify_resource_exists(server_url, resource_type, resource_id):
            valid_count += 1
        else:
            invalid_count += 1
            logger.warning(f"Reference to {resource_type}/{resource_id} is invalid")

    return valid_count, invalid_count

def verify_resources_from_list(resources: List[Dict[str, Any]], server_url: str, sample_size: int = 0) -> bool:
    """
    Verify that a list of resources exist on the server with their references intact.

    Args:
        resources: List of FHIR resources
        server_url: URL of the FHIR server
        sample_size: Number of resources to sample (0 for all)

    Returns:
        True if verification is successful, False otherwise
    """
    # Sample resources if requested
    if sample_size > 0 and sample_size < len(resources):
        import random
        resources = random.sample(resources, sample_size)

    # Verify each resource
    resource_count = len(resources)
    found_count = 0
    not_found_count = 0
    valid_ref_count = 0
    invalid_ref_count = 0

    for i, resource in enumerate(resources):
        resource_type = resource.get("resourceType")
        resource_id = resource.get("id")

        if not resource_type or not resource_id:
            logger.warning(f"Resource at position {i+1} is missing resourceType or id")
            not_found_count += 1
            continue

        # Show progress
        if (i + 1) % 10 == 0 or i + 1 == resource_count:
            logger.info(f"Verifying resource {i+1}/{resource_count}...")

        # Verify that the resource exists
        server_resource = get_resource_from_server(server_url, resource_type, resource_id)
        if server_resource:
            found_count += 1

            # Verify references
            valid_refs, invalid_refs = verify_references(server_url, server_resource)
            valid_ref_count += valid_refs
            invalid_ref_count += invalid_refs
        else:
            not_found_count += 1

    # Calculate success rate
    if resource_count > 0:
        found_rate = (found_count / resource_count) * 100
    else:
        found_rate = 0

    if valid_ref_count + invalid_ref_count > 0:
        valid_ref_rate = (valid_ref_count / (valid_ref_count + invalid_ref_count)) * 100
    else:
        valid_ref_rate = 100  # No references to validate

    # Log statistics
    logger.info("\nVerification Statistics:")
    logger.info(f"  - Resources to verify: {resource_count}")
    logger.info(f"  - Resources found on server: {found_count} ({found_rate:.1f}%)")
    logger.info(f"  - Resources not found: {not_found_count}")
    logger.info(f"  - Valid references: {valid_ref_count}")
    logger.info(f"  - Invalid references: {invalid_ref_count}")
    logger.info(f"  - Reference validity rate: {valid_ref_rate:.1f}%")

    # Determine success
    success = found_rate > 95 and valid_ref_rate > 95

    return success

def verify_resources_from_ndjson(ndjson_file: str, server_url: str, sample_size: int = 0) -> Tuple[bool, Dict[str, Any]]:
    """
    Verify that resources from an NDJSON file exist on the server with their references intact.

    Args:
        ndjson_file: Path to the NDJSON file
        server_url: URL of the FHIR server
        sample_size: Number of resources to sample (0 for all)

    Returns:
        Tuple of (success flag, statistics dictionary)
    """
    # Read resources from NDJSON file
    resources = read_ndjson_file(ndjson_file)
    if not resources:
        return False, {}

    # Sample resources if requested
    if sample_size > 0 and sample_size < len(resources):
        import random
        resources = random.sample(resources, sample_size)

    # Verify each resource
    resource_count = len(resources)
    found_count = 0
    not_found_count = 0
    valid_ref_count = 0
    invalid_ref_count = 0

    for i, resource in enumerate(resources):
        resource_type = resource.get("resourceType")
        resource_id = resource.get("id")

        if not resource_type or not resource_id:
            logger.warning(f"Resource at position {i+1} is missing resourceType or id")
            not_found_count += 1
            continue

        # Show progress
        if (i + 1) % 10 == 0 or i + 1 == resource_count:
            logger.info(f"Verifying resource {i+1}/{resource_count}...")

        # Verify that the resource exists
        server_resource = get_resource_from_server(server_url, resource_type, resource_id)
        if server_resource:
            found_count += 1

            # Verify references
            valid_refs, invalid_refs = verify_references(server_url, server_resource)
            valid_ref_count += valid_refs
            invalid_ref_count += invalid_refs
        else:
            not_found_count += 1

    # Calculate success rate
    if resource_count > 0:
        found_rate = (found_count / resource_count) * 100
    else:
        found_rate = 0

    if valid_ref_count + invalid_ref_count > 0:
        valid_ref_rate = (valid_ref_count / (valid_ref_count + invalid_ref_count)) * 100
    else:
        valid_ref_rate = 100  # No references to validate

    # Prepare statistics
    stats = {
        "resource_count": resource_count,
        "found_count": found_count,
        "not_found_count": not_found_count,
        "found_rate": found_rate,
        "valid_ref_count": valid_ref_count,
        "invalid_ref_count": invalid_ref_count,
        "valid_ref_rate": valid_ref_rate
    }

    # Determine success
    success = found_rate > 95 and valid_ref_rate > 95

    return success, stats

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Verify that FHIR resources were loaded correctly into a HAPI FHIR server."
    )
    parser.add_argument(
        "--ndjson-file",
        required=True,
        help="Path to the NDJSON file containing the original resources"
    )
    parser.add_argument(
        "--server-url",
        default=config.DEFAULT_SERVER_URL,
        help=f"URL of the FHIR server (default: {config.DEFAULT_SERVER_URL})"
    )
    parser.add_argument(
        "--sample-size",
        type=int,
        default=0,
        help="Number of resources to sample (default: 0, meaning all resources)"
    )
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    success, stats = verify_resources_from_ndjson(args.ndjson_file, args.server_url, args.sample_size)

    # Display statistics
    logger.info("\nVerification Statistics:")
    logger.info(f"  - Resources in NDJSON: {stats['resource_count']}")
    logger.info(f"  - Resources found on server: {stats['found_count']} ({stats['found_rate']:.1f}%)")
    logger.info(f"  - Resources not found: {stats['not_found_count']}")
    logger.info(f"  - Valid references: {stats['valid_ref_count']}")
    logger.info(f"  - Invalid references: {stats['invalid_ref_count']}")
    logger.info(f"  - Reference validity rate: {stats['valid_ref_rate']:.1f}%")

    if success:
        logger.info("\nVerification completed successfully")
        return 0
    else:
        logger.error("\nVerification failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
