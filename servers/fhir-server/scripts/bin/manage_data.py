#!/usr/bin/env python3
"""
Unified command-line interface for FHIR data management operations.

This script provides a centralized interface for all FHIR data management operations,
including:
- Loading data into the FHIR server
- Validating FHIR data
- Managing the FHIR server
- Analyzing FHIR data

Usage:
    python manage_data.py <command> [options]

Commands:
    load                Load FHIR data into the server
    validate            Validate FHIR data
    analyze             Analyze FHIR data
    server              Manage the FHIR server
"""

import argparse
import sys
import os
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logging import configure_logging

# Configure logging
logger = configure_logging(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Unified command-line interface for FHIR data management operations."
    )

    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest="command", help="Command to execute")

    # Load command
    load_parser = subparsers.add_parser("load", help="Load FHIR data into the server")
    load_subparsers = load_parser.add_subparsers(dest="load_command", help="Load command to execute")

    # Load transaction bundles
    load_bundles_parser = load_subparsers.add_parser("bundles", help="Load transaction bundles")
    load_bundles_parser.add_argument(
        "--bundle-dir",
        required=True,
        help="Directory containing FHIR transaction bundles"
    )
    load_bundles_parser.add_argument(
        "--server-url",
        default=os.environ.get("FHIR_SERVER_URL", "http://localhost:8080/fhir"),
        help="URL of the FHIR server (default: http://localhost:8080/fhir)"
    )
    load_bundles_parser.add_argument(
        "--pattern",
        default="*.json",
        help="File pattern to match (default: *.json)"
    )
    load_bundles_parser.add_argument(
        "--export-performance",
        action="store_true",
        help="Export performance data to a JSON file for later analysis"
    )

    # Convert NDJSON to bundles
    convert_parser = load_subparsers.add_parser("convert", help="Convert NDJSON to transaction bundles")
    convert_input_group = convert_parser.add_mutually_exclusive_group(required=True)
    convert_input_group.add_argument(
        "--input-file",
        help="Path to the input NDJSON file"
    )
    convert_input_group.add_argument(
        "--input-dir",
        help="Directory containing NDJSON files to process"
    )
    convert_parser.add_argument(
        "--output-dir",
        default="data/generated_bundles",
        help="Directory to save output files (default: data/generated_bundles)"
    )
    convert_parser.add_argument(
        "--batch-size",
        type=int,
        default=500,
        help="Maximum number of resources per bundle (default: 500)"
    )

    # Selective loading
    selective_parser = load_subparsers.add_parser("selective", help="Selective loading with reference preservation")
    selective_parser.add_argument(
        "--data-dir",
        required=True,
        help="Directory containing NDJSON files"
    )
    selective_parser.add_argument(
        "--server-url",
        default=os.environ.get("FHIR_SERVER_URL", "http://localhost:8080/fhir"),
        help="URL of the FHIR server (default: http://localhost:8080/fhir)"
    )
    selective_parser.add_argument(
        "--verify",
        action="store_true",
        help="Verify that resources were loaded correctly"
    )

    # Validate command
    validate_parser = subparsers.add_parser("validate", help="Validate FHIR data")
    validate_subparsers = validate_parser.add_subparsers(dest="validate_command", help="Validate command to execute")

    # Verify bundle
    verify_bundle_parser = validate_subparsers.add_parser("bundle", help="Verify a transaction bundle")
    verify_bundle_parser.add_argument(
        "--bundle-file",
        required=True,
        help="Path to the transaction bundle file"
    )
    verify_bundle_parser.add_argument(
        "--ndjson-file",
        required=True,
        help="Path to the original NDJSON file"
    )

    # Verify loaded resources
    verify_loaded_parser = validate_subparsers.add_parser("loaded", help="Verify loaded resources")
    verify_loaded_parser.add_argument(
        "--server-url",
        default=os.environ.get("FHIR_SERVER_URL", "http://localhost:8080/fhir"),
        help="URL of the FHIR server (default: http://localhost:8080/fhir)"
    )
    verify_loaded_parser.add_argument(
        "--ndjson-file",
        required=True,
        help="Path to the NDJSON file containing resources to verify"
    )

    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze FHIR data")
    analyze_parser.add_argument(
        "--data-dir",
        required=True,
        help="Directory containing NDJSON files"
    )
    analyze_parser.add_argument(
        "--plan",
        action="store_true",
        help="Generate a loading plan based on the analysis"
    )
    analyze_parser.add_argument(
        "--save-output",
        action="store_true",
        help="Save analysis output to files"
    )

    # Server command
    server_parser = subparsers.add_parser("server", help="Manage the FHIR server")
    server_subparsers = server_parser.add_subparsers(dest="server_command", help="Server command to execute")

    # Clean server
    clean_parser = server_subparsers.add_parser("clean", help="Clean the FHIR server")
    clean_parser.add_argument(
        "--server-url",
        default=os.environ.get("FHIR_SERVER_URL", "http://localhost:8080/fhir"),
        help="URL of the FHIR server (default: http://localhost:8080/fhir)"
    )
    clean_parser.add_argument(
        "--confirm",
        action="store_true",
        help="Confirm that you want to delete all resources (required)"
    )

    # Test server
    test_parser = server_subparsers.add_parser("test", help="Test the FHIR server")
    test_parser.add_argument(
        "--server-url",
        default=os.environ.get("FHIR_SERVER_URL", "http://localhost:8080/fhir"),
        help="URL of the FHIR server (default: http://localhost:8080/fhir)"
    )

    return parser.parse_args()

def main():
    """Main function."""
    # Check if no arguments were provided
    if len(sys.argv) == 1:
        # Print help and exit
        parser = argparse.ArgumentParser(
            description="Unified command-line interface for FHIR data management operations."
        )
        parser.print_help()
        return 1

    args = parse_args()

    if not args.command:
        logger.error("No command specified")
        return 1

    # Handle load command
    if args.command == "load":
        if not args.load_command:
            logger.error("No load command specified")
            return 1

        if args.load_command == "bundles":
            from data_loading.load_all_bundles import main as load_bundles_main
            sys.argv = [sys.argv[0]]
            if args.bundle_dir:
                sys.argv.extend(["--bundle-dir", args.bundle_dir])
            if args.server_url:
                sys.argv.extend(["--server-url", args.server_url])
            if args.pattern:
                sys.argv.extend(["--pattern", args.pattern])
            if args.export_performance:
                sys.argv.append("--export-performance")
            return load_bundles_main()

        elif args.load_command == "convert":
            from data_loading.ndjson_to_bundle import main as convert_main
            sys.argv = [sys.argv[0]]
            if args.input_file:
                sys.argv.extend(["--input-file", args.input_file])
            if args.input_dir:
                sys.argv.extend(["--input-dir", args.input_dir])
            if args.output_dir:
                sys.argv.extend(["--output-dir", args.output_dir])
            if args.batch_size:
                sys.argv.extend(["--batch-size", str(args.batch_size)])
            return convert_main()

        elif args.load_command == "selective":
            from data_loading.selective_loader import main as selective_main
            sys.argv = [sys.argv[0]]
            if args.data_dir:
                sys.argv.extend(["--data-dir", args.data_dir])
            if args.server_url:
                sys.argv.extend(["--server-url", args.server_url])
            if args.verify:
                sys.argv.append("--verify")
            return selective_main()

    # Handle validate command
    elif args.command == "validate":
        if not args.validate_command:
            logger.error("No validate command specified")
            return 1

        if args.validate_command == "bundle":
            from data_validation.verify_bundle import main as verify_bundle_main
            sys.argv = [sys.argv[0]]
            if args.bundle_file:
                sys.argv.extend(["--bundle-file", args.bundle_file])
            if args.ndjson_file:
                sys.argv.extend(["--ndjson-file", args.ndjson_file])
            return verify_bundle_main()

        elif args.validate_command == "loaded":
            from data_validation.verify_loaded_resources import main as verify_loaded_main
            sys.argv = [sys.argv[0]]
            if args.server_url:
                sys.argv.extend(["--server-url", args.server_url])
            if args.ndjson_file:
                sys.argv.extend(["--ndjson-file", args.ndjson_file])
            return verify_loaded_main()

    # Handle analyze command
    elif args.command == "analyze":
        from data_loading.analyze_fhir_references import main as analyze_main
        sys.argv = [sys.argv[0]]
        if args.data_dir:
            sys.argv.extend(["--data-dir", args.data_dir])
        if args.plan:
            sys.argv.append("--plan")
        if args.save_output:
            sys.argv.append("--save-output")
        return analyze_main()

    # Handle server command
    elif args.command == "server":
        if not args.server_command:
            logger.error("No server command specified")
            return 1

        if args.server_command == "clean":
            from server_management.clean_server import main as clean_main
            sys.argv = [sys.argv[0]]
            if args.server_url:
                sys.argv.extend(["--server-url", args.server_url])
            if args.confirm:
                sys.argv.append("--confirm")
            return clean_main()

        elif args.server_command == "test":
            from server_management.test_fhir_server import main as test_main
            sys.argv = [sys.argv[0]]
            if args.server_url:
                sys.argv.extend(["--server-url", args.server_url])
            return test_main()

    logger.error(f"Unknown command: {args.command}")
    return 1

if __name__ == "__main__":
    sys.exit(main())
