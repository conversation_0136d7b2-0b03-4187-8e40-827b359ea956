# FHIR Server Scripts

This directory contains scripts for managing and interacting with the HAPI FHIR server.

## Table of Contents

- [Overview](#overview)
- [Directory Structure](#directory-structure)
- [Unified CLI](#unified-cli)
- [Modules](#modules)
  - [Core](#core)
  - [Data Loading](#data-loading)
  - [Data Validation](#data-validation)
  - [Server Management](#server-management)
- [Usage Examples](#usage-examples)
- [Best Practices](#best-practices)
- [Detailed Documentation](#detailed-documentation)

## Overview

These scripts provide various utilities for working with the HAPI FHIR server, including:

- Testing server connectivity and basic functionality
- Verifying database configuration
- Analyzing references between FHIR resources
- Loading FHIR data while preserving references
- Transaction bundle processing for efficient data loading

## Directory Structure

The scripts are organized into the following directories:

```
scripts/
├── bin/                           # Command-line entry points
│   └── manage_data.py             # Unified CLI for data management operations
├── core/                          # Core functionality
│   ├── config.py                  # Configuration settings
│   ├── logging.py                 # Logging configuration
│   └── utils.py                   # Common utilities
├── data_loading/                  # Data loading functionality
│   ├── analyze_fhir_references.py # FHIR reference analysis
│   ├── load_all_bundles.py        # Bulk loading of transaction bundles
│   ├── ndjson_to_bundle.py        # NDJSON to transaction bundle conversion
│   ├── reference_analyzer.py      # Reference analysis for selective loading
│   ├── selective_loader.py        # Selective loading with reference integrity
│   ├── send_bundle.py             # Send transaction bundles to FHIR server
│   └── stats.py                   # Statistics tracking for bundle processing
├── data_validation/               # Data validation functionality
│   ├── verify_bundle.py           # Verify transaction bundles
│   └── verify_loaded_resources.py # Verify loaded resources
└── server_management/             # Server management functionality
    ├── clean_server.py            # Clean the FHIR server (note: using Docker is recommended)
    ├── test_fhir_server.py        # Test FHIR server connectivity
    └── verify_database.sh         # Verify database configuration
```

## Unified CLI

The `bin/manage_data.py` script provides a unified command-line interface for all FHIR data management operations. It can be used to:

- Load data into the FHIR server
- Validate FHIR data
- Analyze FHIR data
- Manage the FHIR server

**Status**: The unified CLI is still in development and has known issues with some subcommands. For production use, it is strongly recommended to use the individual scripts directly as shown in the examples below. The unified CLI is provided as a preview of future functionality but should not be relied upon for critical operations.

**Usage**:
```bash
python bin/manage_data.py <command> [options]
```

**Commands**:
- `load`: Load FHIR data into the server
  - `bundles`: Load transaction bundles
  - `convert`: Convert NDJSON to transaction bundles
  - `selective`: Selective loading with reference preservation
- `validate`: Validate FHIR data
  - `bundle`: Verify a transaction bundle
  - `loaded`: Verify loaded resources
- `analyze`: Analyze FHIR data
- `server`: Manage the FHIR server
  - `clean`: Clean the FHIR server
  - `test`: Test the FHIR server

**Recommended Usage**:
```bash
# Use individual scripts directly (from the scripts directory)
python data_loading/load_all_bundles.py --bundle-dir ../../data/generated_bundles --server-url http://localhost:8080/fhir --export-performance
python data_loading/ndjson_to_bundle.py --input-dir ../../data/sample_fhir/bulk-export --output-dir ../../data/generated_bundles
python data_loading/selective_loader.py --data-dir ../../data/sample_fhir/bulk-export --server-url http://localhost:8080/fhir --verify
python server_management/test_fhir_server.py --server-url http://localhost:8080/fhir
```

## Modules

### Core

The `core` directory contains core functionality used by all other modules:

- `config.py`: Configuration settings for the FHIR server scripts
- `logging.py`: Logging configuration for consistent logging across all scripts
- `utils.py`: Common utility functions for file I/O, JSON handling, etc.

### Data Loading

The `data_loading` directory contains scripts for loading FHIR data into the FHIR server:

- `analyze_fhir_references.py`: Analyze references between FHIR resources
- `load_all_bundles.py`: Load all transaction bundles with comprehensive performance metrics
- `load_complete_with_references.py`: Load resources with complete references
- `ndjson_to_bundle.py`: Convert NDJSON files to transaction bundles
- `reference_analyzer.py`: Analyze references for selective loading
- `selective_loader.py`: Selective loading with reference integrity
- `send_bundle.py`: Send transaction bundles to the FHIR server
- `stats.py`: Statistics tracking and performance metrics (CPU, memory, disk I/O, network I/O)

### Data Validation

The `data_validation` directory contains scripts for validating FHIR data:

- `verify_bundle.py`: Verify that a transaction bundle was created correctly
- `verify_loaded_resources.py`: Verify that resources were loaded correctly

### Server Management

The `server_management` directory contains scripts for managing the FHIR server:

- `clean_server.py`: Clean the FHIR server by removing all resources (note: this script has limited functionality and may not work with all FHIR server configurations; using Docker to clean the server as described in the Best Practices section is strongly recommended)
- `test_fhir_server.py`: Test FHIR server connectivity and functionality
- `verify_database.sh`: Verify the database configuration of the FHIR server

## Usage Examples

### Basic Server Testing

```bash
# Test if the FHIR server is running
python bin/manage_data.py server test

# Verify the database configuration
./server_management/verify_database.sh postgres
```

### Loading FHIR Data

#### Method 1: Direct Transaction Bundles

```bash
# 1. Configure server for import mode (edit docker-compose-postgres.yml)
# Uncomment these lines in the environment section:
# - hapi.fhir.validation.enabled=false
# - hapi.fhir.validation.request_validator.mode=NONE
# - hapi.fhir.auto_create_placeholder_reference_targets=true
# - hapi.fhir.enforce_referential_integrity_on_write=false
# - hapi.fhir.enforce_referential_integrity_on_delete=false

# 2. Restart the server
../manage-fhir-server.sh restart postgres

# 3. Convert NDJSON to bundles and load them
python bin/manage_data.py load convert --input-file ../../data/sample_fhir/bulk-export/Patient.000.ndjson --output-dir ../../data/generated_bundles
python bin/manage_data.py load bundles --bundle-dir ../../data/generated_bundles --server-url http://localhost:8080/fhir
```

#### Method 2: Selective Loading

```bash
# Load data with selective loading
python bin/manage_data.py load selective --data-dir ../../data/sample_fhir/bulk-export --server-url http://localhost:8080/fhir --verify
```

## Best Practices

1. **Clean the server before loading data**:
   - **RECOMMENDED METHOD**: The most reliable way to clean the server is to remove and recreate the Docker volumes:
     ```bash
     # Stop the server
     ../manage-fhir-server.sh stop

     # Remove the PostgreSQL volume
     docker volume rm fhir-server_postgres-data

     # Start the server with a clean database
     ../manage-fhir-server.sh start
     ```
   - **NOT RECOMMENDED**: The `clean_server.py` script has limited functionality and may not work with all FHIR server configurations. It should only be used for testing purposes and not in production environments.

2. **Choose the right loading method**:
   | Method | When to Use | Pros | Cons |
   |--------|-------------|------|------|
   | **Transaction Bundles** | Large datasets, performance-critical scenarios | Faster, simpler | Requires server configuration for referential integrity |
   | **Selective Loading** | Default server configuration, reference integrity critical | Preserves references, works with default settings | Slightly slower, more complex |

3. **Configure the server appropriately**:
   - For Transaction Bundles, configure the server for import mode (disable validation and referential integrity)
   - For Selective Loading, use the default server configuration

4. **Verify loaded resources**: Always verify that resources were loaded correctly by using the `--verify` flag with selective loader or checking resource counts after loading.

5. **Start with small datasets**: When testing new loading methods, start with small datasets like `data/sample_fhir/bulk-export` before moving to larger datasets like MIMIC.

6. **Restore default configuration**: After using import mode for bulk loading, restore the default server configuration for normal operation.

7. **Monitor performance**:
   - Use the `--export-performance` flag with `load_all_bundles.py` to collect detailed performance metrics
   - Performance reports are saved to the `performance_reports` directory in JSON format
   - These reports include CPU usage, memory consumption, disk I/O, network I/O, and processing times
   - Use these metrics to optimize your loading process and hardware configuration
   - For detailed analysis, see the [Performance Metrics Guide](../../../docs/guides/fhir/data-loading/performance-metrics.md)

## Detailed Documentation

For more detailed instructions, see the documentation in the `docs/guides/fhir/data-loading/` directory:

- [Quick Reference](../../../docs/guides/fhir/data-loading/quick-reference.md) - Concise overview and comparison of loading methods
- [Direct Transaction Tutorial](../../../docs/guides/fhir/data-loading/direct-transaction-tutorial.md) - Step-by-step guide for high-performance bulk loading
- [Selective Loading Guide](../../../docs/guides/fhir/data-loading/selective-loading.md) - Guide for reference-preserving loading with default server configuration
- [Performance Metrics Guide](../../../docs/guides/fhir/data-loading/performance-metrics.md) - Guide for monitoring, analyzing, and optimizing loading performance
- [FHIR Server Documentation](../../../docs/guides/fhir/server/00-index.md) - Comprehensive guide for the HAPI FHIR server

These guides provide comprehensive information on how to use the scripts in this directory to load FHIR data efficiently while maintaining data integrity.
