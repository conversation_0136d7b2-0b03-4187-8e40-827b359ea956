{"test_id": "20250514_133420", "timestamp": "20250514_133420", "system_info": {"timestamp": "2025-05-14T13:34:20.357777", "test_id": "20250514_133420", "platform": "<PERSON>", "platform_release": "24.5.0", "platform_version": "Darwin Kernel Version 24.5.0: <PERSON><PERSON> Apr 22 19:52:00 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6031", "architecture": "arm64", "processor": "arm", "python_version": "3.11.11", "hostname": "laptop-944a4son.home", "cpu_count_logical": 14, "cpu_count_physical": 14, "total_memory_gb": 36.0, "available_memory_gb": 7.87}, "overall_stats": {"total_bundles": 1, "total_bundles_succeeded": 0, "total_bundles_failed": 1, "total_resources": 16, "total_resources_succeeded": 0, "total_resources_failed": 16, "total_processing_time": 0.054463863372802734, "elapsed_time": 0.1571669578552246, "resources_per_second": 101.80256854458243, "bundles_per_second": 6.362660534036402, "peak_cpu_percent": 38.0, "peak_memory_percent": 78.1, "peak_memory_usage_gb": 12.48}, "resource_type_stats": {"Device": {"bundles_processed": 1, "bundles_succeeded": 0, "bundles_failed": 1, "resources_processed": 16, "resources_succeeded": 0, "resources_failed": 16, "processing_time": 0.054463863372802734, "failed_bundles": [{"file_path": "../../data/generated_bundles/Device/Device_bundle.json", "resource_type": "<PERSON><PERSON>", "resource_count": 16, "error_message": "HAPI-0550: HAPI-0825: Error flushing transaction with resource types: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>] - The operation has failed with a client-assigned ID constraint failure. This typically means that multiple client threads are trying to create a new resource with the same client-assigned ID at the same time, and this thread was chosen to be rejected. It can also happen when a request disables the Upsert Existence Check."}]}}, "resource_usage_samples": [{"timestamp": 1747222460.364425, "elapsed_time": 9.5367431640625e-07, "bundles_processed": 0, "resources_processed": 0, "cpu_percent": 38.0, "memory_percent": 78.1, "memory_used_gb": 12.48, "net_sent_mb": 0.0, "net_recv_mb": 0.0, "disk_read_mb": 6.25, "disk_write_mb": 0.0}]}