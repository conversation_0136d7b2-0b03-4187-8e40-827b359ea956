{"test_id": "20250514_133215", "timestamp": "20250514_133215", "system_info": {"timestamp": "2025-05-14T13:32:15.262518", "test_id": "20250514_133215", "platform": "<PERSON>", "platform_release": "24.5.0", "platform_version": "Darwin Kernel Version 24.5.0: <PERSON><PERSON> Apr 22 19:52:00 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6031", "architecture": "arm64", "processor": "arm", "python_version": "3.11.11", "hostname": "laptop-944a4son.home", "cpu_count_logical": 14, "cpu_count_physical": 14, "total_memory_gb": 36.0, "available_memory_gb": 8.83}, "overall_stats": {"total_bundles": 1, "total_bundles_succeeded": 0, "total_bundles_failed": 1, "total_resources": 11, "total_resources_succeeded": 0, "total_resources_failed": 11, "total_processing_time": 0.060797929763793945, "elapsed_time": 0.16654396057128906, "resources_per_second": 66.0486274150509, "bundles_per_second": 6.004420674095537, "peak_cpu_percent": 21.6, "peak_memory_percent": 75.5, "peak_memory_usage_gb": 13.13}, "resource_type_stats": {"AllergyIntolerance": {"bundles_processed": 1, "bundles_succeeded": 0, "bundles_failed": 1, "resources_processed": 11, "resources_succeeded": 0, "resources_failed": 11, "processing_time": 0.060797929763793945, "failed_bundles": [{"file_path": "../../data/generated_bundles/AllergyIntolerance/AllergyIntolerance_bundle.json", "resource_type": "AllergyIntolerance", "resource_count": 11, "error_message": "HAPI-0550: HAPI-0825: Error flushing transaction with resource types: [AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance, AllergyIntolerance] - The operation has failed with a client-assigned ID constraint failure. This typically means that multiple client threads are trying to create a new resource with the same client-assigned ID at the same time, and this thread was chosen to be rejected. It can also happen when a request disables the Upsert Existence Check."}]}}, "resource_usage_samples": [{"timestamp": 1747222335.266387, "elapsed_time": 9.5367431640625e-07, "bundles_processed": 0, "resources_processed": 0, "cpu_percent": 21.6, "memory_percent": 75.5, "memory_used_gb": 13.13, "net_sent_mb": 0.0, "net_recv_mb": 0.0, "disk_read_mb": 0.0, "disk_write_mb": 0.0}]}