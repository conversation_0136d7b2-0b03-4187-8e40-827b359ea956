{"test_id": "20250514_134211", "timestamp": "20250514_134211", "system_info": {"timestamp": "2025-05-14T13:42:11.587783", "test_id": "20250514_134211", "platform": "<PERSON>", "platform_release": "24.5.0", "platform_version": "Darwin Kernel Version 24.5.0: <PERSON><PERSON> Apr 22 19:52:00 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T6031", "architecture": "arm64", "processor": "arm", "python_version": "3.11.11", "hostname": "laptop-944a4son.home", "cpu_count_logical": 14, "cpu_count_physical": 14, "total_memory_gb": 36.0, "available_memory_gb": 8.02}, "overall_stats": {"total_bundles": 1, "total_bundles_succeeded": 1, "total_bundles_failed": 0, "total_resources": 11, "total_resources_succeeded": 11, "total_resources_failed": 0, "total_processing_time": 0.12902402877807617, "elapsed_time": 0.23501086235046387, "resources_per_second": 46.80634711937726, "bundles_per_second": 4.255122465397933, "peak_cpu_percent": 31.6, "peak_memory_percent": 77.7, "peak_memory_usage_gb": 12.75}, "resource_type_stats": {"AllergyIntolerance": {"bundles_processed": 1, "bundles_succeeded": 1, "bundles_failed": 0, "resources_processed": 11, "resources_succeeded": 11, "resources_failed": 0, "processing_time": 0.12902402877807617, "failed_bundles": []}}, "resource_usage_samples": [{"timestamp": 1747222931.591437, "elapsed_time": 9.5367431640625e-07, "bundles_processed": 0, "resources_processed": 0, "cpu_percent": 31.6, "memory_percent": 77.7, "memory_used_gb": 12.75, "net_sent_mb": 0.0, "net_recv_mb": 0.0, "disk_read_mb": 3.04, "disk_write_mb": 0.0}]}