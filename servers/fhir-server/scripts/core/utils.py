#!/usr/bin/env python3
"""
Utility functions for FHIR server scripts.

This module provides common utility functions used by all FHIR server scripts,
including file I/O operations, JSON handling, and resource processing.
"""

import json
import os
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Import logger from logging module
from core.logging import logger

def read_ndjson_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Read an NDJSON file and return a list of parsed JSON objects.

    Args:
        file_path: Path to the NDJSON file

    Returns:
        List of parsed JSON objects
    """
    resources = []
    try:
        with open(file_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    resource = json.loads(line)
                    resources.append(resource)
                except json.JSONDecodeError as e:
                    logger.warning(f"Error parsing JSON at line {line_num}: {e}")

        logger.info(f"Read {len(resources)} resources from {file_path}")
        return resources
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return []

def save_bundle_to_file(bundle: Dict[str, Any], output_path: str) -> bool:
    """
    Save a FHIR Bundle to a JSON file.

    Args:
        bundle: FHIR Bundle as a dictionary
        output_path: Path where the bundle should be saved

    Returns:
        True if successful, False otherwise
    """
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        with open(output_path, 'w') as f:
            json.dump(bundle, f, indent=2)
        logger.info(f"Saved bundle with {len(bundle.get('entry', []))} entries to {output_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving bundle to {output_path}: {e}")
        return False

def get_resource_files(data_dir: str) -> Dict[str, str]:
    """
    Get all NDJSON files in the specified directory.

    Args:
        data_dir: Directory containing NDJSON files

    Returns:
        Dictionary mapping resource types to file paths
    """
    resource_files = {}
    try:
        for filename in os.listdir(data_dir):
            if filename.endswith(".ndjson"):
                # Extract resource type from filename (remove .ndjson extension and any numeric suffix)
                resource_type = filename.split(".")[0]
                resource_files[resource_type] = os.path.join(data_dir, filename)
        logger.info(f"Found {len(resource_files)} NDJSON files in {data_dir}")
        return resource_files
    except Exception as e:
        logger.error(f"Error reading directory {data_dir}: {e}")
        return {}

def find_files(directory: str, pattern: str = "*.json") -> List[str]:
    """
    Find all files in a directory matching a pattern.

    Args:
        directory: Directory to search
        pattern: File pattern to match (default: *.json)

    Returns:
        List of file paths
    """
    try:
        path = Path(directory)
        files = list(path.glob(f"**/{pattern}"))
        files.sort()  # Sort for consistent processing order
        return [str(f) for f in files]
    except Exception as e:
        logger.error(f"Error finding files in {directory}: {e}")
        return []

def check_server_availability(server_url: str) -> bool:
    """
    Check if the FHIR server is available.

    Args:
        server_url: URL of the FHIR server

    Returns:
        True if the server is available, False otherwise
    """
    try:
        import requests
        response = requests.get(f"{server_url}/metadata", timeout=10)
        return response.status_code == 200
    except Exception as e:
        logger.error(f"Error checking server availability: {e}")
        return False
