#!/usr/bin/env python3
"""
Logging configuration for FHIR server scripts.

This module provides a consistent logging configuration used by all FHIR server scripts.
"""

import logging
import sys
from typing import Optional

# Default logging format
DEFAULT_FORMAT = "%(asctime)s - %(levelname)s - %(message)s"
DEFAULT_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

def configure_logging(
    name: Optional[str] = None,
    level: int = logging.INFO,
    format_str: str = DEFAULT_FORMAT,
    date_format: str = DEFAULT_DATE_FORMAT
) -> logging.Logger:
    """
    Configure and return a logger with the specified settings.
    
    Args:
        name: Name of the logger (default: None, returns root logger)
        level: Logging level (default: INFO)
        format_str: Log message format (default: timestamp - level - message)
        date_format: Date format for timestamps (default: YYYY-MM-DD HH:MM:SS)
        
    Returns:
        Configured logger
    """
    # Configure root logger
    logging.basicConfig(
        level=level,
        format=format_str,
        datefmt=date_format,
        stream=sys.stdout
    )
    
    # Get and return the specified logger
    return logging.getLogger(name)

# Create a default logger for import
logger = configure_logging(__name__)
