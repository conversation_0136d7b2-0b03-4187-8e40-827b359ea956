#!/usr/bin/env python3
"""
Configuration settings for FHIR server scripts.

This module provides configuration settings used by all FHIR server scripts,
including default paths, server URLs, and resource processing order.

Path Handling Logic:
-------------------
1. BASE_DIR:
   - Defined as the absolute path to the project root directory
   - Calculated by going up 4 levels from this file's location
   - If this file is at: servers/fhir-server/scripts/core/config.py
   - Then BASE_DIR is: /path/to/project/servers

2. Default Directories:
   - DEFAULT_DATA_DIR: BASE_DIR/data/sample_fhir/bulk-export
   - DEFAULT_OUTPUT_DIR: BASE_DIR/data/generated_bundles
   - These are ABSOLUTE paths, not relative paths

3. Important Considerations:
   - When scripts are run from different directories, these absolute paths remain the same
   - When using relative paths as input (e.g., ../../data/sample_fhir), scripts should
     maintain the same relative structure for output paths
   - Scripts should handle both absolute and relative paths appropriately

4. Best Practices:
   - For command-line tools, prefer relative paths when possible for portability
   - When processing relative input paths, generate relative output paths with the same structure
   - When processing absolute input paths, use the default absolute output paths

Example:
If a script is run from servers/fhir-server with:
  --input-dir ../../data/sample_fhir/bulk-export
The output should be:
  ../../data/generated_bundles/bulk-export-bundles
NOT:
  /absolute/path/to/servers/data/generated_bundles/bulk-export-bundles
"""

import os
from pathlib import Path

# Base directory of the project
BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent

# Default data directory
DEFAULT_DATA_DIR = os.path.join(BASE_DIR, "data", "sample_fhir", "bulk-export")

# Default FHIR server URL
DEFAULT_SERVER_URL = "http://localhost:8080/fhir"

# Default bundle size (number of resources per bundle)
DEFAULT_BUNDLE_SIZE = 500

# Default output directory for generated bundles
DEFAULT_OUTPUT_DIR = os.path.join(BASE_DIR, "data", "generated_bundles")

# Resource processing order (base resources first, then those with references)
RESOURCE_ORDER = [
    "Location",
    "Organization",
    "Practitioner",
    "Patient",
    "Encounter",
    "Condition",
    "Observation",
    "Procedure",
    "MedicationRequest",
    "DiagnosticReport",
    "DocumentReference",
    "AllergyIntolerance",
    "Immunization",
    "Device",
]

# Create output directory if it doesn't exist
os.makedirs(DEFAULT_OUTPUT_DIR, exist_ok=True)
