#!/usr/bin/env python3
"""
Script to convert FHIR resources in NDJSON format to transaction bundles.

This script:
1. Reads resources from NDJSON files (single file or directory)
2. Creates FHIR Bundles of type "transaction"
3. Adds each resource as an entry with method "PUT" to preserve original IDs
4. Saves the bundles to JSON files or outputs them to stdout

Usage:
    # Process a single file
    python ndjson_to_bundle.py \
        --input-file data/sample_fhir/bulk-export/Patient.ndjson \
        --output-file data/generated_bundles/Patient_bundle.json

    # Process an entire directory with automatic output directory generation
    python ndjson_to_bundle.py \
        --input-dir data/sample_fhir/bulk-export

    # This will automatically create output in: data/generated_bundles/bulk-export-bundles/

    # Process an entire directory with explicit output directory
    python ndjson_to_bundle.py \
        --input-dir data/sample_fhir/bulk-export \
        --output-dir data/generated_bundles/custom_output

References:
- FHIR Bundle Resource: https://www.hl7.org/fhir/bundle.html
- FHIR RESTful API: https://www.hl7.org/fhir/http.html#transaction
"""

import argparse
import json
import os
import sys
from typing import Dict, List, Any, Optional
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.utils import read_ndjson_file, save_bundle_to_file, get_resource_files
from core.logging import logger
import core.config as config


def generate_output_dir(input_dir: str) -> str:
    """
    Generate an output directory path based on the input directory name.

    This function handles both absolute and relative paths intelligently:
    - For relative paths (starting with "../"), it maintains the same relative structure
      in the output path, preserving the number of "../" components
    - For absolute paths, it uses the default output directory from config.py

    This approach ensures that:
    1. When using relative paths in command-line arguments, the output maintains
       the same relative structure, making it intuitive and consistent
    2. When using absolute paths, the output follows the centralized configuration

    Parameters
    ----------
    input_dir : str
        Path to the input directory

    Returns
    -------
    str
        Path to the generated output directory

    Examples
    --------
    >>> generate_output_dir("../../data/sample_fhir/bulk-export")
    "../../data/generated_bundles/bulk-export-bundles"

    >>> generate_output_dir("/absolute/path/to/data")
    "/configured/default/output/dir/data-bundles"
    """
    # Extract the last component of the input directory path
    input_dir_name = os.path.basename(os.path.normpath(input_dir))

    # Generate the output directory name with "-bundles" suffix
    output_dir_name = f"{input_dir_name}-bundles"

    # Get the correct base output directory
    # If we're using a relative path like "../../data/sample_fhir/bulk-export",
    # we want to use "../../data/generated_bundles" as the base
    if input_dir.startswith("../"):
        # Count the number of "../" in the input path
        parts = input_dir.split("/")
        up_levels = 0
        for part in parts:
            if part == "..":
                up_levels += 1
            else:
                break

        # Construct the base output directory with the same number of "../"
        base_output_dir = "/".join([".."] * up_levels) + "/data/generated_bundles"
        logger.info(f"Using relative base output directory: {base_output_dir}")
        return os.path.join(base_output_dir, output_dir_name)
    else:
        # Use the default output directory for absolute paths
        logger.info(f"Using default base output directory: {config.DEFAULT_OUTPUT_DIR}")
        return os.path.join(config.DEFAULT_OUTPUT_DIR, output_dir_name)


def create_transaction_bundle(resources: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Create a FHIR transaction bundle from a list of resources.

    Parameters
    ----------
    resources : List[Dict[str, Any]]
        List of FHIR resources

    Returns
    -------
    Dict[str, Any]
        FHIR Bundle of type "transaction"
    """
    entries = []

    for resource in resources:
        resource_type = resource.get("resourceType")
        resource_id = resource.get("id")

        if not resource_type or not resource_id:
            logger.warning(f"Resource missing resourceType or id: {resource}")
            continue

        # Create a bundle entry with PUT method to preserve the original ID
        entry = {
            "resource": resource,
            "request": {
                "method": "PUT",
                "url": f"{resource_type}/{resource_id}"
            }
        }

        entries.append(entry)

    # Create the bundle
    bundle = {
        "resourceType": "Bundle",
        "type": "transaction",
        "entry": entries
    }

    return bundle


def split_resources_into_batches(resources: List[Dict[str, Any]], batch_size: int) -> List[List[Dict[str, Any]]]:
    """
    Split a list of resources into batches of specified size.

    Parameters
    ----------
    resources : List[Dict[str, Any]]
        List of FHIR resources
    batch_size : int
        Maximum number of resources per batch

    Returns
    -------
    List[List[Dict[str, Any]]]
        List of resource batches
    """
    return [resources[i:i + batch_size] for i in range(0, len(resources), batch_size)]


def process_ndjson_file(
    input_file: str,
    output_dir: Optional[str] = None,
    output_file: Optional[str] = None,
    batch_size: int = config.DEFAULT_BUNDLE_SIZE
) -> bool:
    """
    Process an NDJSON file and convert it to one or more transaction bundles.

    Parameters
    ----------
    input_file : str
        Path to the input NDJSON file
    output_dir : Optional[str]
        Directory to save output files (will be created if it doesn't exist)
    output_file : Optional[str]
        Path to the output JSON file (optional)
    batch_size : int
        Maximum number of resources per bundle

    Returns
    -------
    bool
        True if successful, False otherwise
    """
    # Read resources from NDJSON file
    resources = read_ndjson_file(input_file)
    if not resources:
        return False

    # Get resource type from the first resource
    resource_type = resources[0].get("resourceType", "Unknown")

    # Determine output file path
    if output_file is None and output_dir is not None:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Create resource type subdirectory
        resource_dir = os.path.join(output_dir, resource_type)
        os.makedirs(resource_dir, exist_ok=True)

        output_file = os.path.join(resource_dir, f"{resource_type}_bundle.json")

    # Split resources into batches if needed
    if len(resources) > batch_size:
        logger.info(f"Splitting {len(resources)} resources into batches of {batch_size}")
        batches = split_resources_into_batches(resources, batch_size)

        success = True
        for i, batch in enumerate(batches):
            # Create a transaction bundle for this batch
            bundle = create_transaction_bundle(batch)

            # Determine output file name for this batch
            if output_file:
                batch_output_file = output_file.replace(".json", f"_{i+1}.json")
            else:
                batch_output_file = None

            # Save or output the bundle
            if batch_output_file:
                success = success and save_bundle_to_file(bundle, batch_output_file)
            else:
                print(json.dumps(bundle, indent=2))

        return success
    else:
        # Create a single transaction bundle
        bundle = create_transaction_bundle(resources)

        # Save or output the bundle
        if output_file:
            return save_bundle_to_file(bundle, output_file)
        else:
            print(json.dumps(bundle, indent=2))
            return True


def process_directory(
    input_dir: str,
    output_dir: str,
    batch_size: int = config.DEFAULT_BUNDLE_SIZE
) -> bool:
    """
    Process all NDJSON files in a directory and convert them to transaction bundles.

    Parameters
    ----------
    input_dir : str
        Directory containing NDJSON files
    output_dir : str
        Directory to save output files
    batch_size : int
        Maximum number of resources per bundle

    Returns
    -------
    bool
        True if all files were processed successfully, False otherwise
    """
    # Get all NDJSON files in the directory
    resource_files = get_resource_files(input_dir)

    if not resource_files:
        logger.error(f"No NDJSON files found in {input_dir}")
        return False

    # Process each file
    success = True
    for resource_type, file_path in resource_files.items():
        logger.info(f"Processing {resource_type} resources from {file_path}")

        # Skip log files
        if resource_type.lower() == "log":
            logger.info(f"Skipping log file: {file_path}")
            continue

        # Process the file
        file_success = process_ndjson_file(
            input_file=file_path,
            output_dir=output_dir,
            batch_size=batch_size
        )

        if not file_success:
            logger.error(f"Failed to process {file_path}")
            success = False

    return success


def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments.

    Returns
    -------
    argparse.Namespace
        Parsed command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Convert FHIR resources in NDJSON format to transaction bundles."
    )

    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "--input-file",
        help="Path to the input NDJSON file"
    )
    input_group.add_argument(
        "--input-dir",
        help="Directory containing NDJSON files to process"
    )

    parser.add_argument(
        "--output-file",
        help="Path to the output JSON file (only used with --input-file)"
    )
    parser.add_argument(
        "--output-dir",
        help=f"Directory to save output files. If not specified and --input-dir is used, "
             f"automatically generates a directory based on the input directory name with '-bundles' suffix "
             f"in {config.DEFAULT_OUTPUT_DIR}"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=config.DEFAULT_BUNDLE_SIZE,
        help=f"Maximum number of resources per bundle (default: {config.DEFAULT_BUNDLE_SIZE})"
    )

    return parser.parse_args()


def main() -> int:
    """
    Main function.

    Returns
    -------
    int
        Exit code (0 for success, 1 for failure)
    """
    args = parse_args()

    try:
        if args.input_file:
            # Process a single file
            # If no output file or directory is specified, use the default output directory
            output_dir = args.output_dir if args.output_dir is not None else config.DEFAULT_OUTPUT_DIR

            success = process_ndjson_file(
                args.input_file,
                output_dir=output_dir if not args.output_file else None,
                output_file=args.output_file,
                batch_size=args.batch_size
            )
        else:
            # Process a directory
            # If no output directory is specified, generate one based on the input directory name
            if args.output_dir is None:
                output_dir = generate_output_dir(args.input_dir)
                logger.info(f"Auto-generating output directory: {output_dir}")
            else:
                output_dir = args.output_dir

            success = process_directory(
                args.input_dir,
                output_dir,
                args.batch_size
            )

        if success:
            logger.info("Bundle creation completed successfully")
            return 0
        else:
            logger.error("Bundle creation failed")
            return 1

    except Exception as e:
        logger.exception(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
