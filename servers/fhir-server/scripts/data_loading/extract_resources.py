#!/usr/bin/env python3
"""
Extract resources from transaction bundles and save them as NDJSON files.

This script:
1. Reads transaction bundles from a directory
2. Extracts resources from the bundles
3. Saves the resources as NDJSON files

Usage:
    python extract_resources.py --bundle-dir <dir> --output-dir <dir>
"""

import argparse
import json
import os
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logging import logger
import core.config as config

def extract_resources_from_bundle(bundle_file, output_dir):
    """
    Extract resources from a transaction bundle and save them as NDJSON files.
    
    Args:
        bundle_file: Path to the bundle file
        output_dir: Directory to save the extracted resources
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Read the bundle
    with open(bundle_file, 'r') as f:
        bundle = json.load(f)
    
    # Extract resources by type
    resources_by_type = {}
    for entry in bundle.get('entry', []):
        resource = entry.get('resource')
        if not resource:
            continue
        
        resource_type = resource.get('resourceType')
        if not resource_type:
            continue
        
        if resource_type not in resources_by_type:
            resources_by_type[resource_type] = []
        
        resources_by_type[resource_type].append(resource)
    
    # Save resources as NDJSON files
    for resource_type, resources in resources_by_type.items():
        output_file = os.path.join(output_dir, f"{resource_type}.ndjson")
        
        # Append to existing file if it exists
        mode = 'a' if os.path.exists(output_file) else 'w'
        
        with open(output_file, mode) as f:
            for resource in resources:
                f.write(json.dumps(resource) + '\n')
        
        logger.info(f"Saved {len(resources)} {resource_type} resources to {output_file}")

def process_bundle_directory(bundle_dir, output_dir):
    """
    Process all bundle files in a directory.
    
    Args:
        bundle_dir: Directory containing bundle files
        output_dir: Directory to save the extracted resources
    """
    # Find all bundle files
    bundle_files = []
    for root, _, files in os.walk(bundle_dir):
        for file in files:
            if file.endswith('.json'):
                bundle_files.append(os.path.join(root, file))
    
    logger.info(f"Found {len(bundle_files)} bundle files in {bundle_dir}")
    
    # Process each bundle file
    for bundle_file in bundle_files:
        logger.info(f"Processing {bundle_file}")
        extract_resources_from_bundle(bundle_file, output_dir)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Extract resources from transaction bundles and save them as NDJSON files."
    )
    parser.add_argument(
        "--bundle-dir",
        required=True,
        help="Directory containing transaction bundles"
    )
    parser.add_argument(
        "--output-dir",
        required=True,
        help="Directory to save the extracted resources"
    )
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    # Process bundle directory
    process_bundle_directory(args.bundle_dir, args.output_dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
