#!/usr/bin/env python3
"""
Statistics tracking and reporting for FHIR bundle processing with performance metrics.

This module provides classes and functions for tracking and reporting comprehensive statistics
about FHIR bundle processing operations, including:
- Resource type breakdown (counts by resource type)
- Success/failure rates (overall and by resource type)
- Processing times (total, average, by resource type)
- Detailed error reporting (with specific error messages)
- System resource usage (CPU, memory, disk I/O, network I/O)
- Performance metrics (throughput, resource utilization)
- Hardware configuration information (CPU cores, memory, platform)

The primary class, BundleProcessingStats, provides methods to:
1. Track statistics during bundle processing
2. Sample system resource usage at regular intervals
3. Generate formatted summary reports
4. Export detailed performance data to JSON files for analysis

These utilities are designed to support performance testing and optimization of FHIR data
loading processes. They can be used to evaluate the impact of hardware resources on loading time,
analyze the time vs. cost relationship for different configurations, identify performance
bottlenecks, and optimize for scalability with increasing data volumes.

Example usage:
    ```python
    # Initialize statistics tracking
    stats = BundleProcessingStats(test_id="performance_test_1")
    stats.start_processing()

    # Process bundles and record statistics
    for file_path in bundle_files:
        bundle = load_bundle_from_file(file_path)
        success, error_message = send_bundle_to_server(bundle, server_url)
        stats.record_bundle_processing(file_path, bundle, success, processing_time, error_message)
        stats.sample_resource_usage(i, stats.total_resources)

    # Mark the end of processing
    stats.end_processing()

    # Generate and display the summary report
    summary_report = stats.generate_summary_report()
    print(summary_report)

    # Export performance data for analysis
    stats.export_performance_data("./performance_reports")
    ```
"""

import os
import time
import json
import sys
from typing import Dict, Any, List, Optional
from pathlib import Path
from collections import defaultdict
from datetime import timedelta, datetime

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

# Import system resource monitoring modules with fallbacks
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import platform
    PLATFORM_INFO_AVAILABLE = True
except ImportError:
    PLATFORM_INFO_AVAILABLE = False

from core.logging import logger
import core.config as config


class BundleProcessingStats:
    """Class to track and report bundle processing statistics."""

    def __init__(self, test_id: Optional[str] = None):
        """
        Initialize statistics tracking.

        Args:
            test_id: Optional identifier for this test run
        """
        # Track by resource type
        self.resource_stats = defaultdict(lambda: {
            'bundles_processed': 0,
            'bundles_succeeded': 0,
            'bundles_failed': 0,
            'resources_processed': 0,
            'resources_succeeded': 0,
            'resources_failed': 0,
            'processing_time': 0.0,
            'failed_bundles': []  # List to track failed bundles
        })

        # Overall stats
        self.total_bundles = 0
        self.total_bundles_succeeded = 0
        self.total_bundles_failed = 0
        self.total_resources = 0
        self.total_resources_succeeded = 0
        self.total_resources_failed = 0
        self.total_processing_time = 0.0
        self.start_time = None
        self.end_time = None

        # Track all failures with details
        self.failures = []  # List to track all failures with details

        # New attributes for performance metrics
        self.test_id = test_id or datetime.now().strftime("%Y%m%d_%H%M%S")
        self.system_info = self._collect_system_info()
        self.resource_usage_samples = []
        self.sampling_interval = 5  # seconds between samples
        self.last_sample_time = 0

        # Peak usage metrics
        self.peak_memory_usage = 0.0
        self.peak_memory_percent = 0.0
        self.peak_cpu_percent = 0.0

        # Network and disk metrics
        self.initial_net_io = self._get_net_io()
        self.initial_disk_io = self._get_disk_io()

    def start_processing(self):
        """Mark the start of processing."""
        self.start_time = time.time()
        # Take initial resource sample
        self.sample_resource_usage(0, 0)

    def end_processing(self):
        """Mark the end of processing."""
        self.end_time = time.time()
        # Take final resource sample
        self.sample_resource_usage(self.total_bundles, self.total_resources)

    def _collect_system_info(self) -> Dict[str, Any]:
        """
        Collect information about the system where the script is running.

        Returns:
            Dictionary with system information
        """
        system_info = {
            'timestamp': datetime.now().isoformat(),
            'test_id': self.test_id
        }

        # Add platform information if available
        if PLATFORM_INFO_AVAILABLE:
            try:
                system_info.update({
                    'platform': platform.system(),
                    'platform_release': platform.release(),
                    'platform_version': platform.version(),
                    'architecture': platform.machine(),
                    'processor': platform.processor(),
                    'python_version': platform.python_version(),
                    'hostname': platform.node()
                })
            except Exception as e:
                logger.warning(f"Error collecting platform info: {e}")

        # Add psutil information if available
        if PSUTIL_AVAILABLE:
            try:
                system_info.update({
                    'cpu_count_logical': psutil.cpu_count(logical=True),
                    'cpu_count_physical': psutil.cpu_count(logical=False),
                    'total_memory_gb': round(psutil.virtual_memory().total / (1024**3), 2),
                    'available_memory_gb': round(psutil.virtual_memory().available / (1024**3), 2)
                })
            except Exception as e:
                logger.warning(f"Error collecting psutil info: {e}")

        return system_info

    def _get_net_io(self) -> Optional[Dict[str, int]]:
        """
        Get current network I/O counters.

        Returns:
            Dictionary with network I/O information or None if not available
        """
        if PSUTIL_AVAILABLE:
            try:
                net_io = psutil.net_io_counters()
                return {
                    'bytes_sent': net_io.bytes_sent,
                    'bytes_recv': net_io.bytes_recv
                }
            except Exception as e:
                logger.debug(f"Error getting network I/O: {e}")
        return None

    def _get_disk_io(self) -> Optional[Dict[str, int]]:
        """
        Get current disk I/O counters.

        Returns:
            Dictionary with disk I/O information or None if not available
        """
        if PSUTIL_AVAILABLE:
            try:
                disk_io = psutil.disk_io_counters()
                return {
                    'read_bytes': disk_io.read_bytes,
                    'write_bytes': disk_io.write_bytes
                }
            except Exception as e:
                logger.debug(f"Error getting disk I/O: {e}")
        return None

    def sample_resource_usage(self, bundle_count: Optional[int] = None, resource_count: Optional[int] = None):
        """
        Sample system resource usage.

        Args:
            bundle_count: Current number of bundles processed
            resource_count: Current number of resources processed
        """
        current_time = time.time()

        # Limit sampling frequency
        if current_time - self.last_sample_time < self.sampling_interval:
            return

        self.last_sample_time = current_time

        # Create sample with timestamp and progress info
        sample = {
            'timestamp': current_time,
            'elapsed_time': current_time - self.start_time if self.start_time else 0,
            'bundles_processed': bundle_count,
            'resources_processed': resource_count
        }

        # Add CPU and memory metrics if available
        if PSUTIL_AVAILABLE:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=0.1)
                sample['cpu_percent'] = cpu_percent
                self.peak_cpu_percent = max(self.peak_cpu_percent, cpu_percent)

                # Memory usage
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                memory_used_gb = round(memory.used / (1024**3), 2)

                sample['memory_percent'] = memory_percent
                sample['memory_used_gb'] = memory_used_gb

                self.peak_memory_percent = max(self.peak_memory_percent, memory_percent)
                self.peak_memory_usage = max(self.peak_memory_usage, memory_used_gb)

                # Network I/O
                current_net_io = self._get_net_io()
                if current_net_io and self.initial_net_io:
                    sample['net_sent_mb'] = round((current_net_io['bytes_sent'] - self.initial_net_io['bytes_sent']) / (1024**2), 2)
                    sample['net_recv_mb'] = round((current_net_io['bytes_recv'] - self.initial_net_io['bytes_recv']) / (1024**2), 2)

                # Disk I/O
                current_disk_io = self._get_disk_io()
                if current_disk_io and self.initial_disk_io:
                    sample['disk_read_mb'] = round((current_disk_io['read_bytes'] - self.initial_disk_io['read_bytes']) / (1024**2), 2)
                    sample['disk_write_mb'] = round((current_disk_io['write_bytes'] - self.initial_disk_io['write_bytes']) / (1024**2), 2)

            except Exception as e:
                logger.debug(f"Error sampling resource usage: {e}")

        # Add sample to collection
        self.resource_usage_samples.append(sample)

    def extract_resource_type(self, file_path: str) -> str:
        """
        Extract the resource type from a file path.

        Args:
            file_path: Path to the bundle file

        Returns:
            Resource type or 'Unknown' if not determinable
        """
        # Try to extract from directory structure first (e.g., .../Patient/Patient_bundle.json)
        path_parts = Path(file_path).parts
        for part in reversed(path_parts):
            if part in config.RESOURCE_ORDER:
                return part

        # Try to extract from filename (e.g., Patient_bundle.json)
        filename = Path(file_path).name
        for resource_type in config.RESOURCE_ORDER:
            if filename.startswith(resource_type + "_"):
                return resource_type

        # Default to Unknown
        return "Unknown"

    def record_bundle_processing(self, file_path: str, bundle: Dict[str, Any], success: bool, processing_time: float, error_message: str = None):
        """
        Record statistics for a processed bundle.

        Args:
            file_path: Path to the bundle file
            bundle: The bundle that was processed
            success: Whether processing was successful
            processing_time: Time taken to process the bundle
            error_message: Error message if processing failed
        """
        resource_type = self.extract_resource_type(file_path)
        entry_count = len(bundle.get("entry", []))

        # Update resource type stats
        stats = self.resource_stats[resource_type]
        stats['bundles_processed'] += 1
        stats['resources_processed'] += entry_count
        stats['processing_time'] += processing_time

        if success:
            stats['bundles_succeeded'] += 1
            stats['resources_succeeded'] += entry_count
        else:
            stats['bundles_failed'] += 1
            stats['resources_failed'] += entry_count

            # Record failure details
            failure_info = {
                'file_path': file_path,
                'resource_type': resource_type,
                'resource_count': entry_count,
                'error_message': error_message or "Unknown error"
            }

            # Add to resource type specific failures
            stats['failed_bundles'].append(failure_info)

            # Add to overall failures list
            self.failures.append(failure_info)

        # Update overall stats
        self.total_bundles += 1
        self.total_resources += entry_count
        self.total_processing_time += processing_time

        if success:
            self.total_bundles_succeeded += 1
            self.total_resources_succeeded += entry_count
        else:
            self.total_bundles_failed += 1
            self.total_resources_failed += entry_count

    def generate_summary_report(self) -> str:
        """
        Generate a formatted summary report.

        Returns:
            Formatted summary report as a string
        """
        if not self.start_time or not self.end_time:
            return "No processing data available."

        total_elapsed = self.end_time - self.start_time

        # Create the report
        report = []
        report.append("\n" + "="*80)
        report.append("                       FHIR BUNDLE LOADING SUMMARY REPORT")
        report.append("="*80)

        # Resource type breakdown
        report.append("\nRESOURCE TYPE BREAKDOWN:")
        report.append("-"*80)
        report.append(f"{'Resource Type':<20} {'Bundles':<20} {'Resources':<20} {'Time':<15} {'Success Rate':<10}")
        report.append("-"*80)

        # Sort resource types by number of resources processed (descending)
        sorted_types = sorted(
            self.resource_stats.items(),
            key=lambda x: x[1]['resources_processed'],
            reverse=True
        )

        for resource_type, stats in sorted_types:
            bundles_info = f"{stats['bundles_succeeded']}/{stats['bundles_processed']}"
            resources_info = f"{stats['resources_succeeded']}/{stats['resources_processed']}"
            time_info = f"{stats['processing_time']:.2f}s"

            if stats['resources_processed'] > 0:
                success_rate = (stats['resources_succeeded'] / stats['resources_processed']) * 100
                success_info = f"{success_rate:.1f}%"
            else:
                success_info = "N/A"

            report.append(f"{resource_type:<20} {bundles_info:<20} {resources_info:<20} {time_info:<15} {success_info:<10}")

        # Overall statistics
        report.append("\nOVERALL STATISTICS:")
        report.append("-"*80)

        # Calculate overall success rate
        if self.total_resources > 0:
            overall_success_rate = (self.total_resources_succeeded / self.total_resources) * 100
        else:
            overall_success_rate = 0

        # Calculate average processing time per bundle
        if self.total_bundles > 0:
            avg_time_per_bundle = self.total_processing_time / self.total_bundles
        else:
            avg_time_per_bundle = 0

        # Format timedelta for better readability
        elapsed_td = timedelta(seconds=total_elapsed)
        hours, remainder = divmod(elapsed_td.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        elapsed_formatted = f"{hours:02}:{minutes:02}:{seconds:02}"

        report.append(f"Total Processing Time:      {elapsed_formatted} (HH:MM:SS)")
        report.append(f"Average Time Per Bundle:    {avg_time_per_bundle:.2f} seconds")
        report.append(f"Total Bundles Processed:    {self.total_bundles_succeeded}/{self.total_bundles} ({(self.total_bundles_succeeded/self.total_bundles*100 if self.total_bundles else 0):.1f}%)")
        report.append(f"Total Resources Processed:  {self.total_resources_succeeded}/{self.total_resources} ({overall_success_rate:.1f}%)")

        # Add performance metrics section
        report.append("\nPERFORMANCE METRICS:")
        report.append("-"*80)

        # Calculate throughput metrics
        resources_per_second = self.total_resources / total_elapsed if total_elapsed > 0 else 0
        bundles_per_second = self.total_bundles / total_elapsed if total_elapsed > 0 else 0

        report.append(f"Resources Per Second:       {resources_per_second:.2f}")
        report.append(f"Bundles Per Second:         {bundles_per_second:.2f}")
        report.append(f"Average Resource Size:      {(self.total_resources / self.total_bundles if self.total_bundles else 0):.2f} resources/bundle")

        # Add system resource usage if available
        if PSUTIL_AVAILABLE and self.resource_usage_samples:
            report.append("\nSYSTEM RESOURCE USAGE:")
            report.append("-"*80)

            # System information
            if 'cpu_count_logical' in self.system_info:
                report.append(f"CPU Cores:                  {self.system_info.get('cpu_count_physical', 'N/A')} physical, {self.system_info.get('cpu_count_logical', 'N/A')} logical")

            if 'total_memory_gb' in self.system_info:
                report.append(f"Total System Memory:        {self.system_info.get('total_memory_gb', 'N/A')} GB")

            # Peak usage
            report.append(f"Peak CPU Usage:             {self.peak_cpu_percent:.1f}%")
            report.append(f"Peak Memory Usage:          {self.peak_memory_usage:.2f} GB ({self.peak_memory_percent:.1f}%)")

            # Calculate averages from samples
            if len(self.resource_usage_samples) > 1:
                avg_cpu = sum(s.get('cpu_percent', 0) for s in self.resource_usage_samples) / len(self.resource_usage_samples)
                avg_memory = sum(s.get('memory_used_gb', 0) for s in self.resource_usage_samples) / len(self.resource_usage_samples)

                report.append(f"Average CPU Usage:          {avg_cpu:.1f}%")
                report.append(f"Average Memory Usage:       {avg_memory:.2f} GB")

        # Top resource types
        report.append("\nTOP RESOURCE TYPES BY VOLUME:")
        report.append("-"*80)

        for i, (resource_type, stats) in enumerate(sorted_types[:5], 1):
            if stats['resources_processed'] > 0:
                report.append(f"{i}. {resource_type}: {stats['resources_processed']} resources ({(stats['resources_processed']/self.total_resources*100 if self.total_resources else 0):.1f}% of total)")

        # Failed bundles section
        if self.failures:
            report.append("\nFAILED BUNDLES:")
            report.append("-"*80)
            report.append(f"{'Resource Type':<15} {'File':<50} {'Resources':<10} {'Error'}")
            report.append("-"*80)

            # Group failures by resource type
            for resource_type, stats in sorted_types:
                if stats['bundles_failed'] > 0:
                    for failure in stats['failed_bundles']:
                        # Get just the filename without the full path for cleaner display
                        filename = os.path.basename(failure['file_path'])
                        # Truncate error message if too long
                        error_msg = failure['error_message']
                        if len(error_msg) > 50:
                            error_msg = error_msg[:47] + "..."

                        report.append(f"{resource_type:<15} {filename:<50} {failure['resource_count']:<10} {error_msg}")

        report.append("="*80)

        return "\n".join(report)

    def export_performance_data(self, output_dir: str = "./performance_reports") -> str:
        """
        Export performance data to a JSON file for later analysis.

        Args:
            output_dir: Directory where to save the performance data

        Returns:
            Path to the created JSON file
        """
        # Create directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Create a timestamp for the filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create the data structure
        performance_data = {
            'test_id': self.test_id,
            'timestamp': timestamp,
            'system_info': self.system_info,
            'overall_stats': {
                'total_bundles': self.total_bundles,
                'total_bundles_succeeded': self.total_bundles_succeeded,
                'total_bundles_failed': self.total_bundles_failed,
                'total_resources': self.total_resources,
                'total_resources_succeeded': self.total_resources_succeeded,
                'total_resources_failed': self.total_resources_failed,
                'total_processing_time': self.total_processing_time,
                'elapsed_time': self.end_time - self.start_time if self.start_time and self.end_time else 0,
                'resources_per_second': self.total_resources / (self.end_time - self.start_time) if self.start_time and self.end_time and self.end_time > self.start_time else 0,
                'bundles_per_second': self.total_bundles / (self.end_time - self.start_time) if self.start_time and self.end_time and self.end_time > self.start_time else 0,
                'peak_cpu_percent': self.peak_cpu_percent,
                'peak_memory_percent': self.peak_memory_percent,
                'peak_memory_usage_gb': self.peak_memory_usage
            },
            'resource_type_stats': {k: v for k, v in self.resource_stats.items() if k != 'failed_bundles'},
            'resource_usage_samples': self.resource_usage_samples
        }

        # Create filename with test_id if available
        filename = f"performance_data_{self.test_id}_{timestamp}.json"
        file_path = os.path.join(output_dir, filename)

        # Write to file
        try:
            with open(file_path, 'w') as f:
                json.dump(performance_data, f, indent=2)
            logger.info(f"Performance data exported to {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Error exporting performance data: {e}")
            return ""
