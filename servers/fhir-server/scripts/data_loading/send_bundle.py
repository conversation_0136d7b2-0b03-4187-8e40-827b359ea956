#!/usr/bin/env python3
"""
<PERSON>ript to send a FHIR transaction bundle to a HAPI FHIR server.

This script:
1. Reads a FHIR Bundle from a JSON file
2. Sends the bundle to a FHIR server using a POST request
3. Processes and displays the server's response

References:
- FHIR Bundle Resource: https://www.hl7.org/fhir/bundle.html
- FHIR RESTful API: https://www.hl7.org/fhir/http.html#transaction
- HAPI FHIR Server: https://hapifhir.io/
"""

import argparse
import json
import sys
import time
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

import requests
from requests.exceptions import RequestException

from core.logging import logger
import core.config as config

def load_bundle_from_file(file_path: str) -> Optional[Dict[str, Any]]:
    """
    Load a FHIR Bundle from a JSON file.

    Args:
        file_path: Path to the JSON file

    Returns:
        FHIR Bundle as a dictionary, or None if loading fails
    """
    try:
        with open(file_path, 'r') as f:
            bundle = json.load(f)

        # Verify that this is a FHIR Bundle
        if bundle.get("resourceType") != "Bundle" or bundle.get("type") != "transaction":
            logger.error(f"File {file_path} does not contain a valid FHIR transaction bundle")
            return None

        logger.info(f"Loaded bundle with {len(bundle.get('entry', []))} entries from {file_path}")
        return bundle
    except Exception as e:
        logger.error(f"Error loading bundle from {file_path}: {e}")
        return None

def send_bundle_to_server(bundle: Dict[str, Any], server_url: str, max_retries: int = 3) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Send a FHIR Bundle to a FHIR server.

    Args:
        bundle: FHIR Bundle as a dictionary
        server_url: URL of the FHIR server
        max_retries: Maximum number of retry attempts

    Returns:
        Tuple of (success flag, response data)
    """
    headers = {
        "Content-Type": "application/fhir+json",
        "Accept": "application/fhir+json"
    }

    entry_count = len(bundle.get("entry", []))
    logger.info(f"Sending bundle with {entry_count} entries to {server_url}")

    for attempt in range(1, max_retries + 1):
        try:
            response = requests.post(
                server_url,
                headers=headers,
                json=bundle,
                timeout=120  # Longer timeout for large bundles
            )

            if response.status_code in (200, 201):
                logger.info(f"Bundle processed successfully (status code: {response.status_code})")
                return True, response.json()
            else:
                logger.error(f"Error processing bundle: {response.status_code} - {response.text}")

                # If this is not the last attempt, retry
                if attempt < max_retries:
                    wait_time = 2 ** attempt  # Exponential backoff
                    logger.info(f"Retrying in {wait_time} seconds (attempt {attempt}/{max_retries})...")
                    time.sleep(wait_time)
                else:
                    return False, None

        except RequestException as e:
            logger.error(f"Request error: {e}")

            # If this is not the last attempt, retry
            if attempt < max_retries:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.info(f"Retrying in {wait_time} seconds (attempt {attempt}/{max_retries})...")
                time.sleep(wait_time)
            else:
                return False, None

    return False, None

def analyze_response(response_data: Dict[str, Any]) -> None:
    """
    Analyze and display the server's response to a transaction bundle.

    Args:
        response_data: Response data from the server
    """
    if not response_data or response_data.get("resourceType") != "Bundle":
        logger.warning("Invalid response data")
        return

    entries = response_data.get("entry", [])
    success_count = 0
    error_count = 0

    for entry in entries:
        response = entry.get("response", {})
        status = response.get("status", "")

        if status.startswith("2"):  # 2xx status codes indicate success
            success_count += 1
        else:
            error_count += 1
            logger.warning(f"Error in entry: {status} - {response.get('outcome', {})}")

    logger.info(f"Response summary: {success_count} successes, {error_count} errors")

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Send a FHIR transaction bundle to a HAPI FHIR server."
    )
    parser.add_argument(
        "--input-file",
        required=True,
        help="Path to the input JSON file containing a FHIR Bundle"
    )
    parser.add_argument(
        "--server-url",
        default=config.DEFAULT_SERVER_URL,
        help=f"URL of the FHIR server (default: {config.DEFAULT_SERVER_URL})"
    )
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Load the bundle from file
    bundle = load_bundle_from_file(args.input_file)
    if not bundle:
        return 1

    # Send the bundle to the server
    success, response_data = send_bundle_to_server(bundle, args.server_url)

    # Analyze the response
    if success and response_data:
        analyze_response(response_data)
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
