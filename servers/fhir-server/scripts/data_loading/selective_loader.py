#!/usr/bin/env python3
"""
Script to selectively load FHIR resources with complete references.

This script:
1. Analyzes references between FHIR resources
2. Identifies resources with complete references (all referenced resources exist)
3. Determines the optimal loading order based on dependencies
4. Converts resources to transaction bundles
5. Loads the bundles in the determined order
6. Verifies that resources were loaded correctly

This implements the "Selective Approach" to FHIR data loading, which focuses on
loading only resources with complete references without creating synthetic resources.

References:
- FHIR References: https://www.hl7.org/fhir/references.html
- FHIR RESTful API: https://www.hl7.org/fhir/http.html
- FHIR Transactions: https://www.hl7.org/fhir/http.html#transaction
"""

import argparse
import json
import os
import sys
import time
from typing import Dict, List, Any
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from core.logging import logger
import core.config as config
from data_loading.ndjson_to_bundle import create_transaction_bundle, split_resources_into_batches
from data_loading.send_bundle import send_bundle_to_server
from data_validation.verify_loaded_resources import verify_resources_from_list

def save_resources_to_bundles(
    resources: List[Dict[str, Any]],
    output_dir: str,
    resource_type: str,
    batch_size: int
) -> List[str]:
    """
    Save resources to transaction bundles.

    Args:
        resources: List of FHIR resources
        output_dir: Directory to save bundles
        resource_type: Type of resources
        batch_size: Maximum number of resources per bundle

    Returns:
        List of paths to the created bundle files
    """
    # Create output directory for this resource type
    resource_output_dir = os.path.join(output_dir, resource_type)
    os.makedirs(resource_output_dir, exist_ok=True)

    # Split resources into batches if needed
    if len(resources) > batch_size:
        batches = split_resources_into_batches(resources, batch_size)

        bundle_files = []
        for i, batch in enumerate(batches):
            # Create a transaction bundle for this batch
            bundle = create_transaction_bundle(batch)

            # Save the bundle
            bundle_file = os.path.join(resource_output_dir, f"{resource_type}_bundle_{i+1}.json")
            with open(bundle_file, 'w') as f:
                json.dump(bundle, f, indent=2)

            bundle_files.append(bundle_file)
            logger.info(f"Saved bundle with {len(batch)} entries to {bundle_file}")

        return bundle_files
    else:
        # Create a single transaction bundle
        bundle = create_transaction_bundle(resources)

        # Save the bundle
        bundle_file = os.path.join(resource_output_dir, f"{resource_type}_bundle.json")
        with open(bundle_file, 'w') as f:
            json.dump(bundle, f, indent=2)

        logger.info(f"Saved bundle with {len(resources)} entries to {bundle_file}")
        return [bundle_file]

def load_bundles(bundle_files: List[str], server_url: str) -> bool:
    """
    Load bundles to the FHIR server.

    Args:
        bundle_files: List of paths to bundle files
        server_url: URL of the FHIR server

    Returns:
        True if all bundles were loaded successfully, False otherwise
    """
    success = True

    for bundle_file in bundle_files:
        logger.info(f"Loading bundle {bundle_file}")

        # Load the bundle
        with open(bundle_file, 'r') as f:
            bundle = json.load(f)

        # Send the bundle to the server
        send_success, _ = send_bundle_to_server(bundle, server_url)

        if not send_success:
            logger.error(f"Failed to load bundle {bundle_file}")
            success = False

    return success

def process_resources_selectively(
    data_dir: str,
    output_dir: str,
    server_url: str,
    batch_size: int,
    verify: bool
) -> bool:
    """
    Process FHIR resources selectively based on reference completeness.

    This implementation uses an incremental approach:
    1. Start with resources that have no external dependencies (like Patient)
    2. After loading each resource type, check which additional resources can now be loaded
    3. Continue until no more resources can be loaded

    Args:
        data_dir: Directory containing NDJSON files
        output_dir: Directory to save generated bundles
        server_url: URL of the FHIR server
        batch_size: Maximum number of resources per bundle
        verify: Whether to verify loaded resources

    Returns:
        True if successful, False otherwise
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Initialize the reference analyzer
    from data_loading.reference_analyzer import ReferenceAnalyzer
    analyzer = ReferenceAnalyzer(data_dir)

    logger.info(f"Scanning resources in {data_dir}")
    analyzer.scan_resources()

    logger.info(f"Extracting references")
    analyzer.extract_references()

    # Track loaded resources by type and ID
    loaded_resources_by_type = {}
    loaded_resource_ids = {resource_type: set() for resource_type in analyzer.available_resources.keys()}

    # Get initial loading order
    loading_order = analyzer.determine_loading_order()

    # Process resources in order
    success = True
    resources_loaded_this_round = True

    while resources_loaded_this_round:
        resources_loaded_this_round = False

        for resource_type in loading_order:
            # Skip if we've already processed all resources of this type
            if resource_type in loaded_resources_by_type and len(loaded_resources_by_type[resource_type]) == len(analyzer.available_resources[resource_type]):
                continue

            logger.info(f"\nChecking {resource_type} resources")

            # Get all resources of this type
            resources_to_check = []
            for file_path in [analyzer.resource_files.get(resource_type)]:
                if file_path:
                    from core.utils import read_ndjson_file
                    resources = read_ndjson_file(file_path)
                    resources_to_check.extend(resources)

            # Filter out resources that have already been loaded
            resources_to_check = [r for r in resources_to_check if r.get("id") not in loaded_resource_ids[resource_type]]

            if not resources_to_check:
                continue

            # Check which resources have all their references satisfied
            loadable_resources = []
            for resource in resources_to_check:
                resource_id = resource.get("id")
                if not resource_id:
                    continue

                # Extract references from this resource
                references = []
                resource_json = json.dumps(resource)
                import re
                ref_pattern = r'"reference"\s*:\s*"([^/]+)/([^"]+)"'
                for match in re.finditer(ref_pattern, resource_json):
                    ref_type, ref_id = match.groups()
                    references.append((ref_type, ref_id))

                # Check if all referenced resources have been loaded
                all_refs_satisfied = True
                for ref_type, ref_id in references:
                    if ref_id not in loaded_resource_ids.get(ref_type, set()):
                        all_refs_satisfied = False
                        break

                if all_refs_satisfied:
                    loadable_resources.append(resource)

            if not loadable_resources:
                logger.info(f"No loadable {resource_type} resources found at this time")
                continue

            logger.info(f"Loading {len(loadable_resources)} {resource_type} resources")

            # Save resources to bundles
            bundle_files = save_resources_to_bundles(loadable_resources, output_dir, resource_type, batch_size)

            # Load bundles
            load_success = load_bundles(bundle_files, server_url)
            if not load_success:
                logger.error(f"Failed to load {resource_type} resources")
                success = False
                continue

            # Update loaded resources tracking
            if resource_type not in loaded_resources_by_type:
                loaded_resources_by_type[resource_type] = []
            loaded_resources_by_type[resource_type].extend(loadable_resources)

            for resource in loadable_resources:
                resource_id = resource.get("id")
                if resource_id:
                    loaded_resource_ids[resource_type].add(resource_id)

            resources_loaded_this_round = True

            # Verify loaded resources if requested
            if verify:
                logger.info(f"Verifying loaded resources for {resource_type}")
                verify_success = verify_resources_from_list(loadable_resources, server_url, sample_size=min(10, len(loadable_resources)))

                if not verify_success:
                    logger.warning(f"Verification for {resource_type} failed")

        if not resources_loaded_this_round:
            logger.info("No more resources can be loaded with current dependencies")
            break

    # Log summary
    total_loaded = sum(len(resources) for resources in loaded_resources_by_type.values())
    total_available = sum(len(ids) for ids in analyzer.available_resources.values())

    logger.info(f"\nLoading summary:")
    logger.info(f"  - Resource types loaded: {len(loaded_resources_by_type)}")
    logger.info(f"  - Total resources loaded: {total_loaded} out of {total_available} ({total_loaded/total_available*100:.1f}%)")

    for resource_type, resources in loaded_resources_by_type.items():
        available = len(analyzer.available_resources.get(resource_type, []))
        logger.info(f"  - {resource_type}: {len(resources)}/{available} resources ({len(resources)/available*100:.1f}%)")

    return success

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Selectively load FHIR resources with complete references."
    )
    parser.add_argument(
        "--data-dir",
        default=config.DEFAULT_DATA_DIR,
        help=f"Directory containing NDJSON files (default: {config.DEFAULT_DATA_DIR})"
    )
    parser.add_argument(
        "--output-dir",
        default=config.DEFAULT_OUTPUT_DIR,
        help=f"Directory to save generated bundles (default: {config.DEFAULT_OUTPUT_DIR})"
    )
    parser.add_argument(
        "--server-url",
        default=config.DEFAULT_SERVER_URL,
        help=f"URL of the FHIR server (default: {config.DEFAULT_SERVER_URL})"
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=config.DEFAULT_BUNDLE_SIZE,
        help=f"Maximum number of resources per bundle (default: {config.DEFAULT_BUNDLE_SIZE})"
    )
    parser.add_argument(
        "--verify",
        action="store_true",
        help="Verify that resources were loaded correctly"
    )
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Process resources selectively
    start_time = time.time()
    success = process_resources_selectively(
        args.data_dir,
        args.output_dir,
        args.server_url,
        args.batch_size,
        args.verify
    )
    end_time = time.time()

    # Display summary
    elapsed_time = end_time - start_time
    if success:
        logger.info(f"\nProcessing completed successfully in {elapsed_time:.1f} seconds")
        return 0
    else:
        logger.error(f"\nProcessing completed with errors in {elapsed_time:.1f} seconds")
        return 1

if __name__ == "__main__":
    sys.exit(main())
