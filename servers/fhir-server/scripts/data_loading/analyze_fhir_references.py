#!/usr/bin/env python3
"""
FHIR Reference Analyzer

This script analyzes references between FHIR resources in NDJSON files and
provides detailed information about identifiers, references, and dependencies.
It can also generate a loading plan based on the analysis.

The script has three main modes of operation:
1. Basic analysis (default): Analyzes resources and displays results in console
2. Plan generation (--plan): Generates a loading plan based on dependencies
3. Output saving (--save-output): Saves analysis and plan to files in the data directory

References:
- FHIR Resource Identification: https://build.fhir.org/resource.html#identification
- FHIR References: https://build.fhir.org/references.html
- FHIR Bulk Data Format: https://hl7.org/fhir/uv/bulkdata/
"""

import argparse
import json
import os
import re
import datetime
import sys
from collections import defaultdict, Counter
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

def parse_args():
    """
    Parse command line arguments.

    Returns
    -------
    argparse.Namespace
        Parsed command line arguments containing data directory, verbosity,
        sample size, output options, and plan generation flag.
    """
    parser = argparse.ArgumentParser(
        description="Analyze references between FHIR resources in NDJSON files."
    )
    parser.add_argument(
        "--data-dir",
        default="../../data/sample_fhir/bulk-export",
        help="Directory containing NDJSON files (default: ../../data/sample_fhir/bulk-export)",
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Show detailed information",
    )
    parser.add_argument(
        "--sample",
        type=int,
        default=5,
        help="Number of examples to show for each type (default: 5)",
    )
    parser.add_argument(
        "--save-output",
        action="store_true",
        help="Save analysis output to temporary files",
    )
    parser.add_argument(
        "--plan",
        action="store_true",
        help="Generate a loading plan based on the analysis",
    )
    return parser.parse_args()

def extract_references(resource_json):
    """
    Extract references from a FHIR resource.

    This function identifies different types of references in a FHIR resource:
    - Direct references (ResourceType/id)
    - Conditional references (ResourceType?identifier=system|value)
    - Absolute references (http://server/fhir/ResourceType/id)
    - Logical references (urn:uuid:id)

    Parameters
    ----------
    resource_json : str
        JSON string representation of a FHIR resource

    Returns
    -------
    list of tuple
        List of tuples containing (resource_type, resource_id, reference_type)
        where reference_type is one of: direct, conditional, absolute, logical
    """
    references = []

    # Look for direct references in the format "reference":"ResourceType/id"
    ref_pattern = r'"reference"\s*:\s*"([^/]+)/([^"]+)"'
    for match in re.finditer(ref_pattern, resource_json):
        resource_type, resource_id = match.groups()
        references.append((resource_type, resource_id, "direct"))

    # Look for conditional references in the format "reference":"ResourceType?identifier=system|value"
    cond_ref_pattern = r'"reference"\s*:\s*"([^?]+)\?identifier=([^|]+)\|([^"]+)"'
    for match in re.finditer(cond_ref_pattern, resource_json):
        resource_type, system, value = match.groups()
        references.append((resource_type, f"{system}|{value}", "conditional"))

    # Look for absolute references in the format "reference":"http://server/fhir/ResourceType/id"
    abs_ref_pattern = r'"reference"\s*:\s*"http[^"]+/([^/]+)/([^"]+)"'
    for match in re.finditer(abs_ref_pattern, resource_json):
        resource_type, resource_id = match.groups()
        references.append((resource_type, resource_id, "absolute"))

    # Look for logical references in the format "reference":"urn:uuid:id"
    logical_ref_pattern = r'"reference"\s*:\s*"urn:uuid:([^"]+)"'
    for match in re.finditer(logical_ref_pattern, resource_json):
        resource_id = match.group(1)
        references.append(("Unknown", resource_id, "logical"))

    return references

def analyze_fhir_data(data_dir, verbose=False, sample_size=5, output_file=None):
    """
    Analyze FHIR data in the specified directory.

    This function analyzes NDJSON files containing FHIR resources to identify:
    - Resource types and counts
    - Unique identifiers
    - References between resources
    - Missing references
    - Resources with complete and incomplete references
    - Optimal loading order

    Parameters
    ----------
    data_dir : str
        Directory containing NDJSON files with FHIR resources
    verbose : bool, optional
        Whether to show detailed information (default: False)
    sample_size : int, optional
        Number of examples to show for each type (default: 5)
    output_file : str, optional
        Path to save the analysis report (default: None, no file is saved)

    Returns
    -------
    dict or None
        Dictionary containing analysis results, or None if analysis failed
    """
    output = []

    def print_and_log(message):
        print(message)
        if output_file:
            output.append(message)

    print_and_log(f"Analyzing FHIR data in: {data_dir}")

    # Verify that the directory exists
    if not os.path.isdir(data_dir):
        print_and_log(f"Error: Directory {data_dir} does not exist.")
        return None

    # Find all NDJSON files
    ndjson_files = [f for f in os.listdir(data_dir) if f.endswith('.ndjson')]
    if not ndjson_files:
        print_and_log(f"Error: No NDJSON files found in {data_dir}.")
        return None

    print_and_log(f"Found {len(ndjson_files)} NDJSON files.")

    # Dictionaries to store information
    resources_by_type = defaultdict(list)
    resource_ids_by_type = defaultdict(set)
    references_by_type = defaultdict(list)
    reference_types_count = Counter()

    # Dictionaries for detailed analysis
    resource_references = {}  # {resource_type/id: [references]}
    resources_with_missing_refs = defaultdict(list)  # {resource_type: [ids]}
    resources_fully_referenced = defaultdict(list)  # {resource_type: [ids]}

    # Analyze each file
    for filename in ndjson_files:
        resource_type = filename.split('.')[0]
        file_path = os.path.join(data_dir, filename)

        print_and_log(f"Analyzing {filename}...")

        try:
            with open(file_path, 'r') as f:
                line_count = 0
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    line_count += 1

                    try:
                        resource = json.loads(line)
                        resource_id = resource.get('id')

                        if resource_id:
                            resources_by_type[resource_type].append(resource)
                            resource_ids_by_type[resource_type].add(resource_id)

                            # Extract references for this specific resource
                            resource_key = f"{resource_type}/{resource_id}"
                            refs = extract_references(line)
                            resource_references[resource_key] = refs

                            # Extract references for general analysis
                            for ref_type, ref_id, ref_method in refs:
                                references_by_type[resource_type].append((ref_type, ref_id, ref_method))
                                reference_types_count[ref_method] += 1

                    except json.JSONDecodeError:
                        print_and_log(f"  Error: Invalid line in {filename}:{line_count}")

                print_and_log(f"  Processed {line_count} resources of type {resource_type}.")

        except Exception as e:
            print_and_log(f"  Error processing {filename}: {e}")

    # Show summary
    print_and_log("\n=== Resource Summary ===")
    for resource_type, resources in resources_by_type.items():
        print_and_log(f"{resource_type}: {len(resources)} resources")

    # Show identifier examples
    print_and_log("\n=== Identifier Examples ===")
    for resource_type, ids in resource_ids_by_type.items():
        id_list = list(ids)
        print_and_log(f"{resource_type}: {len(ids)} unique identifiers")
        if verbose:
            for i, resource_id in enumerate(id_list[:sample_size]):
                print_and_log(f"  {i+1}. {resource_id}")

    # Show reference types
    print_and_log("\n=== Reference Types ===")
    for ref_method, count in reference_types_count.items():
        print_and_log(f"{ref_method}: {count} references")

    # Analyze missing references
    print_and_log("\n=== Reference Analysis ===")
    missing_references = defaultdict(set)

    # Analyze each resource to verify its references
    for resource_key, refs in resource_references.items():
        resource_type, resource_id = resource_key.split('/', 1)
        has_missing_refs = False

        for ref_type, ref_id, ref_method in refs:
            if ref_method == "direct":
                if ref_id not in resource_ids_by_type.get(ref_type, set()):
                    missing_references[ref_type].add(ref_id)
                    has_missing_refs = True

        # Classify the resource based on whether it has missing references
        if has_missing_refs:
            resources_with_missing_refs[resource_type].append(resource_id)
        else:
            resources_fully_referenced[resource_type].append(resource_id)

    # Show missing references
    print_and_log("\n=== Missing References ===")
    total_missing = 0
    for ref_type, ref_ids in missing_references.items():
        print_and_log(f"{ref_type}: {len(ref_ids)} missing references")
        total_missing += len(ref_ids)
        if verbose and ref_ids:
            for i, ref_id in enumerate(list(ref_ids)[:sample_size]):
                print_and_log(f"  {i+1}. {ref_id}")

    print_and_log(f"\nTotal missing references: {total_missing}")

    # Show resources with missing vs. complete references
    print_and_log("\n=== Resources with Missing References ===")
    total_with_missing = 0
    for resource_type, resource_ids in resources_with_missing_refs.items():
        count = len(resource_ids)
        total_with_missing += count
        print_and_log(f"{resource_type}: {count} resources with missing references")
        if verbose and count > 0:
            for i, resource_id in enumerate(resource_ids[:sample_size]):
                print_and_log(f"  {i+1}. {resource_id}")

    print_and_log(f"\nTotal resources with missing references: {total_with_missing}")

    print_and_log("\n=== Resources with Complete References ===")
    total_fully_referenced = 0
    for resource_type, resource_ids in resources_fully_referenced.items():
        count = len(resource_ids)
        total_fully_referenced += count
        print_and_log(f"{resource_type}: {count} resources with complete references")

    print_and_log(f"\nTotal resources with complete references: {total_fully_referenced}")

    # Generate loading recommendations
    print_and_log("\n=== Loading Recommendations ===")

    # Determine optimal loading order
    dependency_graph = {}
    for resource_type, refs in references_by_type.items():
        dependency_graph[resource_type] = set()
        for ref_type, _, _ in refs:
            dependency_graph[resource_type].add(ref_type)

    # Sort resource types by dependencies
    ordered_types = []
    visited = set()

    def visit(resource_type):
        if resource_type in visited:
            return
        visited.add(resource_type)
        for dependency in dependency_graph.get(resource_type, []):
            if dependency in dependency_graph:
                visit(dependency)
        ordered_types.append(resource_type)

    for resource_type in dependency_graph:
        visit(resource_type)

    print_and_log("Recommended loading order:")
    for i, resource_type in enumerate(ordered_types):
        print_and_log(f"  {i+1}. {resource_type}")

    # Recommend loading strategy
    print_and_log("\nRecommended strategy:")
    print_and_log("  1. Load resources with complete references first")
    print_and_log("  2. Handle resources with incomplete references based on your strategy")
    print_and_log("  3. Verify references after loading")

    # Print statistics on resources with complete vs incomplete references
    print_and_log("\nReference completeness statistics:")
    for resource_type in ordered_types:
        complete = len(resources_fully_referenced.get(resource_type, []))
        incomplete = len(resources_with_missing_refs.get(resource_type, []))
        total = complete + incomplete
        if total > 0:
            complete_pct = (complete / total) * 100
            print_and_log(f"  - {resource_type}: {complete}/{total} complete ({complete_pct:.1f}%)")

    # Show examples of resources with their references
    if verbose:
        print_and_log("\n=== Resource Reference Examples ===")
        for resource_type, resources in resources_by_type.items():
            print_and_log(f"\n{resource_type}:")
            for i, resource in enumerate(resources[:sample_size]):
                print_and_log(f"  {i+1}. ID: {resource.get('id')}")

                # Find references in this resource
                resource_json = json.dumps(resource)
                refs = extract_references(resource_json)

                if refs:
                    print_and_log("    References:")
                    for ref_type, ref_id, ref_method in refs:
                        print_and_log(f"      - {ref_type}/{ref_id} ({ref_method})")
                else:
                    print_and_log("    No references")

    # Save the report to a file if specified
    if output_file:
        try:
            with open(output_file, 'w') as f:
                for line in output:
                    f.write(line + '\n')
            print(f"Report saved to: {output_file}")
        except Exception as e:
            print(f"Error saving report: {e}")

    # Return useful information for selective loading
    return {
        "ordered_types": ordered_types,
        "resources_fully_referenced": resources_fully_referenced,
        "resources_with_missing_refs": resources_with_missing_refs,
        "missing_references": missing_references
    }



def generate_load_plan(analysis_result, output_file=None):
    """
    Generate a loading plan based on reference analysis.

    This function creates a Markdown document with a step-by-step plan for loading
    FHIR resources in the optimal order based on reference dependencies.

    Parameters
    ----------
    analysis_result : dict
        Dictionary containing analysis results from analyze_fhir_data()
    output_file : str, optional
        Path to save the loading plan (default: None, no file is saved)

    Returns
    -------
    list or None
        List of strings containing the loading plan, or None if generation failed
    """
    if not analysis_result:
        print("Error: No analysis results available to generate a plan.")
        return None

    ordered_types = analysis_result["ordered_types"]
    resources_fully_referenced = analysis_result["resources_fully_referenced"]
    resources_with_missing_refs = analysis_result["resources_with_missing_refs"]
    missing_references = analysis_result["missing_references"]

    plan = []
    plan.append("# FHIR Data Loading Plan")
    plan.append("# Automatically generated based on reference analysis")
    plan.append("")

    # Step 1: Create synthetic resources for missing references
    if missing_references:
        plan.append("## Step 1: Create Synthetic Resources")
        plan.append("")
        for ref_type, ref_ids in missing_references.items():
            plan.append(f"### {ref_type}: {len(ref_ids)} resources")
            for ref_id in list(ref_ids)[:5]:  # Show only a few examples
                plan.append(f"- {ref_id}")
            if len(ref_ids) > 5:
                plan.append(f"- ... and {len(ref_ids) - 5} more")
            plan.append("")

    # Step 2: Load resources in order
    plan.append("## Step 2: Load Resources in Order")
    plan.append("")
    for resource_type in ordered_types:
        fully_ref_count = len(resources_fully_referenced.get(resource_type, []))
        missing_ref_count = len(resources_with_missing_refs.get(resource_type, []))
        total_count = fully_ref_count + missing_ref_count

        if total_count > 0:
            plan.append(f"### {resource_type}: {total_count} resources")
            plan.append(f"- With complete references: {fully_ref_count}")
            plan.append(f"- With missing references: {missing_ref_count}")
            plan.append("")

    # Step 3: Verify references
    plan.append("## Step 3: Verify References")
    plan.append("")
    plan.append("After loading the data, verify that references are maintained:")
    plan.append("1. Select some patients at random")
    plan.append("2. Verify that their observations, conditions, etc. are correctly linked")
    plan.append("3. Check that reference-based queries work correctly")

    # Save the plan to a file if specified
    if output_file:
        try:
            with open(output_file, 'w') as f:
                for line in plan:
                    f.write(line + '\n')
            print(f"Loading plan saved to: {output_file}")
        except Exception as e:
            print(f"Error saving loading plan: {e}")

    return plan

def main():
    """
    Main function to execute the FHIR reference analysis.

    This function:
    1. Parses command line arguments
    2. Sets up output files if --save-output is specified
    3. Analyzes FHIR data in the specified directory
    4. Generates a loading plan if --plan is specified
    5. Saves output to files if --save-output is specified

    The script has three main modes of operation:
    - Basic analysis (default): Only displays results in console
    - Plan generation (--plan): Generates a loading plan
    - Output saving (--save-output): Saves results to files in the data directory
    """
    args = parse_args()

    # Determine if we should save output
    output_file = None
    plan_file = None

    # If save-output flag is set, use the data directory specified by --data-dir
    if args.save_output:
        output_dir = args.data_dir  # Use the data directory for output

        # It's assumed args.data_dir exists as it's the input directory.
        # If there's a concern it might not, an os.makedirs(output_dir, exist_ok=True) could be added.
        # For now, we assume it exists as it's the source of the data being analyzed.

        print(f"Using data directory for output: {output_dir}")

        # Generate output filename with timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f"fhir_analysis_{timestamp}.txt")
        print(f"Analysis will be saved to: {output_file}")

        # If plan is requested, set plan file path
        if args.plan:
            plan_file = os.path.join(output_dir, f"fhir_analysis_{timestamp}_plan.md")
            print(f"Loading plan will be saved to: {plan_file}")

    # Analyze FHIR data
    analysis_result = analyze_fhir_data(args.data_dir, args.verbose, args.sample, output_file)

    # If a plan was requested and analysis was successful, generate a loading plan
    if args.plan and analysis_result and args.save_output:
        generate_load_plan(analysis_result, plan_file)

if __name__ == "__main__":
    main()
