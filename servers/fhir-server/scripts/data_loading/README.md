# FHIR Data Loading Scripts

This directory contains scripts for loading FHIR data from NDJSON files into a HAPI FHIR server using two primary methods:

1. **Direct Transaction Bundles**: For bulk loading with permissive server configuration
2. **Selective Loading**: For incremental loading with reference integrity

## Loading Methods

### Method 1: Direct Transaction Bundles

Best for initial data loading with permissive server configuration:

1. Configure server to disable validation and referential integrity checks
2. Convert NDJSON files to transaction bundles with `ndjson_to_bundle.py`
3. Load all bundles at once with `load_all_bundles.py` or individually with `send_bundle.py`
4. Works with complex reference relationships when server is properly configured

### Method 2: Selective Loading

Best for maintaining reference integrity with default server configuration:

1. Analyze references between resources to determine loading order
2. Load only resources whose dependencies are satisfied
3. Process resources incrementally as dependencies become available
4. Preserves reference integrity but may load fewer resources

## Server Configuration

**Critical**: Server configuration dramatically affects data loading success:

- **Default Configuration**: Enforces validation and referential integrity
  - Only resources with satisfied references will load
  - Selective loader works best with this configuration

- **Import Mode Configuration**: Disables validation and referential integrity checks
  - All resources can be loaded regardless of references
  - Direct transaction bundles work best with this configuration

See the [Direct Transaction Tutorial](../../../docs/guides/fhir/data-loading/direct-transaction-tutorial.md) for details on configuring the server.

## Scripts

| Script | Description | Primary Use |
|--------|-------------|-------------|
| `ndjson_to_bundle.py` | Converts NDJSON files to transaction bundles | Both methods |
| `send_bundle.py` | Sends transaction bundles to the FHIR server | Both methods |
| `load_all_bundles.py` | Loads all transaction bundles from a directory with performance metrics | Method 1 |
| `selective_loader.py` | Implements selective loading with dependency resolution | Method 2 |
| `reference_analyzer.py` | Analyzes references between FHIR resources | Method 2 |
| `verify_loaded_resources.py` | Verifies that resources are loaded correctly | Both methods |
| `clean_server.py` | Cleans the FHIR server by removing all resources | Both methods |
| `utils.py` | Utility functions for working with FHIR data | Both methods |
| `stats.py` | Statistics tracking and performance metrics for bundle processing | Method 1 |

## Common Challenges

- **Reference Integrity**: Resources with unresolved references will be rejected with default configuration
- **Circular References**: Resources that reference each other require special handling
- **Performance**: Large bundles can cause timeouts; use appropriate batch sizes
- **Memory Usage**: Processing large NDJSON files can require significant memory

## Documentation

For detailed usage instructions, see:

- [Quick Reference](../../../docs/guides/fhir/data-loading/quick-reference.md) - Concise overview of both methods
- [Direct Transaction Tutorial](../../../docs/guides/fhir/data-loading/direct-transaction-tutorial.md) - Step-by-step guide for bulk loading
- [Selective Loading Guide](../../../docs/guides/fhir/data-loading/selective-loading.md) - Guide for reference-preserving loading
- [Performance Metrics Guide](../../../docs/guides/fhir/data-loading/performance-metrics.md) - Detailed guide for performance testing and optimization
