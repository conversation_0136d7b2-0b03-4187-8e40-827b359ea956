#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze references between FHIR resources and determine the optimal loading order.

This script:
1. Scans all NDJSON files in a directory to identify resources and their IDs
2. Extracts all references between resources
3. Builds a dependency graph based on these references
4. Determines which resources have complete references (all referenced resources exist)
5. Generates a loading plan that prioritizes resources with complete references

References:
- FHIR References: https://www.hl7.org/fhir/references.html
- FHIR RESTful API: https://www.hl7.org/fhir/http.html
"""

import argparse
import json
import os
import re
import sys
from collections import defaultdict
from typing import Dict, List, Set, Tuple, Any, Optional

from core.utils import read_ndjson_file, get_resource_files
from core.logging import logger
import core.config as config

class ReferenceAnalyzer:
    """
    Analyzes references between FHIR resources to determine loading order.

    This class implements the "Dependency Satisfaction Principle":
    A resource can be loaded if all its references point to resources that are already available.
    """

    def __init__(self, data_dir: str):
        """
        Initialize the ReferenceAnalyzer.

        Args:
            data_dir: Directory containing NDJSON files
        """
        self.data_dir = data_dir
        self.resource_files = get_resource_files(data_dir)

        # Maps resource type to a set of resource IDs
        self.available_resources: Dict[str, Set[str]] = defaultdict(set)

        # Maps (resource_type, resource_id) to a list of (referenced_type, referenced_id)
        self.resource_references: Dict[Tuple[str, str], List[Tuple[str, str]]] = {}

        # Maps resource type to a set of resource types it depends on
        self.type_dependencies: Dict[str, Set[str]] = defaultdict(set)

        # Resources with complete references (all referenced resources exist)
        self.resources_with_complete_refs: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

        # Resources with incomplete references (some referenced resources don't exist)
        self.resources_with_incomplete_refs: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

    def scan_resources(self) -> None:
        """
        Scan all NDJSON files to identify resources and their IDs.
        """
        for resource_type, file_path in self.resource_files.items():
            resources = read_ndjson_file(file_path)

            for resource in resources:
                resource_id = resource.get("id")
                if not resource_id:
                    continue

                self.available_resources[resource_type].add(resource_id)

        logger.info(f"Scanned {len(self.resource_files)} resource types")
        for resource_type, ids in self.available_resources.items():
            logger.info(f"  - {resource_type}: {len(ids)} resources")

    def extract_references(self) -> None:
        """
        Extract all references between resources.
        """
        for resource_type, file_path in self.resource_files.items():
            resources = read_ndjson_file(file_path)

            for resource in resources:
                resource_id = resource.get("id")
                if not resource_id:
                    continue

                # Extract references from the resource
                references = self._extract_resource_references(resource)

                # Store the references
                self.resource_references[(resource_type, resource_id)] = references

                # Update type dependencies
                for ref_type, _ in references:
                    if ref_type != resource_type:  # Avoid self-dependencies
                        self.type_dependencies[resource_type].add(ref_type)

        logger.info(f"Extracted references from {len(self.resource_references)} resources")

    def _extract_resource_references(self, resource: Dict[str, Any]) -> List[Tuple[str, str]]:
        """
        Extract references from a FHIR resource.

        Args:
            resource: FHIR resource as a dictionary

        Returns:
            List of (referenced_type, referenced_id) tuples
        """
        references = []
        resource_json = json.dumps(resource)

        # Look for direct references in the format "reference":"ResourceType/id"
        ref_pattern = r'"reference"\s*:\s*"([^/]+)/([^"]+)"'
        for match in re.finditer(ref_pattern, resource_json):
            resource_type, resource_id = match.groups()
            references.append((resource_type, resource_id))

        return references

    def classify_resources(self) -> None:
        """
        Classify resources based on reference completeness.

        A resource has complete references if all resources it references exist
        in the available resources.
        """
        for resource_type, file_path in self.resource_files.items():
            resources = read_ndjson_file(file_path)

            for resource in resources:
                resource_id = resource.get("id")
                if not resource_id:
                    continue

                # Get references for this resource
                references = self.resource_references.get((resource_type, resource_id), [])

                # Check if all referenced resources exist
                all_refs_exist = True
                for ref_type, ref_id in references:
                    if ref_id not in self.available_resources.get(ref_type, set()):
                        all_refs_exist = False
                        break

                # Classify the resource
                if all_refs_exist:
                    self.resources_with_complete_refs[resource_type].append(resource)
                else:
                    self.resources_with_incomplete_refs[resource_type].append(resource)

        # Log statistics
        total_complete = sum(len(resources) for resources in self.resources_with_complete_refs.values())
        total_incomplete = sum(len(resources) for resources in self.resources_with_incomplete_refs.values())
        total = total_complete + total_incomplete

        logger.info(f"Classified {total} resources:")
        logger.info(f"  - Complete references: {total_complete} ({total_complete/total*100:.1f}%)")
        logger.info(f"  - Incomplete references: {total_incomplete} ({total_incomplete/total*100:.1f}%)")

        for resource_type in sorted(self.available_resources.keys()):
            complete = len(self.resources_with_complete_refs.get(resource_type, []))
            incomplete = len(self.resources_with_incomplete_refs.get(resource_type, []))
            total_type = complete + incomplete

            if total_type > 0:
                logger.info(f"  - {resource_type}: {complete}/{total_type} complete ({complete/total_type*100:.1f}%)")

    def determine_loading_order(self) -> List[str]:
        """
        Determine the optimal loading order based on dependencies.

        Uses a topological sort algorithm to order resource types such that
        all dependencies of a resource type are loaded before the resource type itself.

        Returns:
            List of resource types in the optimal loading order
        """
        # Create a directed graph
        graph = {rt: self.type_dependencies.get(rt, set()) for rt in self.available_resources.keys()}

        # Calculate in-degree for each node (number of dependencies)
        in_degree = {node: 0 for node in graph}
        for node in graph:
            for neighbor in graph[node]:
                if neighbor in in_degree:
                    in_degree[neighbor] += 1

        # Start with nodes that have no dependencies (in-degree = 0)
        queue = [node for node in in_degree if in_degree[node] == 0]
        loading_order = []

        # Process nodes in order of dependency
        while queue:
            # Take a node with no dependencies
            node = queue.pop(0)
            loading_order.append(node)

            # For each node that depends on this one
            for node_with_deps in graph:
                if node in graph[node_with_deps]:
                    # Reduce its in-degree
                    in_degree[node_with_deps] -= 1
                    # If it has no more dependencies, add to queue
                    if in_degree[node_with_deps] == 0:
                        queue.append(node_with_deps)

        # Check if we processed all nodes (if not, there's a cycle)
        if len(loading_order) != len(graph):
            logger.warning("Circular dependency detected in resource types")
            # Add remaining nodes in any order
            for node in graph:
                if node not in loading_order:
                    loading_order.append(node)

        logger.info(f"Determined loading order: {', '.join(loading_order)}")
        return loading_order

    def generate_load_plan(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Generate a loading plan based on reference completeness.

        Returns:
            Dictionary mapping resource types to lists of resources to load
        """
        # Determine the loading order
        loading_order = self.determine_loading_order()

        # Create the loading plan
        load_plan = {}
        for resource_type in loading_order:
            # Include only resources with complete references
            resources = self.resources_with_complete_refs.get(resource_type, [])
            if resources:
                load_plan[resource_type] = resources

        # Log the loading plan
        logger.info(f"Generated loading plan:")
        for resource_type, resources in load_plan.items():
            logger.info(f"  - {resource_type}: {len(resources)} resources")

        return load_plan

def analyze_references(data_dir: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Analyze references between FHIR resources and generate a loading plan.

    Args:
        data_dir: Directory containing NDJSON files

    Returns:
        Dictionary mapping resource types to lists of resources to load
    """
    analyzer = ReferenceAnalyzer(data_dir)

    logger.info(f"Scanning resources in {data_dir}")
    analyzer.scan_resources()

    logger.info(f"Extracting references")
    analyzer.extract_references()

    logger.info(f"Classifying resources")
    analyzer.classify_resources()

    logger.info(f"Generating loading plan")
    load_plan = analyzer.generate_load_plan()

    return load_plan

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze references between FHIR resources and determine the optimal loading order."
    )
    parser.add_argument(
        "--data-dir",
        default=config.DEFAULT_DATA_DIR,
        help=f"Directory containing NDJSON files (default: {config.DEFAULT_DATA_DIR})"
    )
    parser.add_argument(
        "--output-file",
        help="Path to save the loading plan as JSON (optional)"
    )
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Analyze references and generate loading plan
    load_plan = analyze_references(args.data_dir)

    # Save the loading plan if requested
    if args.output_file:
        try:
            # Convert resources to dictionaries for JSON serialization
            serializable_plan = {}
            for resource_type, resources in load_plan.items():
                serializable_plan[resource_type] = resources

            with open(args.output_file, 'w') as f:
                json.dump(serializable_plan, f, indent=2)

            logger.info(f"Saved loading plan to {args.output_file}")
        except Exception as e:
            logger.error(f"Error saving loading plan: {e}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
