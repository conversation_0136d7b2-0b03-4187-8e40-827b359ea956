#!/usr/bin/env python3
"""
<PERSON>ript to load all FHIR transaction bundles from a directory to a FHIR server with performance metrics.

This script:
1. Finds all JSON files in a specified directory
2. Loads each file as a FHIR transaction bundle
3. Sends each bundle to the FHIR server
4. Provides a comprehensive summary report of the loading process
5. Tracks and reports detailed performance metrics (CPU, memory, disk I/O, network I/O)
6. Exports performance data to JSON files for analysis

The script is designed to support performance testing and optimization of FHIR data loading
processes. It can be used to evaluate the impact of hardware resources on loading time,
analyze the time vs. cost relationship for different configurations, identify performance
bottlenecks, and optimize for scalability with increasing data volumes.

Examples:
    # Basic usage - load all bundles from a directory
    python load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir

    # Export performance data for analysis
    python load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir --export-performance

    # Specify a test ID for comparing multiple runs
    python load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir --test-id "8cpu_16gb_test1" --export-performance

    # Adjust sampling interval for resource usage monitoring
    python load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir --sampling-interval 10

    # Specify custom output directory for performance reports
    python load_all_bundles.py --bundle-dir data/generated_bundles --server-url http://localhost:8080/fhir --export-performance --output-dir ./custom_reports

References:
- FHIR Bundle Resource: https://www.hl7.org/fhir/bundle.html
- FHIR RESTful API: https://www.hl7.org/fhir/http.html#transaction
"""

import argparse
import os
import sys
import time
import requests
from typing import List, Tuple, Optional
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

from data_loading.send_bundle import load_bundle_from_file
from core.logging import logger
from data_loading.stats import BundleProcessingStats
import core.config as config
from core.utils import find_files

def find_bundle_files(bundle_dir: str, pattern: str = "*.json") -> List[str]:
    """
    Find all bundle files in the specified directory matching the pattern.

    This function recursively searches the specified directory for files matching
    the given pattern. It returns a sorted list of absolute file paths to ensure
    consistent processing order across multiple runs.

    Args:
        bundle_dir: Directory to search for bundle files. Can be absolute or relative path.
                   Example: "data/generated_bundles" or "/path/to/bundles"
        pattern: File pattern to match using glob syntax (default: "*.json").
                 Examples: "*.json" for all JSON files, "Patient_*.json" for Patient bundles only

    Returns:
        List of absolute file paths to the matched files, sorted alphabetically.
        Example: ["/path/to/bundles/Patient_bundle.json", "/path/to/bundles/Observation_bundle.json"]

    Raises:
        FileNotFoundError: If the specified directory does not exist
        PermissionError: If the specified directory cannot be accessed due to permissions
    """
    bundle_files = find_files(bundle_dir, pattern)
    if bundle_files:
        logger.info(f"Found {len(bundle_files)} bundle files in {bundle_dir} (including subdirectories)")
    return bundle_files




def load_and_send_bundles(
    bundle_files: List[str],
    server_url: str,
    test_id: Optional[str] = None,
    sampling_interval: int = 5
) -> Tuple[int, int, BundleProcessingStats]:
    """
    Load and send each bundle file to the FHIR server with performance monitoring.

    This function processes each bundle file in the provided list, sends it to the FHIR server
    as a transaction bundle, and tracks detailed performance metrics during the process.
    It collects system resource usage (CPU, memory, disk I/O, network I/O) at regular intervals
    specified by the sampling_interval parameter.

    The function handles errors gracefully, recording detailed information about any failures
    for later analysis. It also adds a small delay between requests to avoid overwhelming the server.

    Args:
        bundle_files: List of bundle file paths to process.
                     Example: ["data/generated_bundles/Patient_bundle.json"]
        server_url: URL of the FHIR server, including the base path.
                   Example: "http://localhost:8080/fhir" or "https://fhir.example.com/fhir"
        test_id: Optional identifier for this test run, useful for comparing multiple runs.
                Example: "8cpu_16gb_test1" or "production_server_baseline"
                If not provided, a timestamp-based ID will be generated automatically.
        sampling_interval: Interval in seconds between resource usage samples (default: 5).
                          Lower values provide more granular data but increase overhead.
                          Recommended range: 1-30 seconds depending on test duration.

    Returns:
        Tuple of (success_count, error_count, processing_stats) where:
        - success_count: Number of bundles successfully processed
        - error_count: Number of bundles that failed processing
        - processing_stats: BundleProcessingStats object containing detailed metrics and statistics

    Example:
        ```python
        bundle_files = find_bundle_files("data/generated_bundles")
        success_count, error_count, stats = load_and_send_bundles(
            bundle_files,
            "http://localhost:8080/fhir",
            test_id="performance_test_1",
            sampling_interval=10
        )
        print(f"Processed {len(bundle_files)} bundles: {success_count} succeeded, {error_count} failed")
        print(stats.generate_summary_report())
        ```
    """
    success_count = 0
    error_count = 0
    total_files = len(bundle_files)

    # Initialize statistics tracking with test_id
    stats = BundleProcessingStats(test_id=test_id)
    stats.sampling_interval = sampling_interval
    stats.start_processing()

    for i, file_path in enumerate(bundle_files, 1):
        logger.info(f"Processing file {i}/{total_files}: {file_path}")

        # Track processing time for this bundle
        bundle_start_time = time.time()
        error_message = None

        # Load the bundle
        bundle = load_bundle_from_file(file_path)
        if not bundle:
            error_message = f"Failed to load bundle from file"
            logger.error(f"{error_message}: {file_path}")
            error_count += 1
            continue

        # Send the bundle to the server
        try:
            # Custom wrapper to capture error messages
            def wrapped_send_bundle(bundle, server_url):
                headers = {
                    "Content-Type": "application/fhir+json",
                    "Accept": "application/fhir+json"
                }

                try:
                    response = requests.post(
                        server_url,
                        headers=headers,
                        json=bundle,
                        timeout=120  # Longer timeout for large bundles
                    )

                    if response.status_code in (200, 201):
                        logger.info(f"Bundle processed successfully (status code: {response.status_code})")
                        return True, response.json()
                    else:
                        error_text = response.text
                        logger.error(f"Error processing bundle: {response.status_code} - {error_text}")

                        # Try to parse the error response
                        try:
                            error_data = response.json()
                            return False, error_data
                        except:
                            return False, error_text

                except Exception as e:
                    logger.error(f"Request error: {e}")
                    return False, str(e)

            # Use our wrapped function
            success, response_data = wrapped_send_bundle(bundle, server_url)

            # Capture error message if failed
            if not success:
                # First check if we have a structured error response
                if isinstance(response_data, dict):
                    # Try to extract meaningful error message from response
                    if 'issue' in response_data and isinstance(response_data['issue'], list) and len(response_data['issue']) > 0:
                        issue = response_data['issue'][0]
                        if 'diagnostics' in issue:
                            error_message = issue['diagnostics']
                        elif 'details' in issue and 'text' in issue['details']:
                            error_message = issue['details']['text']
                        else:
                            error_message = f"Error code: {issue.get('code', 'unknown')}"
                    else:
                        error_message = str(response_data)
                # If we don't have structured response, check if we have a string
                elif isinstance(response_data, str):
                    error_message = response_data
                # Fallback to a generic error message
                else:
                    error_message = "Failed to process bundle"
        except Exception as e:
            success = False
            error_message = f"Exception during bundle processing: {str(e)}"
            logger.error(error_message)

        # Calculate processing time
        bundle_processing_time = time.time() - bundle_start_time

        # Record statistics
        stats.record_bundle_processing(file_path, bundle, success, bundle_processing_time, error_message)

        # Sample resource usage
        stats.sample_resource_usage(i, stats.total_resources)

        if success:
            success_count += 1
        else:
            error_count += 1

        # Add a small delay between requests to avoid overwhelming the server
        if i < total_files:
            time.sleep(0.5)

    # Mark the end of processing
    stats.end_processing()

    return success_count, error_count, stats

def parse_args():
    """
    Parse command line arguments for the FHIR bundle loading script.

    This function sets up the argument parser with all available options for the script,
    including basic bundle loading parameters and advanced performance monitoring options.
    It handles default values from environment variables and configuration files.

    Returns:
        argparse.Namespace: Parsed command line arguments with the following attributes:
            - bundle_dir: Directory containing FHIR transaction bundles
            - server_url: URL of the FHIR server
            - pattern: File pattern to match
            - test_id: Identifier for this test run
            - export_performance: Whether to export performance data
            - output_dir: Directory to save performance reports
            - sampling_interval: Interval in seconds between resource usage samples

    Example:
        ```
        # Basic usage
        python load_all_bundles.py --bundle-dir ./data/generated_bundles

        # With performance metrics
        python load_all_bundles.py --bundle-dir ./data/generated_bundles --export-performance

        # Full configuration
        python load_all_bundles.py --bundle-dir ./data/generated_bundles --server-url http://localhost:8080/fhir \
                                  --pattern "Patient_*.json" --test-id "patient_only_test" \
                                  --export-performance --output-dir ./custom_reports --sampling-interval 10
        ```
    """
    parser = argparse.ArgumentParser(
        description="Load all FHIR transaction bundles from a directory to a FHIR server with performance metrics."
    )

    # Basic parameters
    parser.add_argument(
        "--bundle-dir",
        default=os.environ.get("BUNDLE_DIR", config.DEFAULT_OUTPUT_DIR),
        help=f"Directory containing FHIR transaction bundles (default: {config.DEFAULT_OUTPUT_DIR}). "
             f"Can be set with BUNDLE_DIR environment variable."
    )
    parser.add_argument(
        "--server-url",
        default=os.environ.get("FHIR_SERVER_URL", config.DEFAULT_SERVER_URL),
        help=f"URL of the FHIR server (default: {config.DEFAULT_SERVER_URL}). "
             f"Can be set with FHIR_SERVER_URL environment variable."
    )
    parser.add_argument(
        "--pattern",
        default="*.json",
        help="File pattern to match (default: *.json). Examples: '*.json', 'Patient_*.json'"
    )

    # Performance metrics parameters
    parser.add_argument(
        "--test-id",
        help="Identifier for this test run (useful for comparing multiple runs). "
             "Examples: '8cpu_16gb_test1', 'production_server_baseline'"
    )
    parser.add_argument(
        "--export-performance",
        action="store_true",
        help="Export performance data to a JSON file for later analysis"
    )
    parser.add_argument(
        "--output-dir",
        default="./performance_reports",
        help="Directory to save performance reports (default: ./performance_reports). "
             "Will be created if it doesn't exist."
    )
    parser.add_argument(
        "--sampling-interval",
        type=int,
        default=5,
        help="Interval in seconds between resource usage samples (default: 5). "
             "Lower values provide more granular data but increase overhead."
    )

    return parser.parse_args()

def main():
    """
    Main function for the FHIR bundle loading script.

    This function orchestrates the entire process:
    1. Parses command line arguments
    2. Finds all bundle files matching the specified pattern
    3. Loads and sends each bundle to the FHIR server
    4. Generates and displays a summary report
    5. Exports performance data if requested
    6. Returns an appropriate exit code

    The function handles errors gracefully and provides detailed logging
    throughout the process. It returns 0 if all bundles were processed
    successfully, or 1 if any errors occurred.

    Returns:
        int: Exit code (0 for success, 1 for failure)
    """
    args = parse_args()

    # Find all bundle files
    bundle_files = find_bundle_files(args.bundle_dir, args.pattern)
    if not bundle_files:
        logger.error(f"No bundle files found in {args.bundle_dir}")
        return 1

    # Log the start of processing
    logger.info(f"Starting to process {len(bundle_files)} bundle files from {args.bundle_dir}")
    logger.info(f"Sending to FHIR server: {args.server_url}")
    if args.test_id:
        logger.info(f"Test ID: {args.test_id}")

    # Load and send each bundle
    success_count, error_count, stats = load_and_send_bundles(
        bundle_files,
        args.server_url,
        test_id=args.test_id,
        sampling_interval=args.sampling_interval
    )

    # Generate and display the summary report
    summary_report = stats.generate_summary_report()
    print(summary_report)

    # Export performance data if requested
    if args.export_performance:
        output_path = stats.export_performance_data(args.output_dir)
        if output_path:
            logger.info(f"Performance data exported to {output_path}")
            print(f"\nPerformance data exported to: {output_path}")
            print("This file can be used for detailed analysis and comparison with other test runs.")

    # Log basic results for compatibility with existing tools/scripts
    total_time = stats.end_time - stats.start_time
    logger.info(f"Processing completed in {total_time:.2f} seconds")
    logger.info(f"Results: {success_count} successful, {error_count} failed, {len(bundle_files)} total")

    # Print final summary to console
    print(f"\nProcessing completed in {total_time:.2f} seconds")
    print(f"Results: {success_count} successful, {error_count} failed, {len(bundle_files)} total")

    # Return success if all bundles were processed successfully
    success = error_count == 0
    if success:
        print("\nAll bundles processed successfully!")
    else:
        print(f"\nWarning: {error_count} bundles failed to process. See log for details.")

    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
