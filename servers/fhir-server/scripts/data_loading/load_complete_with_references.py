#!/usr/bin/env python3
"""
FHIR Data Loading Tool with Reference Preservation

This script loads FHIR resources from NDJSON files into a HAPI FHIR server,
preserving references between resources. It analyzes the references in the data
and creates synthetic resources for missing references to ensure that all
references are valid.

The script follows these steps:
1. Analyze all NDJSON files to identify missing references
2. Create synthetic resources for missing references (Location, Practitioner, Organization, etc.)
3. Load all resources in the correct order to maintain references
4. Optionally verify that references are maintained after loading

References:
- FHIR Resource Identification: https://build.fhir.org/resource.html#identification
- FHIR REST API: https://build.fhir.org/http.html#update
- HAPI FHIR REST Operations: https://hapifhir.io/hapi-fhir/docs/server_plain/rest_operations.html
"""

import argparse
import json
import os
import re
import sys
import time
from typing import Dict, List, Optional, Set, Tuple

import requests
from requests.exceptions import RequestException

# Definir el orden de carga (primero los recursos base, luego los que tienen referencias)
RESOURCE_ORDER = [
    "Location",
    "Organization",
    "Practitioner",
    "Patient",
    "Encounter",
    "Condition",
    "Observation",
    "Procedure",
    "MedicationRequest",
    "DiagnosticReport",
    "DocumentReference",
    "AllergyIntolerance",
    "Immunization",
    "Device",
]

def parse_args():
    """
    Parse command line arguments.

    Returns
    -------
    argparse.Namespace
        Parsed command line arguments
    """
    parser = argparse.ArgumentParser(
        description="Load FHIR data while preserving references between resources."
    )
    parser.add_argument(
        "--data-dir",
        default="../../data/sample_fhir/bulk-export",
        help="Directory containing NDJSON files (default: ../../data/sample_fhir/bulk-export)",
    )
    parser.add_argument(
        "--server-url",
        default="http://localhost:8080/fhir",
        help="URL of the FHIR server (default: http://localhost:8080/fhir)",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=10,
        help="Number of resources to process in each batch (default: 10)",
    )
    parser.add_argument(
        "--verify-references",
        action="store_true",
        help="Verify that references between resources are maintained",
    )
    parser.add_argument(
        "--clean",
        action="store_true",
        help="Clean the FHIR server before loading data",
    )
    return parser.parse_args()

def check_server_availability(server_url: str) -> bool:
    """
    Check if the FHIR server is available.

    Parameters
    ----------
    server_url : str
        URL of the FHIR server

    Returns
    -------
    bool
        True if the server is available, False otherwise
    """
    try:
        response = requests.get(f"{server_url}/metadata")
        return response.status_code == 200
    except RequestException:
        return False

def get_resource_files(data_dir: str) -> Dict[str, str]:
    """
    Get NDJSON files for each resource type.

    Parameters
    ----------
    data_dir : str
        Directory containing NDJSON files

    Returns
    -------
    Dict[str, str]
        Dictionary mapping resource types to file paths
    """
    resource_files = {}
    for filename in os.listdir(data_dir):
        if filename.endswith(".ndjson"):
            # Extraer el tipo de recurso del nombre del archivo (eliminar la extensión .ndjson y cualquier sufijo numérico)
            resource_type = filename.split(".")[0]
            resource_files[resource_type] = os.path.join(data_dir, filename)
    return resource_files

def extract_references(resource_json: str) -> List[Tuple[str, str]]:
    """
    Extract references from a FHIR resource.

    This function extracts both direct references (ResourceType/id) and
    conditional references (ResourceType?identifier=system|value).

    Parameters
    ----------
    resource_json : str
        JSON string representation of a FHIR resource

    Returns
    -------
    List[Tuple[str, str]]
        List of tuples containing (resource_type, resource_id)
    """
    references = []

    # Buscar referencias en el formato "reference":"ResourceType/id"
    ref_pattern = r'"reference"\s*:\s*"([^/]+)/([^"]+)"'
    for match in re.finditer(ref_pattern, resource_json):
        resource_type, resource_id = match.groups()
        references.append((resource_type, resource_id))

    # Buscar referencias condicionales en el formato "reference":"ResourceType?identifier=system|value"
    cond_ref_pattern = r'"reference"\s*:\s*"([^?]+)\?identifier=([^|]+)\|([^"]+)"'
    for match in re.finditer(cond_ref_pattern, resource_json):
        resource_type, system, value = match.groups()
        references.append((resource_type, f"{system}|{value}"))

    return references

def analyze_references(data_dir: str, resource_files: Dict[str, str]) -> Dict[str, Set[str]]:
    """
    Analyze all NDJSON files to identify references between resources.

    This function identifies missing references that need to be created
    before loading the resources.

    Parameters
    ----------
    data_dir : str
        Directory containing NDJSON files (not used directly but kept for API consistency)
    resource_files : Dict[str, str]
        Dictionary mapping resource types to file paths

    Returns
    -------
    Dict[str, Set[str]]
        Dictionary mapping resource types to sets of missing resource IDs
    """
    print("Analyzing references in NDJSON files...")

    # Dictionary to store existing resources by type
    existing_resources = {}

    # Dictionary to store references by type
    references = {}

    # First, collect all existing resources
    for resource_type, file_path in resource_files.items():
        existing_resources[resource_type] = set()

        try:
            with open(file_path, "r") as f:
                for line in f:
                    if not line.strip():
                        continue

                    try:
                        resource = json.loads(line)
                        resource_id = resource.get("id")

                        if resource_id:
                            existing_resources[resource_type].add(resource_id)
                    except json.JSONDecodeError:
                        continue
        except Exception as e:
            print(f"❌ Error reading file {file_path}: {e}")

    # Then, collect all references
    for resource_type, file_path in resource_files.items():
        try:
            with open(file_path, "r") as f:
                for line in f:
                    if not line.strip():
                        continue

                    try:
                        refs = extract_references(line)

                        for ref_type, ref_id in refs:
                            if ref_type not in references:
                                references[ref_type] = set()

                            references[ref_type].add(ref_id)
                    except Exception as e:
                        print(f"❌ Error analyzing references: {e}")
        except Exception as e:
            print(f"❌ Error reading file {file_path}: {e}")

    # Identify missing references
    missing_references = {}

    for ref_type, ref_ids in references.items():
        if ref_type not in existing_resources:
            missing_references[ref_type] = ref_ids
        else:
            missing_ids = ref_ids - existing_resources[ref_type]

            if missing_ids:
                missing_references[ref_type] = missing_ids

    # Show a summary of missing references
    print("\nSummary of missing references:")
    for ref_type, ref_ids in missing_references.items():
        print(f"  - {ref_type}: {len(ref_ids)} missing references")

    return missing_references

def clean_server(server_url: str) -> None:
    """
    Clean the FHIR server by deleting all resources.

    This function attempts to delete all resources of each type defined in
    RESOURCE_ORDER. Note that this may not completely clean the server if
    there are reference constraints preventing deletion.

    Parameters
    ----------
    server_url : str
        URL of the FHIR server
    """
    print("\nCleaning the FHIR server...")

    for resource_type in RESOURCE_ORDER:
        try:
            # Get all resources of this type
            response = requests.get(f"{server_url}/{resource_type}")

            if response.status_code == 200:
                bundle = response.json()

                if "entry" in bundle:
                    for entry in bundle.get("entry", []):
                        resource = entry.get("resource", {})
                        resource_id = resource.get("id")

                        if resource_id:
                            # Delete the resource
                            delete_response = requests.delete(f"{server_url}/{resource_type}/{resource_id}")

                            if delete_response.status_code in (200, 204):
                                print(f"  - Deleted {resource_type}/{resource_id}")
                            else:
                                print(f"  ❌ Error deleting {resource_type}/{resource_id}: {delete_response.text}")
        except Exception as e:
            print(f"❌ Error cleaning resources of type {resource_type}: {e}")

    print("✅ FHIR server cleaned")

def create_missing_resources(missing_references: Dict[str, Set[str]], server_url: str) -> int:
    """
    Create missing resources in the FHIR server.

    This function creates synthetic resources for references that were found
    in the NDJSON files but don't exist as actual resources. This ensures that
    all references will be valid when loading the actual resources.

    Parameters
    ----------
    missing_references : Dict[str, Set[str]]
        Dictionary mapping resource types to sets of missing resource IDs
    server_url : str
        URL of the FHIR server

    Returns
    -------
    int
        Number of resources successfully created
    """
    print("\nCreating missing resources...")

    success_count = 0

    for resource_type, resource_ids in missing_references.items():
        print(f"Creating {len(resource_ids)} resources of type {resource_type}...")

        for resource_id in resource_ids:
            # Check if the ID is a conditional identifier
            if "|" in resource_id:
                system, value = resource_id.split("|", 1)

                # Create a resource with the specified identifier
                resource = {
                    "resourceType": resource_type,
                    "id": f"synthetic-{value.replace('/', '-')}",
                    "identifier": [
                        {
                            "system": system,
                            "value": value
                        }
                    ],
                    "active": True
                }

                # Add specific properties based on resource type
                if resource_type == "Location":
                    resource["status"] = "active"
                    resource["name"] = f"Synthetic Location {value}"
                    resource["mode"] = "instance"
                elif resource_type == "Practitioner":
                    resource["active"] = True
                    resource["name"] = [
                        {
                            "family": f"Synthetic",
                            "given": [f"Practitioner {value}"]
                        }
                    ]
                elif resource_type == "Organization":
                    resource["active"] = True
                    resource["name"] = f"Synthetic Organization {value}"
            else:
                # Create a resource with the specified ID
                resource = {
                    "resourceType": resource_type,
                    "id": resource_id,
                    "active": True
                }

                # Add specific properties based on resource type
                if resource_type == "Location":
                    resource["status"] = "active"
                    resource["name"] = f"Synthetic Location {resource_id}"
                    resource["mode"] = "instance"
                elif resource_type == "Practitioner":
                    resource["active"] = True
                    resource["name"] = [
                        {
                            "family": f"Synthetic",
                            "given": [f"Practitioner {resource_id}"]
                        }
                    ]
                elif resource_type == "Organization":
                    resource["active"] = True
                    resource["name"] = f"Synthetic Organization {resource_id}"

            # Use PUT to create the resource with the specified ID
            try:
                response = requests.put(
                    f"{server_url}/{resource_type}/{resource['id']}",
                    headers={"Content-Type": "application/fhir+json"},
                    json=resource,
                )

                if response.status_code in (200, 201):
                    success_count += 1
                else:
                    print(f"❌ Error creating {resource_type}/{resource['id']}: {response.text}")
            except Exception as e:
                print(f"❌ Error creating {resource_type}/{resource['id']}: {e}")

    print(f"✅ Created {success_count} missing resources")
    return success_count

def load_resources(
    resource_type: str,
    file_path: str,
    server_url: str,
    batch_size: int,
) -> Tuple[int, int]:
    """
    Load resources of a specific type into the FHIR server.

    This function loads resources from an NDJSON file into the FHIR server
    using PUT requests to preserve the original resource IDs.

    Parameters
    ----------
    resource_type : str
        Type of FHIR resource to load
    file_path : str
        Path to the NDJSON file containing the resources
    server_url : str
        URL of the FHIR server
    batch_size : int
        Number of resources to process before showing progress

    Returns
    -------
    Tuple[int, int]
        Tuple containing (success_count, error_count)
    """
    success_count = 0
    error_count = 0

    try:
        with open(file_path, "r") as f:
            resources = [line.strip() for line in f if line.strip()]
    except Exception as e:
        print(f"❌ Error reading file {file_path}: {e}")
        return 0, 0

    total_resources = len(resources)
    print(f"Loading {total_resources} resources of type {resource_type}...")

    for i, resource_json in enumerate(resources):
        try:
            # Parse the JSON resource
            resource = json.loads(resource_json)
            resource_id = resource.get("id")

            if not resource_id:
                print(f"❌ Error: Resource without ID at position {i+1}")
                error_count += 1
                continue

            # Use PUT to preserve the original ID
            response = requests.put(
                f"{server_url}/{resource_type}/{resource_id}",
                headers={"Content-Type": "application/fhir+json"},
                json=resource,
            )

            if response.status_code in (200, 201):
                success_count += 1
            else:
                print(f"❌ Error loading {resource_type}/{resource_id}: {response.text}")
                error_count += 1

            # Show progress
            if (i + 1) % batch_size == 0 or i + 1 == total_resources:
                print(f"  - Processed {i+1}/{total_resources} resources ({success_count} successes, {error_count} errors)")

        except Exception as e:
            print(f"❌ Error processing resource: {e}")
            error_count += 1

    print(f"✅ Completed {resource_type}: {success_count} successes, {error_count} errors")
    return success_count, error_count

def verify_references(server_url: str) -> bool:
    """
    Verify that references between resources are maintained.

    This function checks that references between resources are working correctly
    by retrieving a sample of patients and checking if related resources
    (observations, conditions, encounters, immunizations) can be found.

    Parameters
    ----------
    server_url : str
        URL of the FHIR server

    Returns
    -------
    bool
        True if references are working correctly, False otherwise
    """
    print("\nVerifying references between resources...")

    # Get some patients to verify
    try:
        response = requests.get(f"{server_url}/Patient?_count=5")
        if response.status_code != 200:
            print(f"❌ Error getting patients: {response.text}")
            return False

        patients = response.json().get("entry", [])
        if not patients:
            print("❌ No patients found for reference verification")
            return False

        references_ok = False

        for entry in patients:
            patient = entry.get("resource", {})
            patient_id = patient.get("id")

            if not patient_id:
                continue

            print(f"\nVerifying references for Patient/{patient_id}:")

            # Check observations linked to the patient
            obs_response = requests.get(f"{server_url}/Observation?subject=Patient/{patient_id}&_summary=count")
            if obs_response.status_code == 200:
                obs_count = obs_response.json().get("total", 0)
                print(f"  - Linked observations: {obs_count}")
            else:
                print(f"  - Error checking observations: {obs_response.text}")
                obs_count = 0

            # Check conditions linked to the patient
            cond_response = requests.get(f"{server_url}/Condition?subject=Patient/{patient_id}&_summary=count")
            if cond_response.status_code == 200:
                cond_count = cond_response.json().get("total", 0)
                print(f"  - Linked conditions: {cond_count}")
            else:
                print(f"  - Error checking conditions: {cond_response.text}")
                cond_count = 0

            # Check encounters linked to the patient
            enc_response = requests.get(f"{server_url}/Encounter?subject=Patient/{patient_id}&_summary=count")
            if enc_response.status_code == 200:
                enc_count = enc_response.json().get("total", 0)
                print(f"  - Linked encounters: {enc_count}")
            else:
                print(f"  - Error checking encounters: {enc_response.text}")
                enc_count = 0

            # Check immunizations linked to the patient
            imm_response = requests.get(f"{server_url}/Immunization?patient=Patient/{patient_id}&_summary=count")
            if imm_response.status_code == 200:
                imm_count = imm_response.json().get("total", 0)
                print(f"  - Linked immunizations: {imm_count}")
            else:
                print(f"  - Error checking immunizations: {imm_response.text}")
                imm_count = 0

            # Determine if references are working
            if obs_count > 0 or cond_count > 0 or enc_count > 0 or imm_count > 0:
                print("  ✅ References for this patient are working correctly")
                references_ok = True
            else:
                print("  ⚠️ No resources linked to this patient were found")

        return references_ok

    except Exception as e:
        print(f"❌ Error verifying references: {e}")
        return False

def main():
    """
    Main function to execute the FHIR data loading process.

    This function:
    1. Parses command line arguments
    2. Verifies the FHIR server is available
    3. Optionally cleans the server
    4. Analyzes references in NDJSON files
    5. Creates missing resources
    6. Loads resources in the correct order
    7. Optionally verifies references

    Returns
    -------
    None
    """
    args = parse_args()

    print("=== FHIR Data Loading Tool with Reference Preservation ===")
    print(f"Data directory: {args.data_dir}")
    print(f"FHIR server URL: {args.server_url}")
    print(f"Batch size: {args.batch_size}")
    print()

    # Verify that the FHIR server is available
    print(f"Verifying FHIR server at {args.server_url}...")
    if not check_server_availability(args.server_url):
        print(f"❌ Error: FHIR server not available at {args.server_url}")
        print("Please make sure the server is running and accessible.")
        sys.exit(1)
    print("✅ FHIR server available")

    # Clean the server if requested
    if args.clean:
        clean_server(args.server_url)

    # Get NDJSON files for each resource type
    resource_files = get_resource_files(args.data_dir)

    # Analyze references to identify missing resources
    missing_references = analyze_references(args.data_dir, resource_files)

    # Create missing resources
    if missing_references:
        create_missing_resources(missing_references, args.server_url)

    # Load resources in the correct order
    total_success = 0
    total_error = 0

    for resource_type in RESOURCE_ORDER:
        if resource_type in resource_files:
            success, error = load_resources(
                resource_type,
                resource_files[resource_type],
                args.server_url,
                args.batch_size,
            )
            total_success += success
            total_error += error
        else:
            print(f"⚠️ No file found for resource type {resource_type}")

    # Load any remaining resource types that are not in the predefined order
    for resource_type, file_path in resource_files.items():
        if resource_type not in RESOURCE_ORDER:
            success, error = load_resources(
                resource_type,
                file_path,
                args.server_url,
                args.batch_size,
            )
            total_success += success
            total_error += error

    print(f"\n✅ Data loading completed: {total_success} successes, {total_error} errors")

    # Verify references if requested
    if args.verify_references:
        if verify_references(args.server_url):
            print("\n✅ Reference verification completed successfully")
        else:
            print("\n⚠️ Some references could not be verified")

    print(f"\nYou can access the resources at: {args.server_url}")

if __name__ == "__main__":
    main()
