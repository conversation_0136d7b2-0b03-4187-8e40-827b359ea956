#!/bin/bash
# FHIR Server Management Script
#
# This script manages the FHIR server with PostgreSQL database configuration.
# It supports starting, stopping, and checking the status of the server.
#
# References:
# - HAPI FHIR Docker: https://hub.docker.com/r/hapiproject/hapi
# - HAPI FHIR JPA Server Starter: https://github.com/hapifhir/hapi-fhir-jpaserver-starter

# Determine which Docker Compose command to use
if command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    COMPOSE_CMD="docker compose"
fi

# Default configuration
DB_TYPE="postgres"  # Only PostgreSQL is supported
ACTION=$1

# Function to display usage information
usage() {
    echo "Usage: $0 [start|stop|status|logs|restart]"
    echo ""
    echo "Actions:"
    echo "  start    - Start the FHIR server"
    echo "  stop     - Stop the FHIR server"
    echo "  status   - Check the status of the FHIR server"
    echo "  logs     - Show logs from the FHIR server"
    echo "  restart  - Restart the FHIR server"
    echo ""
    echo "Examples:"
    echo "  $0 start    - Start the server"
    echo "  $0 status   - Check server status"
    echo "  $0 restart  - Restart the server"
    exit 1
}

# Function to get the compose file
get_compose_file() {
    echo "docker-compose-postgres.yml"
}

# Check if action is provided
if [ -z "$ACTION" ]; then
    usage
fi

# Get the appropriate compose file
COMPOSE_FILE=$(get_compose_file)

# Execute the requested action
case $ACTION in
    start)
        echo "Starting FHIR server with PostgreSQL database..."
        $COMPOSE_CMD -f $COMPOSE_FILE up -d
        echo "Server starting. Check status with: $0 status"
        ;;
    stop)
        echo "Stopping FHIR server..."
        $COMPOSE_CMD -f $COMPOSE_FILE down
        ;;
    status)
        echo "Checking FHIR server status..."
        $COMPOSE_CMD -f $COMPOSE_FILE ps
        ;;
    logs)
        echo "Showing FHIR server logs..."
        $COMPOSE_CMD -f $COMPOSE_FILE logs -f
        ;;
    restart)
        echo "Restarting FHIR server with PostgreSQL database..."
        $COMPOSE_CMD -f $COMPOSE_FILE down
        $COMPOSE_CMD -f $COMPOSE_FILE up -d
        ;;
    *)
        echo "Invalid action: $ACTION"
        usage
        ;;
esac

exit 0
