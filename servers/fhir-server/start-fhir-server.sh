#!/bin/bash

# <PERSON>ript to start and manage the HAPI FHIR server

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if Docker is installed
if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install Docker before continuing."
    exit 1
fi

# Check if Docker Compose is available and determine which command to use
if command_exists docker-compose; then
    DOCKER_COMPOSE="docker-compose"
    echo "Using docker-compose"
elif docker compose version > /dev/null 2>&1; then
    DOCKER_COMPOSE="docker compose"
    echo "Using docker compose (modern version)"
else
    echo "❌ Docker Compose is not installed. Please install Docker Compose before continuing."
    exit 1
fi

# Function to display help
show_help() {
    echo "Usage: $0 [command]"
    echo ""
    echo "Available commands:"
    echo "  start       Start the HAPI FHIR server"
    echo "  stop        Stop the HAPI FHIR server"
    echo "  restart     Restart the HAPI FHIR server"
    echo "  status      Show the status of the HAPI FHIR server"
    echo "  logs        Show the logs of the HAPI FHIR server"
    echo "  help        Show this help message"
    echo ""
}

# Check if a command was provided
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# Process the command
case "$1" in
    start)
        echo "Starting HAPI FHIR server..."
        $DOCKER_COMPOSE up -d
        echo "✅ HAPI FHIR server started. Access http://localhost:${FHIR_PORT:-8080}/fhir/metadata to verify."
        ;;
    stop)
        echo "Stopping HAPI FHIR server..."
        $DOCKER_COMPOSE down
        echo "✅ HAPI FHIR server stopped."
        ;;
    restart)
        echo "Restarting HAPI FHIR server..."
        $DOCKER_COMPOSE down
        $DOCKER_COMPOSE up -d
        echo "✅ HAPI FHIR server restarted."
        ;;
    status)
        echo "HAPI FHIR server status:"
        $DOCKER_COMPOSE ps
        ;;
    logs)
        echo "Showing HAPI FHIR server logs:"
        $DOCKER_COMPOSE logs -f
        ;;
    help)
        show_help
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_help
        exit 1
        ;;
esac

exit 0
