services:
  # HAPI FHIR Server with PostgreSQL
  # Based on the official HAPI FHIR JPA Server Starter example:
  # https://github.com/hapifhir/hapi-fhir-jpaserver-starter
  hapi-fhir-server:
    image: hapiproject/hapi:latest                  # Official HAPI FHIR server image
    ports:
      - "${FHIR_PORT:-8080}:8080"                   # Exposes container port 8080 to the host port defined by FHIR_PORT or defaults to 8080
    environment:
      # Basic FHIR server settings
      - hapi.fhir.default_encoding=json             # Sets default encoding to JSON
      - hapi.fhir.fhir_version=${FHIR_VERSION:-R4}  # Sets the FHIR version to use

      # Bulk operations configuration
      - hapi.fhir.bulk_export_enabled=true          # Enables FHIR bulk export feature
      - hapi.fhir.bulk_import_enabled=true          # Enables FHIR bulk import feature
      - hapi.fhir.client_id_strategy=ANY            # Allows any client ID for bulk operations

      # FHIR Server Data Validation Configuration
      # By default, the server enforces data integrity and validation
      # For bulk data loading, uncomment the following lines to disable validation and referential integrity checks
      # After completing data import, comment these lines again to restore default security and integrity settings
      # -----------------------------------------------------------------------------------------------------
      - hapi.fhir.validation.enabled=false                        # Disables validation
      - hapi.fhir.validation.request_validator.mode=NONE          # Disables request validation
      - hapi.fhir.auto_create_placeholder_reference_targets=true  # Creates placeholder resources for missing references
      - hapi.fhir.enforce_referential_integrity_on_write=false    # Disables referential integrity checks on write
      - hapi.fhir.enforce_referential_integrity_on_delete=false   # Disables referential integrity checks on delete

      # PostgreSQL configuration
      # Using the recommended dialect from the HAPI FHIR documentation:
      # https://hapifhir.io/hapi-fhir/docs/server_jpa/database_support.html
      #
      # TODO: [ISSUE-DB-001] Fix HAPI FHIR dialect configuration
      # Despite setting the correct dialect (HapiFhirPostgresDialect), the server logs show:
      # "Dialect is not a HAPI FHIR dialect: org.hibernate.dialect.PostgreSQLDialect, version: 14.17"
      # This may affect performance with large datasets. Investigate:
      # 1. Alternative configuration methods
      # 2. Check if this is a known issue in the HAPI FHIR community
      # 3. Consider custom Docker image with patched configuration
      # Reference: https://groups.google.com/g/hapi-fhir/
      #
      - spring.datasource.url=*******************************:${POSTGRES_PORT}/${POSTGRES_DB}  # JDBC connection URL to PostgreSQL database
      - spring.datasource.username=${POSTGRES_USER}                                            # Database username
      - spring.datasource.password=${POSTGRES_PASSWORD}                                        # Database password
      - spring.datasource.driverClassName=org.postgresql.Driver                                # JDBC driver for PostgreSQL
      - spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect  # Optimized dialect for HAPI FHIR and PostgreSQL
      - spring.jpa.properties.hibernate.search.enabled=false                                   # Disables advanced search (not required for most PostgreSQL use cases)
    volumes:
      - hapi-data:/data/hapi                        # Persistent volume for FHIR server data
      - ../../data/sample_fhir/bulk-export-filtered:/data/import  # Mount local directory for import
    restart: unless-stopped                         # Restart container unless stopped manually
    depends_on:
      - fhir-postgres                               # Waits for the database to be ready before starting
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/fhir/metadata"]  # Checks if the metadata endpoint is available
      interval: 30s                                 # Interval between health checks
      timeout: 10s                                  # Maximum wait time for a response
      retries: 5                                    # Number of retries before marking as unhealthy

  # PostgreSQL database service
  # PostgreSQL 14 is used for compatibility and stability with HAPI FHIR
  # Note on PostgreSQL version choice:
  # - PostgreSQL 15 and 16 have known compatibility issues with HAPI FHIR due to the "clob" data type
  #   See: https://groups.google.com/g/hapi-fhir/c/gQLLcRtlwpI (Error: "type clob does not exist")
  # - The official HAPI FHIR JPA Server Starter uses PostgreSQL 15-alpine, but community reports
  #   indicate issues with this version: https://github.com/hapifhir/hapi-fhir-jpaserver-starter/blob/master/docker-compose.yml
  # - If upgrading to PostgreSQL 15+ in the future, test thoroughly and check if the "clob" issue has been resolved
  fhir-postgres:
    image: postgres:14                              # Official PostgreSQL version 14 image
    ports:
      - "${POSTGRES_EXTERNAL_PORT:-5433}:${POSTGRES_PORT}"
    environment:
      POSTGRES_DB: ${POSTGRES_DB}                  # Name of the database to create
      POSTGRES_USER: ${POSTGRES_USER}              # Database admin user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}      # Admin user password
    volumes:
      - postgres-data:/var/lib/postgresql/data    # Persistent volume for PostgreSQL data
    restart: unless-stopped                       # Restart container unless stopped manually
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]  # Checks if the database is ready
      interval: 10s                               # Interval between health checks
      timeout: 5s                                 # Maximum wait time for a response
      retries: 5                                  # Number of retries before marking as unhealthy

volumes:
  hapi-data:                                      # Persistent volume for FHIR server data
  postgres-data:                                  # Persistent volume for PostgreSQL data
