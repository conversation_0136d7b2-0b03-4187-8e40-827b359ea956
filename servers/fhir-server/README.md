# HAPI FHIR Server

This directory contains the configuration and scripts to run a local HAPI FHIR server using Docker. The server provides a standards-compliant implementation of the FHIR (Fast Healthcare Interoperability Resources) API for healthcare data exchange.

> **Note**: For comprehensive documentation on setting up and using the FHIR server, please refer to the [FHIR Server Documentation](../../../docs/guides/fhir/server/00-index.md) in the main documentation directory.

## Overview

We have implemented a local HAPI FHIR server using Docker with PostgreSQL database for production use. This setup provides a flexible foundation for the FHIR to OMOP transformation project, allowing for robust data management and integration.

## Directory Structure

```
servers/fhir-server/
├── .env                            # Environment variables
├── docker-compose-postgres.yml     # PostgreSQL database configuration
├── manage-fhir-server.sh           # Server management script
├── docs/                           # Documentation directory
│   └── guides/fhir/data-loading/   # Data loading documentation
├── scripts/                        # Python scripts directory
│   ├── bin/                        # Command-line entry points
│   │   └── manage_data.py          # Unified CLI for data management
│   ├── core/                       # Core functionality
│   │   ├── config.py               # Configuration settings
│   │   ├── logging.py              # Logging configuration
│   │   └── utils.py                # Common utilities
│   ├── data_loading/               # Data loading functionality
│   │   ├── ndjson_to_bundle.py     # Converts NDJSON to transaction bundles
│   │   ├── send_bundle.py          # Sends bundles to FHIR server
│   │   ├── load_all_bundles.py     # Loads all bundles with performance metrics
│   │   ├── selective_loader.py     # Selective loading with dependency resolution
│   │   ├── reference_analyzer.py   # Analyzes references between resources
│   │   └── stats.py                # Statistics and performance metrics
│   ├── data_validation/            # Data validation functionality
│   │   ├── verify_bundle.py        # Verifies transaction bundles
│   │   └── verify_loaded_resources.py # Verifies loaded resources
│   └── server_management/          # Server management functionality
│       ├── clean_server.py         # Cleans the FHIR server (limited functionality)
│       ├── test_fhir_server.py     # Tests FHIR server connectivity
│       └── verify_database.sh      # Verifies database configuration
└── README.md                       # This documentation
```

## Prerequisites

- Docker 20.10.x or higher
- Docker Compose 2.x or higher
- Python 3.8 or higher (for test scripts)

## Configuration

The HAPI FHIR server is configured using the `.env` file in this directory. You can create this file manually or copy the template provided below.

### Environment Variables

```
# HAPI FHIR Server Configuration

# Server configuration
FHIR_PORT=8080
FHIR_VERSION=R4

# Authentication (uncomment to enable)
#FHIR_USERNAME=fhir_user
#FHIR_PASSWORD=secure_fhir_password

# PostgreSQL configuration (primary database for production)
POSTGRES_DB=hapi
POSTGRES_USER=admin
POSTGRES_PASSWORD=secure_password
POSTGRES_PORT=5432
```

> **Security Note**: For production environments, make sure to change the default passwords to strong, unique values.

## Usage

### Starting the Server

```bash
# Start the server with PostgreSQL
cd servers/fhir-server
./manage-fhir-server.sh start postgres

# Check server status
./manage-fhir-server.sh status

# View server logs
./manage-fhir-server.sh logs

# Stop the server
./manage-fhir-server.sh stop

# Restart the server
./manage-fhir-server.sh restart postgres
```

### Testing the Server

The included Python script `scripts/server_management/test_fhir_server.py` performs automated validation of the FHIR server:

```bash
# Install dependencies (if necessary)
pip install requests python-dotenv

# Test with local server (default)
python scripts/server_management/test_fhir_server.py

# Test with cloud server
python scripts/server_management/test_fhir_server.py --server-url https://fhir.aiotek.ai/fhir

# Test with custom server from environment variable
export FHIR_SERVER_URL=https://fhir.example.org/fhir
python scripts/server_management/test_fhir_server.py
```

### Loading Sample Data

We provide two methods for loading FHIR data:

#### Method 1: Direct Transaction Bundles

This method requires configuring the server to handle references between resources:

```bash
# 1. Configure server for import mode (edit docker-compose-postgres.yml)
# Uncomment these lines in the environment section:
# - hapi.fhir.validation.enabled=false
# - hapi.fhir.validation.request_validator.mode=NONE
# - hapi.fhir.auto_create_placeholder_reference_targets=true
# - hapi.fhir.enforce_referential_integrity_on_write=false
# - hapi.fhir.enforce_referential_integrity_on_delete=false

# 2. Restart the server
./manage-fhir-server.sh restart postgres

# 3. Convert NDJSON to bundles and load them
# Assuming you are in the root of the repository
cd servers/fhir-server

# Run the conversion script (providing paths relative to current directory)
python scripts/data_loading/ndjson_to_bundle.py \
  --input-file ../../data/sample_fhir/bulk-export/Patient.000.ndjson \
  --output-file ../../data/generated_bundles/Patient_bundle.json

# Send the generated bundle to the FHIR server
python scripts/data_loading/send_bundle.py \
  --input-file ../../data/generated_bundles/Patient_bundle.json \
  --server-url http://localhost:8080/fhir
```

#### Method 2: Selective Loading

This method works with default server configuration but loads only resources with satisfied references:

```bash
# Load data with selective loading
python scripts/data_loading/selective_loader.py --data-dir ../../data/sample_fhir/bulk-export --server-url http://localhost:8080/fhir --verify
```

For detailed instructions on data loading methods, see the [FHIR Data Loading Documentation](../../docs/guides/fhir/data-loading/quick-reference.md).

### Accessing the Server

You can access the FHIR server directly from your browser:

- **Main page**: http://localhost:8080/
- **FHIR capabilities**: http://localhost:8080/fhir/metadata
- **Search for patients**: http://localhost:8080/fhir/Patient
- **Search for observations**: http://localhost:8080/fhir/Observation

## Database Configuration

We use PostgreSQL as our primary database for production use. The configuration is defined in `docker-compose-postgres.yml` and uses environment variables from the `.env` file.

### PostgreSQL Configuration

The PostgreSQL configuration in `docker-compose-postgres.yml` includes:

```yaml
# HAPI FHIR Server with PostgreSQL configuration
services:
  # HAPI FHIR Server
  hapi-fhir-server:
    image: hapiproject/hapi:latest
    ports:
      - "${FHIR_PORT:-8080}:8080"
    environment:
      - hapi.fhir.default_encoding=json
      - hapi.fhir.fhir_version=${FHIR_VERSION:-R4}
      - hapi.fhir.bulk_export_enabled=true
      - hapi.fhir.bulk_import_enabled=true
      # PostgreSQL configuration
      - spring.datasource.url=*******************************:${POSTGRES_PORT}/${POSTGRES_DB}
      - spring.datasource.username=${POSTGRES_USER}
      - spring.datasource.password=${POSTGRES_PASSWORD}
      - spring.datasource.driverClassName=org.postgresql.Driver
      - spring.jpa.properties.hibernate.dialect=ca.uhn.fhir.jpa.model.dialect.HapiFhirPostgresDialect
    depends_on:
      - fhir-postgres

  # PostgreSQL database
  fhir-postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres-data:/var/lib/postgresql/data

volumes:
  postgres-data:
```

Key characteristics of the PostgreSQL configuration:
- Robust data persistence through Docker volumes
- Better performance for large datasets
- Advanced database features (indexing, transactions, etc.)
- Improved concurrency handling
- PostgreSQL 14 for optimal compatibility with HAPI FHIR

### Data Import Configuration

For bulk data loading, you can temporarily modify the server configuration by uncommenting these lines in `docker-compose-postgres.yml`:

```yaml
# FHIR Server Data Validation Configuration
# Uncomment for bulk data loading, then comment out again after import
# - hapi.fhir.validation.enabled=false
# - hapi.fhir.validation.request_validator.mode=NONE
# - hapi.fhir.auto_create_placeholder_reference_targets=true
# - hapi.fhir.enforce_referential_integrity_on_write=false
# - hapi.fhir.enforce_referential_integrity_on_delete=false
```

### Database Reset

To reset the database completely:

```bash
# Stop the server
./manage-fhir-server.sh stop postgres

# Remove the PostgreSQL data volume
docker volume rm fhir-server_postgres-data

# Start the server again
./manage-fhir-server.sh start postgres
```

## Best Practices

1. **Clean the server before loading data**:
   - **Recommended method**: Use `docker volume rm fhir-server_postgres-data` followed by `./manage-fhir-server.sh start postgres` to completely reset the database.
   - **Note**: While a `clean_server.py` script exists, it has limited functionality and is not recommended for production use.

2. **Choose the right loading method**:
   - **Direct Transaction Bundles**: When you need to load all resources regardless of references
   - **Selective Loading**: When you need to maintain reference integrity with default server configuration

3. **Configure the server appropriately**:
   - For Direct Transaction Bundles, configure the server for import mode
   - For Selective Loading, use the default server configuration

4. **Verify loaded resources**: Always verify that resources were loaded correctly by using the `--verify` flag with selective loader or checking resource counts after loading.

5. **Restore default configuration**: After using import mode for bulk loading, restore the default server configuration for normal operation.

## Integration with the Main Project

To use this FHIR server with the main project, make sure the following variables are configured in the main `.env` file:

```
FHIR_SERVER_BASE_URL=http://localhost:8080/fhir
FHIR_SERVER_USERNAME=  # If you have enabled authentication
FHIR_SERVER_PASSWORD=  # If you have enabled authentication
```

## Security Configuration

By default, the FHIR server does not have authentication enabled. To enable basic authentication:

1. Uncomment and configure the following variables in the `.env` file:
   ```
   FHIR_USERNAME=fhir_user
   FHIR_PASSWORD=secure_fhir_password
   ```

2. Update the `docker-compose-postgres.yml` file to include these variables in the environment section:
   ```yaml
   - hapi.fhir.security.basic_auth_enabled=true
   - hapi.fhir.security.basic_auth_username=${FHIR_USERNAME}
   - hapi.fhir.security.basic_auth_password=${FHIR_PASSWORD}
   ```

3. Restart the server:
   ```bash
   ./manage-fhir-server.sh restart postgres
   ```

## Production Considerations

For production deployments, consider the following enhancements:

1. **Security**:
   - Enable HTTPS with proper certificates
   - Implement authentication and authorization
   - Configure firewalls to restrict access
   - Use strong, unique passwords for all services
   - Keep the HAPI FHIR server and PostgreSQL images updated

2. **Monitoring**:
   - Implement health checks and monitoring solutions
   - Set up alerts for server and database issues
   - Monitor resource usage (CPU, memory, disk)

3. **Data Management**:
   - Configure regular database backups
   - Implement a backup rotation strategy
   - Test backup restoration procedures

4. **Performance**:
   - Adjust container resource limits based on expected load
   - Configure connection pooling for the database
   - Optimize JVM settings for the FHIR server

## Additional Documentation

For more detailed information, see:

- [FHIR Server Documentation](../../../docs/guides/fhir/server/00-index.md) - Comprehensive guide for the HAPI FHIR server
- [Data Loading Documentation](../../../docs/guides/fhir/data-loading/quick-reference.md) - Guide for loading data into the FHIR server
- [Performance Metrics Guide](docs/guides/fhir/data-loading/performance-metrics.md) - Guide for monitoring and optimizing performance
