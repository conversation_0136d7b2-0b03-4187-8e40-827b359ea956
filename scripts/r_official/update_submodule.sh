#!/bin/bash
# Manual Update Helper for CommonDataModel Submodule
#
# This script helps you manually update the official OHDSI CommonDataModel
# submodule to newer versions with full control over the process.
#
# Usage:
#   ./scripts/r_official/update_submodule.sh
#   ./scripts/r_official/update_submodule.sh --version v5.4.2
#   ./scripts/r_official/update_submodule.sh --check

set -e

SUBMODULE_PATH="external/CommonDataModel"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔄 CommonDataModel Submodule Update Helper${NC}"
echo "================================================"

# Parse command line arguments
CHECK_ONLY=false
TARGET_VERSION=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --check)
            CHECK_ONLY=true
            shift
            ;;
        --version)
            TARGET_VERSION="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [--check] [--version VERSION]"
            echo ""
            echo "Options:"
            echo "  --check          Only check current status, don't update"
            echo "  --version VER    Update to specific version (e.g., v5.4.2)"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            exit 1
            ;;
    esac
done

cd "$PROJECT_ROOT"

# Check if submodule exists
if [ ! -d "$SUBMODULE_PATH" ]; then
    echo -e "${YELLOW}⚠️  Submodule not found. Initializing...${NC}"
    git submodule add https://github.com/OHDSI/CommonDataModel.git "$SUBMODULE_PATH"
    git submodule update --init
    echo -e "${GREEN}✅ Submodule initialized successfully${NC}"
fi

cd "$SUBMODULE_PATH"

# Get current status
CURRENT_COMMIT=$(git rev-parse HEAD)
CURRENT_TAG=$(git describe --tags --exact-match 2>/dev/null || echo "Not on a tag")
CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "detached")

echo -e "${BLUE}📊 Current Status:${NC}"
echo "   Path: $SUBMODULE_PATH"
echo "   Commit: ${CURRENT_COMMIT:0:8}"
echo "   Tag: $CURRENT_TAG"
echo "   Branch: $CURRENT_BRANCH"
echo ""

# Fetch latest information
echo -e "${BLUE}🔍 Fetching latest information...${NC}"
git fetch origin --tags

# Show available versions
echo -e "${BLUE}📋 Available versions (latest 10):${NC}"
git tag --sort=-version:refname | head -10

if [ "$CHECK_ONLY" = true ]; then
    echo -e "${GREEN}✅ Status check completed${NC}"
    exit 0
fi

echo ""
echo -e "${YELLOW}⚠️  UPDATE SAFETY CHECKLIST:${NC}"
echo "1. Have you backed up your current database?"
echo "2. Are you prepared to test database creation after update?"
echo "3. Do you have time to rollback if needed?"
echo ""

# Confirm update
read -p "$(echo -e "${YELLOW}Do you want to proceed with the update? (y/N): ${NC}")" -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}ℹ️  Update cancelled${NC}"
    exit 0
fi

# Determine target version
if [ -z "$TARGET_VERSION" ]; then
    echo ""
    echo -e "${BLUE}📌 Update options:${NC}"
    echo "1. Update to latest release tag"
    echo "2. Update to main branch"
    echo "3. Choose specific version"
    echo ""
    read -p "Choose option (1/2/3): " -n 1 -r OPTION
    echo
    
    case $OPTION in
        1)
            TARGET_VERSION=$(git tag --sort=-version:refname | head -1)
            echo -e "${GREEN}Selected latest release: $TARGET_VERSION${NC}"
            ;;
        2)
            TARGET_VERSION="main"
            echo -e "${GREEN}Selected main branch${NC}"
            ;;
        3)
            echo "Available versions:"
            git tag --sort=-version:refname | head -20
            echo ""
            read -p "Enter version (e.g., v5.4.2): " TARGET_VERSION
            ;;
        *)
            echo -e "${RED}Invalid option${NC}"
            exit 1
            ;;
    esac
fi

# Validate target version
if [ "$TARGET_VERSION" != "main" ]; then
    if ! git rev-parse "$TARGET_VERSION" >/dev/null 2>&1; then
        echo -e "${RED}❌ Version $TARGET_VERSION not found${NC}"
        exit 1
    fi
fi

# Perform update
echo ""
echo -e "${BLUE}🔄 Updating to $TARGET_VERSION...${NC}"

if [ "$TARGET_VERSION" = "main" ]; then
    git checkout main
    git pull origin main
else
    git checkout "$TARGET_VERSION"
fi

NEW_COMMIT=$(git rev-parse HEAD)
NEW_TAG=$(git describe --tags --exact-match 2>/dev/null || echo "Not on a tag")

cd "$PROJECT_ROOT"

# Commit the submodule update
git add "$SUBMODULE_PATH"
git commit -m "Update CommonDataModel submodule to $TARGET_VERSION

Previous: ${CURRENT_COMMIT:0:8} ($CURRENT_TAG)
New: ${NEW_COMMIT:0:8} ($NEW_TAG)"

echo ""
echo -e "${GREEN}✅ Submodule updated successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Update Summary:${NC}"
echo "   From: ${CURRENT_COMMIT:0:8} ($CURRENT_TAG)"
echo "   To: ${NEW_COMMIT:0:8} ($NEW_TAG)"
echo ""
echo -e "${YELLOW}⚠️  NEXT STEPS:${NC}"
echo "1. Test database creation with the updated version"
echo "2. Verify R dependencies are still compatible"
echo "3. Update documentation if needed"
echo ""
echo -e "${BLUE}💡 Test database creation:${NC}"
echo "   Rscript scripts/r_official/setup_omop_r.R --dbms sqlite --database_file test_update.db"
