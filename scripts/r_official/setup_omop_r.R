#!/usr/bin/env Rscript
# OMOP CDM Setup using Official OHDSI Method
# 
# This script uses the official OHDSI CommonDataModel R package to create
# an OMOP CDM database structure. It sources the official code from the
# git submodule to ensure we always use the authoritative implementation.
#
# Usage:
#   Rscript setup_omop_r.R --help
#   Rscript setup_omop_r.R --host localhost --database omop_cdm --user omop --schema public
#
# Prerequisites:
#   - Git submodule 'external/CommonDataModel' must be initialized
#   - R packages: DatabaseConnector, SqlRender, devtools
#   - JDBC drivers for your database (PostgreSQL, SQL Server, etc.)
#
# Author: AIO FHIR-OMOP Project
# Date: July 2025

library(optparse)

# Command line argument parsing
option_list <- list(
  make_option(c("--host"), type = "character", default = "localhost",
              help = "Database host [default: %default]"),
  make_option(c("--port"), type = "integer", default = 5432,
              help = "Database port [default: %default]"),
  make_option(c("--database"), type = "character", default = "omop_cdm",
              help = "Database name [default: %default]"),
  make_option(c("--user"), type = "character", default = "omop",
              help = "Database user [default: %default]"),
  make_option(c("--password"), type = "character", default = NULL,
              help = "Database password (if not provided, will prompt)"),
  make_option(c("--schema"), type = "character", default = "public",
              help = "Database schema [default: %default]"),
  make_option(c("--cdm-version"), type = "character", default = "5.4",
              help = "CDM version [default: %default]"),
  make_option(c("--dbms"), type = "character", default = "postgresql",
              help = "Database management system [default: %default]"),
  make_option(c("--driver-path"), type = "character", default = NULL,
              help = "Path to JDBC driver"),
  make_option(c("--dry-run"), action = "store_true", default = FALSE,
              help = "Generate DDL files only, don't execute"),
  make_option(c("--verbose"), action = "store_true", default = FALSE,
              help = "Verbose output")
)

# Parse arguments
parser <- OptionParser(option_list = option_list,
                      description = "Setup OMOP CDM using official OHDSI method")
args <- parse_args(parser)

# Check if official OHDSI code is available
check_official_code <- function() {
  cdm_path <- file.path("..", "..", "external", "CommonDataModel")
  if (!dir.exists(cdm_path)) {
    cat("❌ Error: Official OHDSI CommonDataModel not found at:", cdm_path, "\n")
    cat("Please run: git submodule update --init --recursive\n")
    quit(status = 1)
  }
  
  # Check if we can load the package from submodule
  if (!require("devtools", quietly = TRUE)) {
    cat("❌ Error: devtools package not installed\n")
    cat("Please run: install.packages('devtools')\n")
    quit(status = 1)
  }
  
  if (args$verbose) {
    cat("✅ Official OHDSI CommonDataModel found\n")
  }
  
  return(cdm_path)
}

# Install official CommonDataModel package from submodule
install_official_package <- function(cdm_path) {
  if (args$verbose) {
    cat("📦 Installing official CommonDataModel package from submodule...\n")
  }
  
  tryCatch({
    devtools::install(cdm_path, quiet = !args$verbose)
    if (args$verbose) {
      cat("✅ CommonDataModel package installed successfully\n")
    }
  }, error = function(e) {
    cat("❌ Error installing CommonDataModel package:", e$message, "\n")
    quit(status = 1)
  })
}

# Setup database connection
setup_connection <- function() {
  # Handle password
  password <- args$password
  if (is.null(password)) {
    password <- getPass::getPass("Enter database password: ")
  }
  
  # Create connection details
  connection_details <- DatabaseConnector::createConnectionDetails(
    dbms = args$dbms,
    server = paste0(args$host, "/", args$database),
    user = args$user,
    password = password,
    port = args$port,
    pathToDriver = args$driver_path
  )
  
  if (args$verbose) {
    cat("🔗 Connection configured for:", args$dbms, "at", args$host, "\n")
  }
  
  return(connection_details)
}

# Main execution function
main <- function() {
  cat("🚀 OMOP CDM Setup - Official OHDSI Method\n")
  cat("==========================================\n\n")
  
  # Step 1: Check prerequisites
  cdm_path <- check_official_code()
  
  # Step 2: Install official package
  install_official_package(cdm_path)
  
  # Step 3: Load required libraries
  if (!require("DatabaseConnector", quietly = TRUE)) {
    cat("📦 Installing DatabaseConnector...\n")
    install.packages("DatabaseConnector")
    library(DatabaseConnector)
  }
  
  if (!require("getPass", quietly = TRUE)) {
    cat("📦 Installing getPass...\n")
    install.packages("getPass")
    library(getPass)
  }
  
  library(CommonDataModel)
  
  # Step 4: Setup connection
  connection_details <- setup_connection()
  
  # Step 5: Execute DDL or generate files
  if (args$dry_run) {
    cat("📄 Generating DDL files (dry run mode)...\n")
    output_folder <- file.path("ddl_output", args$cdm_version, args$dbms)
    
    CommonDataModel::buildRelease(
      cdmVersions = args$cdm_version,
      targetDialects = args$dbms,
      outputfolder = output_folder
    )
    
    cat("✅ DDL files generated in:", output_folder, "\n")
  } else {
    cat("🏗️  Creating OMOP CDM v", args$cdm_version, " in database...\n")
    
    tryCatch({
      CommonDataModel::executeDdl(
        connectionDetails = connection_details,
        cdmVersion = args$cdm_version,
        cdmDatabaseSchema = args$schema
      )
      
      cat("✅ OMOP CDM database created successfully!\n")
      cat("📊 Database:", args$database, "\n")
      cat("🏷️  Schema:", args$schema, "\n")
      cat("📌 Version:", args$cdm_version, "\n")
      
    }, error = function(e) {
      cat("❌ Error creating database:", e$message, "\n")
      quit(status = 1)
    })
  }
  
  cat("\n🎉 Setup completed!\n")
}

# Execute main function
if (!interactive()) {
  main()
}
