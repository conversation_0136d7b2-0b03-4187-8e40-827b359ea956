#!/usr/bin/env Rscript
# Install R Dependencies for Official OHDSI Method
#
# This script installs the minimum required R packages to use the
# official OHDSI CommonDataModel method in our FHIR-OMOP project.
#
# Usage:
#   Rscript install_r_deps.R
#
# Author: AIO FHIR-OMOP Project
# Date: July 2025

cat("📦 Installing R Dependencies for Official OHDSI Method\n")
cat("=====================================================\n\n")

# Required packages for official OHDSI method
required_packages <- c(
  "devtools",        # For installing from GitHub/local
  "optparse",        # Command line argument parsing
  "getPass",         # Secure password input
  "DatabaseConnector", # OHDSI database connection
  "SqlRender",       # SQL dialect translation
  "readr",           # File reading utilities
  "jsonlite"         # JSON parsing (for updates)
)

# Function to install packages if not already installed
install_if_missing <- function(package_name) {
  if (!require(package_name, character.only = TRUE, quietly = TRUE)) {
    cat("📦 Installing", package_name, "...\n")
    install.packages(package_name, repos = "https://cran.r-project.org/")
    
    # Verify installation
    if (require(package_name, character.only = TRUE, quietly = TRUE)) {
      cat("✅", package_name, "installed successfully\n")
    } else {
      cat("❌ Failed to install", package_name, "\n")
      return(FALSE)
    }
  } else {
    cat("✅", package_name, "already installed\n")
  }
  return(TRUE)
}

# Install all required packages
success <- TRUE
for (package in required_packages) {
  if (!install_if_missing(package)) {
    success <- FALSE
  }
}

# Download JDBC drivers for PostgreSQL
cat("\n📥 Setting up JDBC drivers...\n")
tryCatch({
  library(DatabaseConnector)
  DatabaseConnector::downloadJdbcDrivers("postgresql")
  cat("✅ PostgreSQL JDBC driver downloaded\n")
}, error = function(e) {
  cat("⚠️  Warning: Could not download JDBC drivers:", e$message, "\n")
  cat("   You may need to download them manually later\n")
})

# Final status
if (success) {
  cat("\n🎉 All R dependencies installed successfully!\n")
  cat("\nNext steps:\n")
  cat("1. Initialize git submodule: git submodule update --init --recursive\n")
  cat("2. Run setup: Rscript setup_omop_r.R --help\n")
} else {
  cat("\n❌ Some packages failed to install. Please check the errors above.\n")
  quit(status = 1)
}
