# Official OHDSI R-based OMOP CDM Setup

This directory contains scripts and utilities for setting up OMOP CDM databases using the **official OHDSI R-based method**. This approach ensures maximum compatibility and standards compliance by using the canonical OHDSI implementation.

## 🎯 Quick Start

```bash
# 1. Validate your setup
./scripts/r_official/validate_setup.sh

# 2. Install R dependencies (one time)
Rscript scripts/r_official/install_r_deps.R

# 3. Create OMOP database
Rscript scripts/r_official/setup_omop_r.R --dbms sqlite --database_file data/omop.db
```

## 📁 Files

| File | Purpose |
|------|---------|
| `setup_omop_r.R` | Main script to create OMOP CDM using official method |
| `install_r_deps.R` | Install required R packages and JDBC drivers |
| `update_submodule.sh` | Helper to manually update the CommonDataModel submodule |
| `validate_setup.sh` | Validate that everything is properly configured |
| `README.md` | This file |

## 🚀 Usage Examples

### PostgreSQL Database
```bash
Rscript scripts/r_official/setup_omop_r.R \
  --dbms postgresql \
  --user omop_user \
  --password your_password \
  --server localhost \
  --database omop_cdm
```

### SQLite Database (for testing)
```bash
Rscript scripts/r_official/setup_omop_r.R \
  --dbms sqlite \
  --database_file data/omop_test.db
```

### Custom Schema
```bash
Rscript scripts/r_official/setup_omop_r.R \
  --dbms postgresql \
  --schema my_omop_schema \
  --database my_database
```

## 🔄 Manual Updates

To update the official OHDSI code:

```bash
# Check current version
./scripts/r_official/update_submodule.sh --check

# Update to latest release
./scripts/r_official/update_submodule.sh

# Update to specific version
./scripts/r_official/update_submodule.sh --version v5.4.2
```

## 🔍 Validation

Before using the scripts, validate your environment:

```bash
./scripts/r_official/validate_setup.sh
```

This checks:
- ✅ Conda environment with R
- ✅ CommonDataModel submodule
- ✅ Required R packages
- ✅ Script files

## 📚 Documentation

For detailed instructions, see: [`docs/guides/omop/database/r_official_setup.md`](../../docs/guides/omop/database/r_official_setup.md)

## 🎯 Why Use This Method?

- **✅ Official Implementation** - Uses exact OHDSI code
- **✅ Maximum Compatibility** - Guaranteed standards compliance  
- **✅ Multi-Database Support** - PostgreSQL, SQL Server, SQLite, Oracle
- **✅ Manual Control** - You decide when to update
- **✅ Minimal Dependencies** - Clean Conda integration

## 🤝 Integration with Python Workflow

This R-based setup creates the same database structure as our Python implementation. After setup:

1. **Load vocabularies** using: `scripts/load_vocabularies.py`
2. **Run ETL processes** using: `src/fhir_omop/main.py`
3. **Validate data** using Python analysis tools

The official R method and Python ETL complement each other perfectly.
