#!/bin/bash
# Validation Script for R-based Official OMOP Setup
#
# This script validates that the R environment and submodule
# are properly configured for the official OHDSI method.
#
# Usage:
#   ./scripts/r_official/validate_setup.sh

set -e

SUBMODULE_PATH="external/CommonDataModel"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 R-based Official OMOP Setup Validation${NC}"
echo "=========================================="

VALIDATION_ERRORS=0

# Check 1: Conda environment
echo -e "${BLUE}1. Checking Conda environment...${NC}"
if conda list | grep -q "r-base"; then
    R_VERSION=$(conda list | grep "r-base" | awk '{print $2}')
    echo -e "${GREEN}   ✅ R is installed (version $R_VERSION)${NC}"
else
    echo -e "${RED}   ❌ R not found in Conda environment${NC}"
    echo -e "${YELLOW}   💡 Run: conda env update -f environment.yml${NC}"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi

# Check 2: R is accessible
echo -e "${BLUE}2. Checking R accessibility...${NC}"
if command -v Rscript >/dev/null 2>&1; then
    R_PATH=$(which Rscript)
    echo -e "${GREEN}   ✅ Rscript found at: $R_PATH${NC}"
else
    echo -e "${RED}   ❌ Rscript not found in PATH${NC}"
    echo -e "${YELLOW}   💡 Ensure Conda environment is activated${NC}"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi

# Check 3: CommonDataModel submodule
echo -e "${BLUE}3. Checking CommonDataModel submodule...${NC}"
cd "$PROJECT_ROOT"
if [ -d "$SUBMODULE_PATH" ]; then
    cd "$SUBMODULE_PATH"
    if [ -f "R/executeDdl.R" ]; then
        SUBMODULE_VERSION=$(git describe --tags 2>/dev/null || git rev-parse --short HEAD)
        echo -e "${GREEN}   ✅ Submodule found (version: $SUBMODULE_VERSION)${NC}"
    else
        echo -e "${RED}   ❌ Submodule exists but R files not found${NC}"
        echo -e "${YELLOW}   💡 Run: git submodule update --init${NC}"
        VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
    fi
    cd "$PROJECT_ROOT"
else
    echo -e "${RED}   ❌ CommonDataModel submodule not found${NC}"
    echo -e "${YELLOW}   💡 Run: git submodule add https://github.com/OHDSI/CommonDataModel.git external/CommonDataModel${NC}"
    VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
fi

# Check 4: R dependencies
echo -e "${BLUE}4. Checking R package dependencies...${NC}"
if command -v Rscript >/dev/null 2>&1; then
    R_DEPS_CHECK=$(Rscript -e "
    packages <- c('optparse', 'DatabaseConnector', 'SqlRender', 'DBI')
    missing <- packages[!sapply(packages, requireNamespace, quietly=TRUE)]
    if(length(missing) > 0) {
        cat('MISSING:', paste(missing, collapse=', '))
    } else {
        cat('OK')
    }
    " 2>/dev/null || echo "ERROR")
    
    if [ "$R_DEPS_CHECK" = "OK" ]; then
        echo -e "${GREEN}   ✅ All R packages installed${NC}"
    elif [[ "$R_DEPS_CHECK" == MISSING:* ]]; then
        MISSING_PACKAGES=${R_DEPS_CHECK#MISSING: }
        echo -e "${RED}   ❌ Missing R packages: $MISSING_PACKAGES${NC}"
        echo -e "${YELLOW}   💡 Run: Rscript scripts/r_official/install_r_deps.R${NC}"
        VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
    else
        echo -e "${YELLOW}   ⚠️  Could not check R packages (R may not be working)${NC}"
        VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
    fi
fi

# Check 5: Script files
echo -e "${BLUE}5. Checking script files...${NC}"
REQUIRED_SCRIPTS=(
    "scripts/r_official/setup_omop_r.R"
    "scripts/r_official/install_r_deps.R"
    "scripts/r_official/update_submodule.sh"
)

for script in "${REQUIRED_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        echo -e "${GREEN}   ✅ $script${NC}"
    else
        echo -e "${RED}   ❌ $script missing${NC}"
        VALIDATION_ERRORS=$((VALIDATION_ERRORS + 1))
    fi
done

# Summary
echo ""
echo -e "${BLUE}📊 Validation Summary${NC}"
echo "===================="

if [ $VALIDATION_ERRORS -eq 0 ]; then
    echo -e "${GREEN}🎉 All checks passed! Your R-based official OMOP setup is ready.${NC}"
    echo ""
    echo -e "${BLUE}💡 Next steps:${NC}"
    echo "   1. Create a database: Rscript scripts/r_official/setup_omop_r.R --help"
    echo "   2. Test with SQLite: Rscript scripts/r_official/setup_omop_r.R --dbms sqlite --database_file test.db"
    echo ""
else
    echo -e "${RED}❌ $VALIDATION_ERRORS validation errors found.${NC}"
    echo -e "${YELLOW}Please fix the issues above before proceeding.${NC}"
    echo ""
    echo -e "${BLUE}🔧 Quick fix commands:${NC}"
    echo "   conda env update -f environment.yml"
    echo "   conda activate fhir-omop"
    echo "   git submodule update --init external/CommonDataModel"
    echo "   Rscript scripts/r_official/install_r_deps.R"
    echo ""
    exit 1
fi
