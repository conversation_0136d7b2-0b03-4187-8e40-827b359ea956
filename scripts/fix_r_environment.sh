#!/bin/bash
# Fix R Environment - BLAS/LAPACK Linking Issues on macOS
# This script ensures R can find the correct BLAS/LAPACK libraries

set -e

echo "🔧 Fixing R environment BLAS/LAPACK linking issues..."

# Check if we're in a conda environment
if [[ -z "$CONDA_PREFIX" ]]; then
    echo "❌ Error: No conda environment active. Please activate your environment first."
    exit 1
fi

LIB_DIR="$CONDA_PREFIX/lib"

echo "📍 Working in: $LIB_DIR"

# Check if R is installed
if [[ ! -f "$CONDA_PREFIX/bin/R" ]]; then
    echo "❌ Error: R is not installed in this environment."
    exit 1
fi

# Function to create symlink safely
create_symlink() {
    local target="$1"
    local link_name="$2"
    
    if [[ -f "$LIB_DIR/$target" ]]; then
        echo "🔗 Creating symlink: $link_name -> $target"
        rm -f "$LIB_DIR/$link_name"
        ln -s "$target" "$LIB_DIR/$link_name"
    else
        echo "⚠️  Warning: Target file $target not found"
    fi
}

# Navigate to lib directory
cd "$LIB_DIR"

echo "🔍 Checking existing OpenBLAS libraries..."
ls -la libopenblas* 2>/dev/null || echo "No OpenBLAS libraries found"

# Find the correct OpenBLAS library
OPENBLAS_LIB=""
if [[ -f "libopenblas.dylib" ]]; then
    OPENBLAS_LIB="libopenblas.dylib"
elif [[ -f "libopenblasp-r0.3.21.dylib" ]]; then
    OPENBLAS_LIB="libopenblasp-r0.3.21.dylib"
else
    echo "❌ Error: No suitable OpenBLAS library found"
    exit 1
fi

echo "✅ Using OpenBLAS library: $OPENBLAS_LIB"

# Create the necessary symlinks
create_symlink "$OPENBLAS_LIB" "libblas.3.dylib"
create_symlink "$OPENBLAS_LIB" "libblas.dylib"
create_symlink "$OPENBLAS_LIB" "liblapack.3.dylib"
create_symlink "$OPENBLAS_LIB" "liblapack.dylib"

echo "🧪 Testing R installation..."
if R --version > /dev/null 2>&1; then
    echo "✅ R is working correctly!"
    R --version | head -1
else
    echo "❌ R test failed. There may be other issues."
    exit 1
fi

echo "🎉 R environment fixed successfully!"
